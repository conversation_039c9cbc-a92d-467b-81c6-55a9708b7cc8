package device

import (
	"runtime/debug"
	"time"

	"github.com/brian-armstrong/gpio"
	"github.com/rs/zerolog/log"
)

// PinTriger 引脚触发器
type PinTrigger struct {
	// 引脚状态值
	Value uint
	// 引脚状态改变时间
	Time int64
	// 是否已触发
	Triggered bool
	// 触发时的电平
	TrigLevel uint
}

// GpioHelper 通用GPIO操作类
type GpioHelper struct {
	// 引脚状态变化处理事件
	GpioChangedHandler func(pin uint, value uint)
	// 引脚状态变化通知管道
	gpioNotify chan *gpio.WatcherNotification
	// 当前运行中断处理的协程ID
	goId int64
}

// NewGpioHelper 创建GPIO操作类
func NewGpioHelper() *GpioHelper {
	return &GpioHelper{}
}

// Init 初始化GPIO服务
func (gh *GpioHelper) Init() error {
	if gh.gpioNotify == nil {
		gh.gpioNotify = make(chan *gpio.WatcherNotification, 100)
	}
	return nil
}

// StartInterrupt 启动GPIO中断监听
func (gh *GpioHelper) StartInterrupt(gpios []uint) error {
	goId := time.Now().UnixNano()
	gh.goId = goId

	// 启用GPIO中断监听
	go gh.gpioInterrupt(gpios, goId)

	// 启用GPIO处理协程
	go gh.gpioChanged(goId)

	return nil
}

// Deinit 关闭GPIO服务
func (gh *GpioHelper) Deinit() error {
	gh.goId = 0
	close(gh.gpioNotify)
	return nil
}

// gpioChanged 引脚状态变化处理
func (gh *GpioHelper) gpioChanged(goId int64) {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("Stack", string(debug.Stack())).Msgf("GpioHelper.gpioChangedHandler 程序运行异常: %v", r)
		}
	}()

	// 读取管道数据
	for gh.goId == goId {
		notify, ok := <-gh.gpioNotify
		if !ok {
			break
		}

		// 处理引脚状态变化
		if gh.GpioChangedHandler != nil {
			gh.GpioChangedHandler(notify.Pin, notify.Value)
		}
	}
}

// gpioInterrupt GPIO中断处理，发现中断并通过管道传输
func (gh *GpioHelper) gpioInterrupt(gpios []uint, goId int64) {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("Stack", string(debug.Stack())).Msgf("GpioHelper.gpioInterrupt 程序运行异常: %v", r)
		}
	}()

	// 记录上一个引脚状态
	oldPins := make(map[uint]*PinTrigger)
	// 初始化电平检测
	inputPins := make([]gpio.Pin, len(gpios))
	for k, v := range gpios {
		inputPins[k] = gpio.NewInput(v)
	}

	// 循环等待电平变化
	for gh.goId == goId {
		// 读取间隔10毫秒
		time.Sleep(20 * time.Millisecond)

		for _, inputPin := range inputPins {
			// 读取引脚状态
			value, err := inputPin.Read()
			if err != nil {
				log.Warn().Err(err).Msg("读取引脚输入状态失败")
				continue
			}

			// 保存引脚
			if _, ok := oldPins[inputPin.Number]; !ok {
				oldPins[inputPin.Number] = &PinTrigger{
					Value:     value,
					Time:      time.Now().UnixMilli(),
					Triggered: false,
					TrigLevel: value,
				}
			} else if oldPins[inputPin.Number].Value != value {
				// 引脚状态变化，记录时间
				oldPins[inputPin.Number].Value = value
				oldPins[inputPin.Number].Time = time.Now().UnixMilli()
				oldPins[inputPin.Number].Triggered = false
			} else if !oldPins[inputPin.Number].Triggered && time.Now().UnixMilli()-oldPins[inputPin.Number].Time > 100 {
				// 未触发，且引脚状态持续50毫秒，认为非抖动
				// 若与上次触发电平不同，则上报
				if value != oldPins[inputPin.Number].TrigLevel {
					gh.gpioNotify <- &gpio.WatcherNotification{
						Pin:   inputPin.Number,
						Value: value,
					}
					// 更新上报电平
					oldPins[inputPin.Number].TrigLevel = value
				}
				oldPins[inputPin.Number].Triggered = true
			}
		}
	}
}
