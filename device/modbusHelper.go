package device

import (
	"bytes"
	"container/list"
	"encoding/binary"
	"errors"
	"pvFactoryBackend/communication"
	"pvFactoryBackend/protocal"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// ModbusHelper 实现 Modbus 协议的通讯处理
type ModbusHelper struct {
	slaveID byte

	// 通讯链路
	link communication.Communication
	// modbus 协议解析器
	modbus *protocal.Modbus

	// 数据接收队列
	receiveQueue *list.List
	// 数据接收队列锁
	receiveQueueLock *sync.RWMutex
}

// NewModbusHelper 创建 ModbusHelper 实例
func NewModbusHelper(slaveID byte) *ModbusHelper {
	helper := &ModbusHelper{
		slaveID: slaveID,
		link:    nil,
		modbus:  nil,
	}
	return helper
}

// Init 初始化 ModbusHelper
func (helper *ModbusHelper) Init(link communication.Communication) error {
	// 若已打开，先关闭
	if helper.link != nil {
		helper.link.Close()
	}
	helper.link = link
	helper.modbus = protocal.NewModbus()

	// 创建接收队列
	if helper.receiveQueue == nil {
		helper.receiveQueue = list.New()
	}
	if helper.receiveQueueLock == nil {
		helper.receiveQueueLock = new(sync.RWMutex)
	}

	// 关闭数据解析函数
	helper.link.SetOnReceive(helper.onGetFrame)

	// 执行设备连接
	if err := helper.link.Open(); err != nil {
		return err
	}

	return nil
}

// Deinit 关闭 ModbusHelper
func (helper *ModbusHelper) Deinit() error {
	if helper.link != nil {
		err := helper.link.Close()
		helper.link = nil
		return err
	}
	return nil
}

// SetAddr 设置地址码
func (helper *ModbusHelper) SetAddr(addr byte) {
	helper.slaveID = addr
}

// waitForFrame 等待接收到指定功能码的帧
// control 功能码
// timeout 超时时间，0表示一直等待
func (helper *ModbusHelper) waitForFrame(control uint8, timeout time.Duration) *protocal.Modbus {
	// 保存已接收到队列长度
	queueLen := 0
	// 记录当前时间
	startTime := time.Now()

	for timeout <= 0 || time.Since(startTime) <= timeout {
		var elem *list.Element = nil

		// 判断队列长度是否发生变化
		if queueLen == helper.receiveQueue.Len() {
			time.Sleep(time.Millisecond * 10)
			continue
		}
		queueLen = helper.receiveQueue.Len()

		// 判断接收队列的所有数据，匹配到符合条件的帧则退出循环
		helper.receiveQueueLock.RLock()
		for e := helper.receiveQueue.Front(); e != nil; e = e.Next() {
			frameRecv := e.Value.(*protocal.Modbus)
			if control == frameRecv.Control {
				elem = e
				break
			}
		}
		helper.receiveQueueLock.RUnlock()
		// 找到了匹配的帧，从队列中删除该帧并返回
		if elem != nil {
			helper.receiveQueueLock.Lock()
			helper.receiveQueue.Remove(elem)
			helper.receiveQueueLock.Unlock()
			return elem.Value.(*protocal.Modbus)
		}

		time.Sleep(time.Millisecond * 100)
	}
	return nil
}

// onGetFrame 接收到数据帧回调函数
func (helper *ModbusHelper) onGetFrame(data []byte, newoff int) int {
	// 解析数据帧
	modbusFrame := protocal.NewModbus()
	n, err := modbusFrame.Decode(data)
	if err != nil {
		log.Err(err).Msg("解析数据失败")
		return 0
	}

	// 响应读取逆变器电压
	getData := modbusFrame.GetData()
	if modbusFrame.Control == 0x04 && len(getData) == 4 &&
		getData[0] == 0xF2 && (getData[1] == 0x15 || getData[1] == 0x16 || getData[1] == 0x17) &&
		getData[2] == 0x00 {
		// 组织返回数据
		helper.modbus.SlaveID = modbusFrame.SlaveID
		helper.modbus.Control = 0x04
		vdatas := make([]byte, getData[3]*2+1)
		vdatas[0] = getData[3] * 2
		for i := 0; i < int(getData[3]); i++ {
			vdatas[i*2+1] = 0x08
			vdatas[i*2+2] = 0x98
		}
		helper.modbus.SetData(vdatas)
		helper.link.Write(helper.modbus.Encode())
		return n
	} else if modbusFrame.Control == 0x21 {
		if len(getData) == 8 && bytes.Equal(getData[2:], []byte{0x11, 0x11, 0x11, 0x11, 0x11, 0x11}) {
			return n
		}

		log.Debug().Msg("接收到地址自适应命令")
		helper.link.Write([]byte{0xFF, 0x21, 0x06, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x8C, 0xF7})
		return n
	}

	// 将数据帧存入接收队列
	helper.receiveQueueLock.Lock()
	helper.receiveQueue.PushBack(modbusFrame)
	helper.receiveQueueLock.Unlock()

	return n
}

// ReadVoltage 读取逆变器电压
func (helper *ModbusHelper) ReadVoltage() ([]float32, error) {
	// 组织返回数据
	helper.modbus.SlaveID = helper.slaveID
	helper.modbus.Control = 0x04
	helper.modbus.SetData([]byte{0xF2, 0x15, 0x00, 0x03})
	helper.link.Write(helper.modbus.Encode())

	// 等待数据
	request := helper.waitForFrame(0x04, time.Second*5)
	if request == nil {
		return nil, errors.New("分布式 <- 逆变器 数据接收超时")
	}
	data := request.GetData()
	// 判断数据长度
	if len(data) != 7 {
		return nil, errors.New("分布式 <- 逆变器 数据长度错误")
	}

	return []float32{
		float32(binary.BigEndian.Uint16(data[1:3]) / 10.0),
		float32(binary.BigEndian.Uint16(data[3:5]) / 10.0),
		float32(binary.BigEndian.Uint16(data[5:7]) / 10.0),
	}, nil
}
