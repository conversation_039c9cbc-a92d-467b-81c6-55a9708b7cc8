package device

import (
	"container/list"
	"encoding/hex"
	"errors"
	"pvFactoryBackend/communication"
	"pvFactoryBackend/protocal"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// Dlt698Helper Dlt698操作助手类
type Dlt698Helper struct {
	// 通讯地址
	addr []byte

	// 通讯链路
	link communication.Communication
	// dl/t698_45协议解析器
	dlt698_45 *protocal.Dlt698_45
	// dl/t698_apdu协议解析器
	dlt698_apdu *protocal.Dlt698_apdu

	// 数据接收队列
	receiveQueue *list.List
	// 数据接收队列锁
	receiveQueueLock *sync.RWMutex
}

// NewDlt698Helper 创建Dlt698操作助手类实例
func NewDlt698Helper() *Dlt698Helper {
	return &Dlt698Helper{
		addr:        []byte{0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA},
		link:        nil,
		dlt698_45:   nil,
		dlt698_apdu: nil,
	}
}

// Init 初始化Dlt698操作助手类
func (d *Dlt698Helper) Init(link communication.Communication) error {
	// 若已打开，先关闭
	if d.link != nil {
		d.link.Close()
	}
	d.link = link
	d.dlt698_45 = protocal.NewDlt698_45()
	d.dlt698_apdu = protocal.NewDlt698_apdu()

	// 创建接收队列
	if d.receiveQueue == nil {
		d.receiveQueue = list.New()
	}
	if d.receiveQueueLock == nil {
		d.receiveQueueLock = new(sync.RWMutex)
	}

	// 关联数据解析函数
	d.link.SetOnReceive(d.onGetFrame)

	// 执行设备连接
	if err := d.link.Open(); err != nil {
		return err
	}

	return nil
}

// Deinit 释放Dlt698操作助手类资源
func (d *Dlt698Helper) Deinit() error {
	if d.link != nil {
		err := d.link.Close()
		d.link = nil
		return err
	}
	return nil
}

// SetAddr 设置通讯地址
func (d *Dlt698Helper) SetAddr(addr string) error {
	// 解析地址
	if len(addr) < 2 {
		addr = "AA"
	} else if len(addr)%2 == 1 {
		addr += "F"
	}
	daddr, err := hex.DecodeString(addr)
	if err != nil {
		return err
	}
	// 反转 daddr 中的字节序
	for i, j := 0, len(daddr)-1; i < j; i, j = i+1, j-1 {
		daddr[i], daddr[j] = daddr[j], daddr[i]
	}
	d.addr = daddr
	return nil
}

// waitForFrame 等待接收到指定序列号的帧
// control 控制码
// operate 应用服务层操作码
// seq 序列号
// timeout 超时时间，0表示一直等待
func (d *Dlt698Helper) waitForFrame(control uint8, operate uint8, seq uint8, timeout time.Duration) *protocal.Dlt698_45 {
	// 保存已接收到队列长度
	queueLen := 0
	// 记录当前时间
	startTime := time.Now()

	for timeout <= 0 || time.Since(startTime) <= timeout {
		var elem *list.Element = nil

		// 判断队列长度是否发生变化
		if queueLen == d.receiveQueue.Len() {
			time.Sleep(time.Millisecond * 10)
			continue
		}
		queueLen = d.receiveQueue.Len()

		// 判断接收队列的所有数据，匹配到符合条件的帧则退出循环
		d.receiveQueueLock.RLock()
		for e := d.receiveQueue.Front(); e != nil; e = e.Next() {
			frameRecv := e.Value.(*protocal.Dlt698_45)
			content := frameRecv.GetContent()
			if len(content) < 3 {
				continue
			}
			if control|0x80 == frameRecv.Control && operate|0x80 == content[0] {
				if (content[0] >= 0x85 && content[0] <= 0x89 || content[0] >= 0xC5 && content[0] <= 0xC9) && content[2] == seq || content[1] == seq {
					elem = e
					break
				}
			}
		}
		d.receiveQueueLock.RUnlock()
		// 找到了匹配的帧，从队列中删除该帧并返回
		if elem != nil {
			d.receiveQueueLock.Lock()
			d.receiveQueue.Remove(elem)
			d.receiveQueueLock.Unlock()
			return elem.Value.(*protocal.Dlt698_45)
		}

		time.Sleep(time.Millisecond * 100)
	}
	return nil
}

// onGetFrame 接收到数据帧回调函数
func (d *Dlt698Helper) onGetFrame(data []byte, newoff int) int {
	// 解析数据帧
	dlt645Frame := protocal.NewDlt698_45()
	n, err := dlt645Frame.Decode(data)
	if err != nil {
		log.Err(err).Msg("解析数据失败")
		return 0
	}

	// 将数据帧存入接收队列
	d.receiveQueueLock.Lock()
	d.receiveQueue.PushBack(dlt645Frame)
	d.receiveQueueLock.Unlock()

	return n
}

// updateFileStart 开始传输文件
// 返回文件块数
func (d *Dlt698Helper) updateFileStart(filepath string) (int, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)
	data, err := d.dlt698_apdu.FileUpdateStart(filepath)
	if err != nil {
		return 0, err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return 0, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return 0, errors.New("本地 <- 分布式 数据接收超时")
	}

	return d.dlt698_apdu.GetBlockCount(), d.dlt698_apdu.FileUpdateStartResponse(frameRecv.GetContent())
}

// updateFilePart 传输文件分片
// id 文件分片序号
func (d *Dlt698Helper) updateFilePart(id int) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)
	data, err := d.dlt698_apdu.FileUpdatePart(id)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.FileUpdatePartResponse(frameRecv.GetContent())
}

// updateFileStatus 传输文件状态
// 返回int数组，若数组元素为0，表示传输成功，否则数组的内容表示传输失败的分片序号
func (d *Dlt698Helper) updateFileStatus() ([]int, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)
	data, err := d.dlt698_apdu.FileUpdateStatus()
	if err != nil {
		return nil, err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return nil, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return nil, errors.New("本地 <- 分布式 数据接收超时")
	}
	status, err := d.dlt698_apdu.FileUpdateStatusResponse(frameRecv.GetContent())
	if err != nil {
		return nil, err
	}
	// 转换传输失败的分片序号
	result := make([]int, 0, len(status)*8)
	for i := 0; i < len(status); i++ {
		for j := 0; j < 8; j++ {
			if (i*8+7-j) < d.dlt698_apdu.GetBlockCount() && status[i]&(1<<j) == 0 {
				result = append(result, i*8+7-j)
			}
		}
	}
	return result, nil
}

// UpdateFile 传输文件
func (d *Dlt698Helper) UpdateFile(filepath string) error {
	// 开始传输文件
	blockCount, err := d.updateFileStart(filepath)
	if err != nil {
		return err
	}
	// 传输文件分片
	for i := 0; i < blockCount; i++ {
		err = d.updateFilePart(i)
		if err != nil {
			log.Warn().Int("分片序号", i).Msg("传输失败")
		}
	}
	// 检查传输状态，并补充传输失败的分片，最多尝试3次
	maxretry := 3
	status, err := d.updateFileStatus()
	if err != nil {
		return err
	}
	for len(status) > 0 && maxretry > 0 {
		maxretry--
		log.Warn().Int("count", len(status)).Msg("传输失败")
		for _, id := range status {
			err = d.updateFilePart(id)
			if err != nil {
				log.Warn().Int("id", id).Msg("补传失败")
			}
		}
		status, err = d.updateFileStatus()
		if err != nil {
			return err
		}
	}
	if maxretry > 0 {
		return nil
	} else {
		return errors.New("多次补包失败，可能设备出现故障")
	}
}

// WriteAddress 写通讯地址
func (d *Dlt698Helper) WriteAddress(addr string) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 转换地址
	if len(addr)%2 == 1 {
		addr += "F"
	}
	daddr, err := hex.DecodeString(addr)
	if err != nil {
		return err
	}
	data, err := d.dlt698_apdu.WriteAddress(daddr)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteAddressResponse(frameRecv.GetContent())
}

// ReadAddress 读通讯地址
func (d *Dlt698Helper) ReadAddress(timeout time.Duration) (string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadAddress()
	if err != nil {
		return "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return "", err
	}

	// 等待接收数据
	if timeout <= 0 {
		timeout = time.Second * 5
	}
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], timeout)
	if frameRecv == nil {
		return "", errors.New("本地 <- 分布式 数据接收超时")
	}
	daddr, err := d.dlt698_apdu.ReadAddressResponse(frameRecv.GetContent())
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(daddr), nil
}

// WriteMeterId 写入电表号
func (d *Dlt698Helper) WriteMeterId(number string) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 转换地址
	if len(number)%2 == 1 {
		number = "0" + number
	}
	dnumber, err := hex.DecodeString(number)
	if err != nil {
		return err
	}
	data, err := d.dlt698_apdu.WriteMeterId(dnumber)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteMeterIdResponse(frameRecv.GetContent())
}

// ReadMeterId 读电表号
func (d *Dlt698Helper) ReadMeterId() (string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadMeterId()
	if err != nil {
		return "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return "", err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return "", errors.New("本地 <- 分布式 数据接收超时")
	}
	dnumber, err := d.dlt698_apdu.ReadMeterIdResponse(frameRecv.GetContent())
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(dnumber), nil
}

// WriteAssetCode 写入资产管理编号
func (d *Dlt698Helper) WriteAssetCode(code string) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	data, err := d.dlt698_apdu.WriteAssetCode(code)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteAssetCodeResponse(frameRecv.GetContent())
}

// ReadAssetCode 读资产管理编号
func (d *Dlt698Helper) ReadAssetCode() (string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadAssetCode()
	if err != nil {
		return "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return "", err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return "", errors.New("本地 <- 分布式 数据接收超时")
	}
	dcode, err := d.dlt698_apdu.ReadAssetCodeResponse(frameRecv.GetContent())
	if err != nil {
		return "", err
	}
	return dcode, nil
}

// WriteBoardInfo 写入板子版本信息
func (d *Dlt698Helper) WriteBoardInfo(hardVersion, hardDate, vendorCode, vendorExt string) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	data, err := d.dlt698_apdu.WriteBoardInfo(hardVersion, hardDate, vendorCode, vendorExt)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteBoardInfoResponse(frameRecv.GetContent())
}

// ReadBoardInfo 读板子版本信息
// 返回硬件版本号，硬件日期，软件版本，软件日期，厂商代码，厂商扩展信息
func (d *Dlt698Helper) ReadBoardInfo() (string, string, string, string, string, string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadBoardInfo()
	if err != nil {
		return "", "", "", "", "", "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return "", "", "", "", "", "", err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return "", "", "", "", "", "", errors.New("本地 <- 分布式 数据接收超时")
	}
	hardVersion, hardDate, softVersion, softDate, vendorCode, vendorExt, err := d.dlt698_apdu.ReadBoardInfoResponse(frameRecv.GetContent())
	if err != nil {
		return "", "", "", "", "", "", err
	}
	return hardVersion, hardDate, softVersion, softDate, vendorCode, vendorExt, nil
}

// WriteHardVersion 写入硬件版本号
func (d *Dlt698Helper) WriteHardVersion(version string) error {
	// 读取板子版本信息
	_, hardDate, _, _, vendorCode, vendorExt, err := d.ReadBoardInfo()
	if err != nil {
		return err
	}
	// 写入板子版本信息
	return d.WriteBoardInfo(version, hardDate, vendorCode, vendorExt)
}

// WriteHardDate 写入硬件日期
func (d *Dlt698Helper) WriteHardDate(date string) error {
	// 读取板子版本信息
	hardVersion, _, _, _, vendorCode, vendorExt, err := d.ReadBoardInfo()
	if err != nil {
		return err
	}
	// 写入板子版本信息
	return d.WriteBoardInfo(hardVersion, date, vendorCode, vendorExt)
}

// WriteVendorCode 写入厂商代码
func (d *Dlt698Helper) WriteVendorCode(code string) error {
	// 读取板子版本信息
	hardVersion, hardDate, _, _, _, vendorExt, err := d.ReadBoardInfo()
	if err != nil {
		return err
	}
	// 写入板子版本信息
	return d.WriteBoardInfo(hardVersion, hardDate, code, vendorExt)
}

// WriteVendorExt 写入厂商扩展信息
func (d *Dlt698Helper) WriteVendorExt(ext string) error {
	// 读取板子版本信息
	hardVersion, hardDate, _, _, vendorCode, _, err := d.ReadBoardInfo()
	if err != nil {
		return err
	}
	// 写入板子版本信息
	return d.WriteBoardInfo(hardVersion, hardDate, vendorCode, ext)
}

// WriteProduceDate 写入生产日期
func (d *Dlt698Helper) WriteProduceDate(date *time.Time) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	data, err := d.dlt698_apdu.WriteProduceDate(date)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteProduceDateResponse(frameRecv.GetContent())
}

// ReadProduceDate 读出生产日期
func (d *Dlt698Helper) ReadProduceDate() (*time.Time, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadProduceDate()
	if err != nil {
		return nil, err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return nil, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return nil, errors.New("本地 <- 分布式 数据接收超时")
	}
	date, err := d.dlt698_apdu.ReadProduceDateResponse(frameRecv.GetContent())
	if err != nil {
		return nil, err
	}
	return date, nil
}

// WriteSafeMode 写入安全模式，是否开机自动启用安全模式
func (d *Dlt698Helper) WriteSafeMode(enable bool) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	data, err := d.dlt698_apdu.WriteSafeMode(enable)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.WriteSafeModeResponse(frameRecv.GetContent())
}

// ReadSafeMode 读出安全模式
func (d *Dlt698Helper) ReadSafeMode() (bool, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadSafeMode()
	if err != nil {
		return false, err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return false, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return false, errors.New("本地 <- 分布式 数据接收超时")
	}
	enable, err := d.dlt698_apdu.ReadSafeModeResponse(frameRecv.GetContent())
	if err != nil {
		return false, err
	}
	return enable, nil
}

// 设置指示灯状态
// status: true 表示启用，false 表示停用
func (d *Dlt698Helper) SetLedStatus(status bool) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	data, err := d.dlt698_apdu.SetLedStatus(status)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)

	// 发送数据
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.SetLedStatusResponse(frameRecv.GetContent())
}

// ReadEsamSn 读取ESAM序列号
func (d *Dlt698Helper) ReadEsamSn() (string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadEsamSn()
	if err != nil {
		return "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return "", err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return "", errors.New("本地 <- 分布式 数据接收超时")
	}
	sn, err := d.dlt698_apdu.ReadEsamSnResponse(frameRecv.GetContent())
	if err != nil {
		return "", err
	}
	return sn, nil
}

// ReadVoltage 读取电压
// logicAddress: 逻辑地址，0x02:RJ45-1，0x03:RJ45-2，0x04:RS485-2
func (d *Dlt698Helper) ReadVoltage(logicAddress uint8) ([]float32, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, logicAddress, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadVoltage()
	if err != nil {
		return nil, err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return nil, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*60)
	if frameRecv == nil {
		return nil, errors.New("本地 <- 分布式 数据接收超时")
	}
	voltage, err := d.dlt698_apdu.ReadVoltageResponse(frameRecv.GetContent())
	if err != nil {
		return nil, err
	}
	return voltage, nil
}

// MeterCalibration 电表校准
func (d *Dlt698Helper) MeterCalibration(voltage float32) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.MeterCalibration(voltage)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.MeterCalibrationResponse(frameRecv.GetContent())
}

// 读取当前时间
func (d *Dlt698Helper) ReadTime() (*time.Time, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadTime()
	if err != nil {
		return nil, err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return nil, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return nil, errors.New("本地 <- 分布式 数据接收超时")
	}
	t, err := d.dlt698_apdu.ReadTimeResponse(frameRecv.GetContent())
	if err != nil {
		return nil, err
	}
	return t, nil
}

// SetTime 设置当前时间
func (d *Dlt698Helper) SetTime(t time.Time) error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.SetTime(t)
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.SetTimeResponse(frameRecv.GetContent())
}

// FormatFS 文件系统格式化
func (d *Dlt698Helper) FormatFS() error {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.FormatFS()
	if err != nil {
		return err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return errors.New("本地 <- 分布式 数据接收超时")
	}
	return d.dlt698_apdu.FormatFSResponse(frameRecv.GetContent())
}

// ReadStaStatus 读取STA通讯状态
func (d *Dlt698Helper) ReadStaStatus() (bool, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadStaStatus()
	if err != nil {
		return false, err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return false, err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return false, errors.New("本地 <- 分布式 数据接收超时")
	}
	status, err := d.dlt698_apdu.ReadStaStatusResponse(frameRecv.GetContent())
	if err != nil {
		return false, err
	}
	return status, nil
}

// 读取蓝牙状态及MAC地址
func (d *Dlt698Helper) ReadBtStatus() (bool, string, error) {
	// 制作发送请求
	d.dlt698_45.Control = 0x43
	d.dlt698_45.SetAddress(d.addr, 0x00, 0x00)

	// 发送数据
	data, err := d.dlt698_apdu.ReadBtStatus()
	if err != nil {
		return false, "", err
	}
	d.dlt698_45.SetContent(data)
	_, err = d.link.Write(d.dlt698_45.Encode())
	if err != nil {
		return false, "", err
	}

	// 等待接收数据
	frameRecv := d.waitForFrame(d.dlt698_45.Control, data[0], data[2], time.Second*5)
	if frameRecv == nil {
		return false, "", errors.New("本地 <- 分布式 数据接收超时")
	}
	status, mac, err := d.dlt698_apdu.ReadBtStatusResponse(frameRecv.GetContent())
	if err != nil {
		return false, "", err
	}
	return status, mac, nil
}
