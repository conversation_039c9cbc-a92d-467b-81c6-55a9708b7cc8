package server

import (
	"encoding/json"
	"net/http"
	"pvFactoryBackend/base"
	"pvFactoryBackend/model"
	"pvFactoryBackend/model/table"
	"runtime/debug"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/rs/zerolog/log"
)

// EventMessage 与客户端通讯消息结构
type EventMessage struct {
	// Conn 客户端连接
	Conn *websocket.Conn
	// MsgType 消息类型
	MsgType int
	// Message 消息内容
	Message gin.H
}

// Event 保存了客户端列表，并向客户端推送消息
type Event struct {
	*websocket.Upgrader

	// Send 客户端消息发送通道
	Send chan *EventMessage
	// Receive 客户端消息接收通道
	Receive chan *EventMessage
	// 配置信息
	config *base.Config

	// 记录连接到ws服务的客户端
	clients map[*websocket.Conn]bool
	// 记录当前已启动的测试方案
	PlanRunning []*TestPlan
}

// NewEvent 创建一个新的事件实例
func NewEvent(config *base.Config) *Event {
	event := &Event{
		Upgrader: &websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		Send:    make(chan *EventMessage, 10),
		Receive: make(chan *EventMessage, 10),
		config:  config,
		clients: make(map[*websocket.Conn]bool),
	}

	return event
}

// Init 初始化测试服务
func (e *Event) Init() error {
	// 启用工装测试服务
	for i := 0; i < len(e.config.Comm); i++ {
		// 启动测试服务
		testPlan := NewTestPlan(&e.config.Comm[i])
		err := testPlan.Init(e.Send, e.config)
		if err != nil {
			return err
		}
		// 记录已启用的测试方案
		e.PlanRunning = append(e.PlanRunning, testPlan)
	}
	return nil
}

// Listen 监听来自客户端的消息，并处理
func (e *Event) Listen() {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("Stack", string(debug.Stack())).Msgf("Event.ListenMessage 程序运行异常: %v", r)
		}
	}()

	for {
		select {
		// 接收到消息
		case message, ok := <-e.Receive:
			if !ok {
				log.Error().Msg("Receive channel closed")
				return
			}
			if message != nil && message.Message != nil {
				stype, ok := message.Message["type"].(string)
				if !ok {
					log.Warn().Msg("消息中不包含type字段，忽略该消息")
					continue
				}
				switch stype {
				case "devicePlan": // 设置测试方案
					e.setDevicePlan(message)
				case "boardCode": // 设置板级测试条码
					e.setBarcode(message)
				}
			} else {
				log.Warn().Int("消息类型", message.MsgType).Msg("收到空消息")
			}

		// 发送消息
		case message, ok := <-e.Send:
			if !ok {
				log.Error().Msg("Send channel closed")
				return
			}

			log.Debug().Int("消息类型", message.MsgType).Msg("发送消息")
			err := message.Conn.WriteJSON(message.Message)
			if err != nil {
				log.Err(err).Msg("WriteJSON failed")
				message.Conn.Close()
			}
		}
	}
}

// Echo 处理HTTP请求，接收消息并放到消息通道中
func (e *Event) Echo(c *gin.Context) {
	// 创建客户端连接
	ws, err := e.Upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Warn().Err(err).Msg("Upgrade websocket failed")
		return
	}

	deviceIds := make([]int, 0, len(e.config.Comm))
	for i := 0; i < len(e.config.Comm); i++ {
		deviceIds = append(deviceIds, e.config.Comm[i].WorkstationID)
	}

	// 限制客户端连接数
	if len(e.clients) >= 1 {
		// 发送客户端连接数已达上限消息
		e.Send <- &EventMessage{
			Conn:    ws,
			MsgType: 20,
			Message: gin.H{
				"type":       "deviceInfo",
				"name":       e.config.DeviceName,
				"devices":    deviceIds,
				"version":    base.VERSION,
				"error_code": 15,
				"message":    "客户端连接数已达上限",
			},
		}
		time.Sleep(1 * time.Second)

		log.Error().Msg("客户端连接数已达上限")
		ws.Close()
		return
	}

	// 记录客户端连接
	e.clients[ws] = true
	defer func() {
		log.Debug().Msg("客户端连接断开")

		delete(e.clients, ws)
		ws.Close()
	}()

	// 发送初始化消息
	e.Send <- &EventMessage{
		Conn:    ws,
		MsgType: 20,
		Message: gin.H{
			"type":    "deviceInfo",
			"name":    e.config.DeviceName,
			"devices": deviceIds,
			"version": base.VERSION,
		},
	}

	// 监听客户端消息
	for {
		msg := make(map[string]interface{})
		err := ws.ReadJSON(&msg)
		if err != nil {
			log.Warn().Err(err).Msg("ReadJSON failed")
			if _, ok := err.(*json.SyntaxError); ok {
				continue
			}
			break
		}
		log.Debug().Fields(msg).Msg("收到消息")

		// 保存接收消息到通道，等待处理
		e.Receive <- &EventMessage{
			Conn:    ws,
			MsgType: 20,
			Message: msg,
		}
	}
}

// 判断输入的字段，并解析数值型
// message 接收到的消息
// name 字段名称
// 返回值：字段值，错误信息
func parseFieldFloat64(message *EventMessage, name string) (float64, gin.H) {
	// 获取指定字段
	vname, ok := message.Message[name]
	if !ok {
		log.Warn().Msg("消息中不包含 " + name + " 字段，忽略该消息")
		return 0, gin.H{
			"type":       message.Message["type"],
			"error_code": 11,
			"message":    "未找到 " + name + " 字段",
		}
	}
	// 读取指定类型数据
	wname, ok := vname.(float64)
	if !ok {
		log.Warn().Msg(name + " 字段类型错误，忽略该消息")
		return 0, gin.H{
			"type":       message.Message["type"],
			"error_code": 12,
			"message":    name + " 字段类型错误",
		}
	}
	return wname, nil
}

// 判断输入的字段，并解析字符串
// message 接收到的消息
// name 字段名称
// 返回值：字段值，错误信息
func parseFieldStr(message *EventMessage, name string) (string, gin.H) {
	// 获取指定字段
	vname, ok := message.Message[name]
	if !ok {
		log.Warn().Msg("消息中不包含 " + name + " 字段，忽略该消息")
		return "", gin.H{
			"type":       message.Message["type"],
			"error_code": 11,
			"message":    "未找到 " + name + " 字段",
		}
	}
	// 读取指定类型数据
	wname, ok := vname.(string)
	if !ok {
		log.Warn().Msg(name + " 字段类型错误，忽略该消息")
		return "", gin.H{
			"type":       message.Message["type"],
			"error_code": 12,
			"message":    name + " 字段类型错误",
		}
	}
	return wname, nil
}

// 判断输入的字段，并解析字符串数组
// message 接收到的消息
// name 字段名称
// 返回值：字段值，错误信息
func parseFieldStrArray(message *EventMessage, name string) ([]string, gin.H) {
	// 获取指定字段
	vname, ok := message.Message[name]
	if !ok {
		log.Warn().Msg("消息中不包含 " + name + " 字段，忽略该消息")
		return nil, gin.H{
			"type":       message.Message["type"],
			"error_code": 11,
			"message":    "未找到 " + name + " 字段",
		}
	}
	// 读取指定类型数据
	wname, ok := vname.([]interface{})
	if !ok {
		log.Warn().Msg(name + " 字段类型错误，忽略该消息")
		return nil, gin.H{
			"type":       message.Message["type"],
			"error_code": 12,
			"message":    name + " 字段类型错误",
		}
	}
	// 转换内容类型
	items := make([]string, 0)
	for i := 0; i < len(wname); i++ {
		if item, ok := wname[i].(string); ok {
			items = append(items, item)
		}
	}
	return items, nil
}

// setDevicePlan 设置测试方案
func (e *Event) setDevicePlan(message *EventMessage) {
	retMsg := &EventMessage{
		Conn:    message.Conn,
		MsgType: 20,
	}

	// 判断当前是否正在测试
	for i := 0; i < len(e.PlanRunning); i++ {
		if e.PlanRunning[i].IsRunning() {
			retMsg.Message = gin.H{
				"type":       message.Message["type"],
				"error_code": 16,
				"message":    "当前正在测试中，请等待测试结束",
			}
			log.Warn().Msg("当前正在测试中，请等待测试结束")
			e.Send <- retMsg
			return
		}
	}

	var planType string
	var planItems []string

	// 获取测试方案类型
	planType, retMsg.Message = parseFieldStr(message, "planType")
	if retMsg.Message != nil {
		e.Send <- retMsg
		return
	}
	// 判断测试方案类型是否合法
	if planType != table.TypeMainStr && planType != table.TypeWholeStr && planType != table.TypeRewholeStr {
		retMsg.Message = gin.H{
			"type":       message.Message["type"],
			"error_code": 13,
			"message":    "未知的测试方案类型",
		}
		log.Warn().Msg("未知的测试方案类型，忽略该消息")
		e.Send <- retMsg
		return
	}

	// 获取测试方案内容
	planItems, retMsg.Message = parseFieldStrArray(message, "items")
	if retMsg.Message != nil {
		e.Send <- retMsg
		return
	}

	// 启用工装测试服务
	for i := 0; i < len(e.PlanRunning); i++ {
		e.PlanRunning[i].ResetPlan(message.Conn, planType, planItems)
	}
	retMsg.Message = gin.H{
		"type":       message.Message["type"],
		"error_code": 0,
		"message":    "成功",
	}
	e.Send <- retMsg
}

// setBarcode 设置测试条码
func (e *Event) setBarcode(message *EventMessage) {
	retMsg := &EventMessage{
		Conn:    message.Conn,
		MsgType: 20,
	}

	var planType string
	var device float64
	var barcode string

	// 获取测试方案类型
	planType, retMsg.Message = parseFieldStr(message, "planType")
	if retMsg.Message != nil {
		e.Send <- retMsg
		return
	}
	// 判断方案类型是否有效
	if planType != table.TypeMainStr && planType != table.TypeWholeStr && planType != table.TypeRewholeStr {
		retMsg.Message = gin.H{
			"type":       message.Message["type"],
			"error_code": 13,
			"message":    "测试方案类型错误",
		}
		log.Warn().Msg("测试方案类型错误，忽略该消息")
		e.Send <- retMsg
		return
	}

	// 获取测试模块序号
	device, retMsg.Message = parseFieldFloat64(message, "device")
	if retMsg.Message != nil {
		e.Send <- retMsg
		return
	}
	// 获取测试模块主板条码
	barcode, retMsg.Message = parseFieldStr(message, "barcode")
	if retMsg.Message != nil {
		e.Send <- retMsg
		return
	}

	// 根据不同的测试方案类型，限制条码长度
	if planType == table.TypeMainStr {
		if len(barcode) < 4 || len(barcode) > 20 {
			retMsg.Message = gin.H{
				"type":       message.Message["type"],
				"error_code": 15,
				"message":    "主板条码长度错误，应为4-20位",
			}
			log.Warn().Msg("主板条码长度错误，忽略该消息")
			e.Send <- retMsg
			return
		}
	} else if planType == table.TypeWholeStr || planType == table.TypeRewholeStr {
		if len(barcode) != 22 {
			retMsg.Message = gin.H{
				"type":       message.Message["type"],
				"error_code": 15,
				"message":    "整机条码长度错误，应为22位",
			}
			log.Warn().Msg("整机条码长度错误，忽略该消息")
			e.Send <- retMsg
			return
		}
	}

	// 在配置中查找指定的模块序号
	for i := 0; i < len(e.config.Comm); i++ {
		if e.config.Comm[i].WorkstationID == int(device) && e.config.Comm[i].TestType == planType {
			// 测试方案为整机测试时，判断条码是否已组装
			if planType == table.TypeWholeStr || planType == table.TypeRewholeStr {
				result, err := model.DefaultDB.SelectBarcodeAssembled(planType, barcode)
				if err != nil {
					retMsg.Message = gin.H{
						"type":       message.Message["type"],
						"error_code": 18,
						"message":    "查询条码状态失败: " + err.Error(),
					}
					log.Warn().Err(err).Msg("查询条码状态失败")
					e.Send <- retMsg
					return
				}
				var detail string
				switch result {
				case 1:
					detail = "未组装"
				case 2:
					detail = "已组装"
				default:
					detail = "未知状态"
				}

				if result != 2 {
					retMsg.Message = gin.H{
						"type":       message.Message["type"],
						"error_code": 19,
						"message":    "条码[" + barcode + "]状态为[" + detail + "]，请解决后后测试",
					}
					log.Warn().Str("barcode", barcode).Str("detail", detail).Msg("条码状态异常，请解决后测试")
					e.Send <- retMsg
					return
				}
			}

			e.config.Comm[i].Barcode = barcode
			retMsg.Message = gin.H{
				"type":       message.Message["type"],
				"error_code": 0,
				"message":    "成功",
			}
			e.Send <- retMsg
			return
		}
	}

	// 未找到指定的模块序号
	retMsg.Message = gin.H{
		"type":       message.Message["type"],
		"error_code": 14,
		"message":    "未知的工装位或未设置测试方案",
	}
	e.Send <- retMsg
}
