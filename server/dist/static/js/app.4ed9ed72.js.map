{"version": 3, "file": "static/js/app.4ed9ed72.js", "mappings": "yCAAA,IAAIA,EAAM,CACT,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,KACX,aAAc,KACd,UAAW,MACX,aAAc,MACd,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,QAAS,MACT,WAAY,MACZ,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,gBAAiB,MACjB,aAAc,MACd,gBAAiB,MACjB,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,UAAW,MACX,aAAc,MACd,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,MACX,aAAc,MACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,WAAY,MACZ,cAAe,MACf,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,IACR,UAAW,IACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,UAAW,MACX,OAAQ,MACR,UAAW,MACX,WAAY,MACZ,cAAe,MACf,UAAW,MACX,aAAc,MACd,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,MACX,aAAc,MACd,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,KACR,UAAW,KACX,OAAQ,MACR,YAAa,MACb,eAAgB,MAChB,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,QAAS,MACT,WAAY,MACZ,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,UAAW,KACX,aAAc,KACd,QAAS,MACT,WAAY,MACZ,OAAQ,MACR,UAAW,MACX,QAAS,MACT,WAAY,MACZ,QAAS,MACT,aAAc,MACd,gBAAiB,MACjB,WAAY,MACZ,UAAW,MACX,aAAc,MACd,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,UAAW,MACX,OAAQ,MACR,YAAa,MACb,eAAgB,MAChB,UAAW,MACX,OAAQ,MACR,UAAW,MACX,aAAc,MACd,gBAAiB,MACjB,OAAQ,MACR,UAAW,MACX,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,MACd,UAAW,MACX,aAAc,OAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOG,EAAoBF,EAC5B,CACA,SAASC,EAAsBF,GAC9B,IAAIG,EAAoBC,EAAEN,EAAKE,GAAM,CACpC,IAAIK,EAAI,IAAIC,MAAM,uBAAyBN,EAAM,KAEjD,MADAK,EAAEE,KAAO,mBACHF,CACP,CACA,OAAOP,EAAIE,EACZ,CACAD,EAAeS,KAAO,WACrB,OAAOC,OAAOD,KAAKV,EACpB,EACAC,EAAeW,QAAUR,EACzBS,EAAOC,QAAUb,EACjBA,EAAeE,GAAK,K,qFCvShBY,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,EACrH,EACIG,EAAkB,GCMtB,GACAC,KAAA,MACAC,WAAA,CACA,EACAC,OAAAA,GACA,KAAAC,aACA,EACAC,QAAA,CACAD,WAAAA,GACAE,SAAAC,MAAA,gBACA,IClByO,I,WCQrOC,GAAY,OACd,EACAd,EACAM,GACA,EACA,KACA,KACA,MAIF,EAAeQ,EAAiB,Q,iCCnB5Bd,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACY,YAAY,gBAAgB,CAACZ,EAAG,kBAAkB,CAACY,YAAY,UAAU,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACd,EAAIe,GAAG,oBAAoBb,EAAG,MAAM,CAACY,YAAY,gBAAgB,CAACZ,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAIiB,YAAY,CAACf,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,iBAAiBJ,EAAIe,GAAG,WAAW,IAAI,KAAKb,EAAG,WAAW,CAACA,EAAG,iBAAiB,CAACY,YAAY,UAAUV,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,SAAS,CAACgB,YAAY,CAAC,OAAS,QAAQd,MAAM,CAAC,KAAO,WAAWJ,EAAImB,GAAInB,EAAIoB,UAAU,SAASC,EAAKC,GAAO,OAAOpB,EAAG,cAAc,CAACqB,IAAID,EAAQ,GAAG,CAACpB,EAAG,cAAc,CAACE,MAAM,CAAC,GAAKiB,EAAKG,QAAQ,CAACtB,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOiB,EAAKI,QAAQvB,EAAG,OAAO,CAACF,EAAIe,GAAGf,EAAI0B,GAAGL,EAAKf,UAAU,IAAI,EAAE,IAAG,IAAI,GAAGJ,EAAG,mBAAmB,CAACY,YAAY,WAAW,CAACZ,EAAG,MAAM,CAACY,YAAY,mBAAmB,CAACZ,EAAG,gBAAgB,MAAM,GAAGA,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI2B,aAAa,OAAS,MAAMX,GAAG,CAAC,GAAKhB,EAAI4B,SAAS,OAAS5B,EAAI6B,eAAe,CAAC3B,EAAG,MAAM,CAACY,YAAY,aAAa,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAACZ,EAAG,MAAM,CAACY,YAAY,YAAY,CAACZ,EAAG,OAAO,CAACY,YAAY,SAAS,CAACd,EAAIe,GAAG,WAAWb,EAAG,OAAO,CAACY,YAAY,SAAS,CAACd,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAI8B,kBAAkB5B,EAAG,MAAM,CAACY,YAAY,YAAY,CAACZ,EAAG,OAAO,CAACY,YAAY,SAAS,CAACd,EAAIe,GAAG,WAAWb,EAAG,OAAO,CAACY,YAAY,SAAS,CAACd,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAI+B,4BAA4B,EAC73C,EACI1B,EAAkB,G,4DCEtB,MAAM2B,EAAcC,OAAOC,SAASC,MAI7BC,EAAIC,GAAQL,EAAYM,MAAM,KAC/BC,EAAwB,UAAUH,GAAM,eAAeC,GAAQ,SAI/DG,EAAUC,EAAAA,EAAMC,OAAO,CACzBC,QAASC,KAAKC,MAAMC,aAAaC,QAAQ,oBAAsBR,EAE/DS,QAAS,MAIbR,EAAQS,aAAaC,QAAQC,KACzBC,GAEWA,IAEXC,IAEIC,QAAQC,IAAIF,GACZG,QAAQC,OAAOJ,EAAM,IAK7Bb,EAAQS,aAAaS,SAASP,KAC1BO,GAEWA,EAASC,OAEpBN,IAII,GADAC,QAAQC,IAAI,cAAeF,QACPO,IAAjBP,EAAMK,SAAqB,CAC1B,GAA6B,MAA1BL,EAAMK,SAASG,OAAe,CAC7B,MAAMC,EAAeT,EAAMK,SAASC,KAAKI,QACzCC,EAAAA,EAAaX,MAAM,CACfU,QAAS,OACTE,YAAaH,EACbI,SAAU,GAGlB,CACA,GAA6B,MAA1Bb,EAAMK,SAASG,OAAe,CAC7B,MAAMC,EAAeT,EAAMK,SAASC,KAAKI,QACzCC,EAAAA,EAAaX,MAAM,CACfU,QAAS,OACTE,YAAaH,EACbI,SAAU,GAGlB,CACJ,CAEA,OAAOV,QAAQ5D,QAAQ,CAAE+D,KAAM,IAAK,IAI5C,QC9DA,MAAM3B,EAAcC,OAAOC,SAASC,MAI7BC,EAAIC,GAAQL,EAAYM,MAAM,KAC/BC,EAAwB,UAAUH,GAAM,eAAeC,GAAQ,SAG9D,SAAS8B,IACZ,OAAO3B,EAAQ,CACX4B,IAAI,YACJC,OAAQ,OAEhB,CAIO,SAASC,EAAUC,EAAQC,EAAOC,EAAMC,GAC3C,OAAOlC,EAAQ,CACX4B,IAAI,aACJC,OAAQ,MACRM,OAAQ,CACJJ,QAAQA,EACRC,OAAOA,EACPC,MAAMA,EACNC,KAAKA,IAGjB,CAEO,SAASE,EAAQL,EAAQC,EAAOC,GACnC,OAAOjC,EAAQ,CACX4B,IAAI,aACJC,OAAQ,OACRV,KAAM,CACFY,QAAQA,EACRC,OAAOA,EACPC,MAAMA,IAGlB,CAGO,SAASI,EAAWN,EAAQC,GAC/B,OAAOhC,EAAQ,CACX4B,IAAI,aACJC,OAAQ,MACRV,KAAM,CACFY,QAAQA,EACRC,OAAOA,IAGnB,CAKO,SAASM,EAAWP,EAAQQ,GAC/B,OAAOvC,EAAQ,CACX4B,IAAI,eACJC,OAAQ,MACRM,OAAQ,CACJI,KAAKA,EACLR,QAAQA,IAGpB,CAEO,SAASS,EAAYC,EAAaC,EAAaC,EAAaT,EAAKD,GACpE,OAAOjC,EAAQ,CACX4B,IAAI,gBACJC,OAAQ,MACRM,OAAQ,CACJM,aAAaA,EACbC,aAAaA,EACbC,aAAaA,EACbT,KAAKA,EACLD,MAAMA,IAGlB,CAGO,SAASW,EAAUH,EAAaC,EAAaC,GAChD,OAAO3C,EAAQ,CACX4B,IAAI,gBACJC,OAAQ,OACRV,KAAM,CACFsB,aAAaA,EACbC,aAAaA,EACbC,aAAaA,IAGzB,CAGO,SAASE,EAAaJ,EAAaC,EAAaC,GACnD,OAAO3C,EAAQ,CACX4B,IAAI,gBACJC,OAAQ,MACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAM,CACF,aAAesB,EACf,aAAeC,EACf,aAAeC,IAG3B,CAEO,SAASI,EAAa5B,GACzB,OAAOnB,EAAQ,CACX4B,IAAK,gBACLC,OAAQ,SACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAMA,GAEd,CAIO,SAAS6B,EAAYC,EAAQlB,EAAQmB,EAAMC,EAAYC,EAASC,EAAYC,EAAWC,EAAUrB,EAAKD,GACzG,OAAOjC,EAAQ,CACX4B,IAAK,YACLC,OAAO,MACPM,OAAQ,CACJJ,QAAQA,EACRkB,QAAQA,EACRC,MAAMA,EACNC,YAAYA,EACZC,SAASA,EACTC,YAAYA,EACZC,WAAWA,EACXC,UAAUA,EACVrB,KAAKA,EACLD,MAAMA,IAGlB,CAEO,SAASuB,EAAUzB,EAAQkB,EAAQC,EAAMC,EAAYC,EAASC,EAAYC,EAAWC,EAAUE,EAAKxB,GACvG,OAAOjC,EAAQ,CACX4B,IAAI,YACJC,OAAQ,OACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAM,CACF,QAAUY,EACV,QAAUkB,EACV,MAAQC,EACR,YAAcC,EACd,SAAWC,EACX,YAAcC,EACd,WAAaC,EACb,UAAYC,EACZ,KAAOE,EACP,MAAQxB,IAGpB,CAEO,SAASyB,EAAa3B,EAAQkB,EAAQC,EAAMC,EAAYC,EAASC,EAAYC,EAAWC,EAAUE,GACrG,OAAOzD,EAAQ,CACX4B,IAAI,YACJC,OAAQ,MACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAM,CACF,QAAUY,EACV,QAAUkB,EACV,MAAQC,EACR,YAAcC,EACd,SAAWC,EACX,YAAcC,EACd,WAAaC,EACb,UAAYC,EACZ,KAAOE,IAGnB,CAIO,SAASE,EAAWpB,EAAKR,EAAQ6B,EAAMC,EAAIxC,EAAOyC,EAAS5B,EAAKD,GACnE,OAAOjC,EAAQ,CACX4B,IAAK,cACLC,OAAO,MACPM,OAAQ,CACJI,KAAKA,EACLR,QAAQA,EACR6B,MAAMA,EACNC,IAAIA,EACJxC,OAAOA,EACPyC,SAASA,EACT5B,KAAKA,EACLD,MAAMA,IAGlB,CAEO8B,eAAeC,EAAWC,EAAUlC,EAAS6B,EAAOC,EAAKxC,EAAQa,EAAMD,GAC1E,IACI,MAAMiC,EAAc,GAAG9D,KAAKC,MAAMC,aAAaC,QAAQ,oBAAsBR,yBAA6CkE,aAAoBlC,WAAiB6B,SAAaC,YAAcxC,wBAA6Ba,WAAcD,IAC/Nf,QAAiBjB,EAAAA,EAAAA,GAAM,CACzB4B,OAAQ,MACRD,IAAKsC,EACLC,aAAc,SAEZC,EAAO,IAAIC,KAAK,CAACnD,EAASC,MAAO,CAAEoB,KAAMrB,EAAS4B,QAAQ,kBAC1DwB,EAAOnG,SAASoG,cAAc,KAC9BC,EAAOC,IAAIC,gBAAgBN,GAEjCE,EAAKE,KAAOA,EACZF,EAAKR,SAAW,qBAChB3F,SAASwG,KAAKC,YAAYN,GAC1BA,EAAKO,QACL1G,SAASwG,KAAKG,YAAYR,GAC1BG,IAAIM,gBAAgBP,EACxB,CAAE,MAAO3D,GACLC,QAAQD,MAAMA,EAClB,CACJ,CAOO,SAASmE,IACZ,OAAOhF,EAAQ,CACX4B,IAAK,oBACLC,OAAO,OAEf,CAGO,SAASoD,EAAarD,EAAIsD,GAC7B,MAAMC,EAAWvD,EAAIpF,KAAIyG,IACdhD,EAAAA,EAAAA,GAAM,CACT2B,IAAK,UAAUqB,qBACfpB,OAAQ,MACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAM,CACF,aAAe+D,OAK3B,OAAOlE,QAAQoE,IAAID,EACvB,CAGO,SAASE,IACZ,OAAOrF,EAAQ,CACX4B,IAAI,wBACJC,OAAQ,OAEhB,CAKO,SAASyD,EAAWvD,GACvB,OAAO/B,EAAQ,CACX4B,IAAK,gBACLC,OAAO,MACPM,OAAQ,CACJJ,QAAQA,IAGpB,CAEO,SAASwD,EAAYxD,EAASyD,GACjC,OAAOxF,EAAQ,CACX4B,IAAK,gBACLC,OAAQ,MACRiB,QAAS,CACL,eAAgB,oBAEpB3B,KAAM,CACF,QAAWY,EACX,WAAcyD,GAElBhF,QAAS,KAEjB,CC3OA,OACA1C,KAAA,cACAqD,IAAAA,GACA,OACA7B,YAAA,GACAC,eAAA,GACAX,SAAA,CACA,CAAAd,KAAA,OAAAkB,MAAA,cAAAC,KAAA,YACA,CAAAnB,KAAA,QAAAkB,MAAA,kBAAAC,KAAA,eACA,CAAAnB,KAAA,OAAAkB,MAAA,kBAAAC,KAAA,WACA,CAAAnB,KAAA,OAAAkB,MAAA,oBAAAC,KAAA,WACA,CAAAnB,KAAA,OAAAkB,MAAA,sBAAAC,KAAA,QACA,CAAAnB,KAAA,OAAAkB,MAAA,2BAAAC,KAAA,QACA,CAAAnB,KAAA,SAAAkB,MAAA,qBAAAC,KAAA,QACA,CAAAnB,KAAA,OAAAkB,MAAA,4BAAAC,KAAA,WACA,CAAAnB,KAAA,OAAAkB,MAAA,iBAAAC,KAAA,YAEAE,cAAA,EAEA,EACAsG,SAAA,CACAC,YAAAA,GACA,YAAAC,OAAA7H,MAAA,WACA,GAEAE,OAAAA,GACA,KAAA4H,WACA,EACA1H,QAAA,CACA0H,SAAAA,GACAC,GAAAC,IAAA,qBAAAC,IACA,KAAAzG,YAAAyG,EAAAjI,KACA,KAAAyB,eAAAwG,EAAAC,OAAA,IAEA,UAAA1G,aAAA,UAAAC,gBACAoC,IAAAsE,MAAAC,IACA,KAAA5G,YAAA4G,EAAApI,KACA,KAAAyB,eAAA2G,EAAAF,OAAA,GAGA,EACAvH,SAAAA,GACA,KAAAU,cAAA,CACA,EACAC,QAAAA,GACA,KAAAD,cAAA,CACA,EACAE,YAAAA,GACA,KAAAF,cAAA,CACA,IC/GmP,ICQ/O,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,QCnB5B5B,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,qBAAqB,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI2I,QAAQ,cAAc3I,EAAI4I,SAAS,YAAa,EAAM,OAASC,GAAUA,EAAOtH,IAAI,OAAS,CAAEuH,EAAG,MAAOC,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,UAAU0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBC,MAAMrJ,EAAIsJ,qBAAqBT,GAAQzI,MAAM,CAAC,UAAY,GAAG,YAAc,QAAQ,SAAWyI,EAAOU,WAAWvI,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAIA,EAAOzE,KAAK0E,QAAQ,QAAQzJ,EAAI0J,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOjI,IAAI,SAAgB,KAAYvB,EAAI4J,oBAAoBf,EAAO,GAAGgB,MAAM,CAACC,MAAOjB,EAAOtE,QAASwF,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKpB,EAAQ,UAAWmB,EAAI,EAAEE,WAAW,oBAAoB,GAAG,CAAC3I,IAAI,SAAS0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOsB,WAAWjK,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,OAAO,CAACY,YAAY,YAAY,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOvI,MAAM,OAAOJ,EAAG,OAAO,CAACY,YAAY,WAAW,CAACd,EAAIe,GAAG,IAAIf,EAAI0B,GAAGmH,EAAOL,gBAAgB,GAAG,CAACjH,IAAI,OAAO0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,OAAO,CAACmJ,MAAM,CACztC,gBAAiBR,EAAOU,aAAeV,EAAOuB,YAAoC,YAAtBvB,EAAOuB,YACnE,eAAgBvB,EAAOU,WAAmC,YAAtBV,EAAOuB,WAC3C,gBAAiBvB,EAAOU,WAAmC,YAAtBV,EAAOuB,WAC5C,cAAevB,EAAOU,WAAmC,SAAtBV,EAAOuB,aACzC,CAACpK,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOwB,SAAS,GAAGrK,EAAImB,GAAInB,EAAIsK,WAAW,SAASjJ,GAAM,MAAO,CAACE,IAAIF,EAAKE,IAAI0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAIL,YAAY,CAAC,MAAQ,QAAQd,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOxH,EAAKE,QAAQ,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOxH,EAAKE,OAAO,OAAO,EAAE,KAAI,MAAK,MAAS,IAAI,EAC7U,EACIlB,EAAkB,G,gDCoDtB,GACAC,KAAA,mBACAqD,IAAAA,GACA,OACA2G,UAAA,GACA3B,QAAA,CACA,CACA/H,MAAA,KACA6J,UAAA,SACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,WAEA,CACA/J,MAAA,KACA6J,UAAA,OACAC,MAAA,GACA3B,YAAA,CAAA4B,aAAA,SAEA,CACA/J,MAAA,KACA6J,UAAA,UACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,aAGA/B,SAAA,GACAgC,WAAA,GACAC,OAAA,GACAC,mBAAA,GACAC,UAAA,GACAC,iBAAA,GAEA,EACAC,OAAAA,GAEA,MAAAC,EAAApI,aAAAC,QAAA,sBACAmI,IACA,KAAAZ,UAAA1H,KAAAC,MAAAqI,GAAAlM,KAAAqC,IAAA,CACAE,IAAAF,EAAAE,IACAX,MAAAS,EAAAT,UAIA,KAAA0J,UAAAa,SAAA9J,IACA,KAAAsH,QAAAyC,KAAA,CACAxK,MAAAS,EAAAT,MACA6J,UAAApJ,EAAAE,IACAmJ,MAAA,IACA3B,YAAA,CAAA4B,aAAAtJ,EAAAE,MACA,IAGA,EACAb,QAAA,CAEA2K,eAAAA,CAAA9G,GACA,MAAA+G,EAAA,aACA,OAAAA,EAAAC,KAAAhH,EACA,EAEAiH,sBAAAA,CAAAjK,EAAAsC,GACA,KAAAoG,KAAA,KAAAe,iBAAAzJ,EAAAsC,GAEA4H,YAAA,KACA,KAAAxB,KAAA,KAAAe,iBAAAzJ,EAAA,QACA,IACA,EAGA+H,oBAAAA,CAAAT,GACA,OACA,iCAAAmC,iBAAAnC,EAAAtH,KACA,6BAAAyJ,iBAAAnC,EAAAtH,KAEA,EAGAqI,mBAAAA,CAAAf,GAEA,IAAA5G,OAAAyJ,iBAIA,YAAAL,gBAAAxC,EAAAtE,cAMAsE,EAAAU,UACAxF,EAAAA,EAAA4H,QAAA,iBAIA,KAAAf,WAAAO,SAAA,CAAAS,EAAAtK,KACA,MAAAuK,EAAA,KAAAd,UAAAe,WAAAC,GAAAA,IAAAlD,EAAAsB,SACAyB,EAAAI,aAAAC,UAAAC,MAAA5K,IAAAuK,IACAD,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,YACA0B,SAAA,QACA0D,OAAAtB,EAAAvI,KACAiE,QAAAsE,EAAAtE,WAEA,KAAAiH,uBAAA3C,EAAAtH,IAAA,WAIA,MAvBAwC,EAAAA,EAAAV,MAAA,sBACA,KAAAmI,uBAAA3C,EAAAtH,IAAA,UALAU,OAAAyJ,kBAAA,CA6BA,EAEAnB,cAAAA,CAAA1G,GACA,MAAAwI,EAAA,CACAC,QAAA,UACAC,KAAA,UACAC,QAAA,UACAC,QAAA,WAEA,OAAAJ,EAAAxI,IAAAwI,EAAAI,OACA,EAGAjC,aAAAA,CAAA3G,GACA,MAAA6I,EAAA,CACAJ,QAAA,KACAC,KAAA,KACAC,QAAA,MACAC,QAAA,OAEA,OAAAC,EAAA7I,IAAA6I,EAAAD,OACA,EAEAE,aAAAA,GACA,MAAAzB,EAAApI,aAAAC,QAAA,YACA,KAAA+H,mBAAAlI,KAAAC,MAAAqI,GAAAlM,KAAAoD,GAAA,QAAAA,WACA,KAAA0I,mBAAAK,SAAA1F,IACA,IAEAmH,EAFAhB,EAAA,IAAAK,UAAAxG,GACAoH,EAAA,IAEA,MAAAC,EAAA,IACA,IAAAC,GAAA,EAEA,MAAAC,EAAAA,KACA,GAAApB,EAAAI,aAAAC,UAAAC,KAAA,CAEA,GADAW,GAAAC,EACAD,EAAA,IAIA,OAHAvJ,QAAAD,MAAA,oBAAAoC,MACA1B,EAAAA,EAAAV,MAAA,UAAAoC,sBACAmG,EAAAqB,QAGA3J,QAAA4J,KAAA,6BAAAL,UAAApH,MACA1B,EAAAA,EAAA4H,QAAA,UAAAlG,uBACAmH,EAAAnB,WAAAuB,EAAAH,EACA,MACAE,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,QACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,GAEAK,aAAAR,EACA,EAEAhB,EAAAyB,OAAA,KACA/J,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAAuI,QAAA,qBAAA7G,KACAsH,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,QACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,EACA,EAEAnB,EAAA0B,QAAAjK,IACAC,QAAAD,MAAA,kBAAAoC,MAAApC,GACAU,EAAAA,EAAAV,MAAA,UAAAoC,kBAAA,EAEAmG,EAAA2B,QAAA,KACAjK,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAA4H,QAAA,qBAAAlG,IAAA,EAEAmG,EAAA4B,UAAA,KAAAC,uBAEAb,EAAAnB,WAAAuB,EAAAH,GACA,KAAAjC,WAAAQ,KAAAQ,EAAA,GAEA,EAEA6B,sBAAAA,CAAAC,GACA,MAAA/J,EAAAf,KAAAC,MAAA6K,EAAA/J,MAGA,GAFAL,QAAAC,IAAAI,GAEA,cAAAA,EAAAoB,KAKA,OAAApB,EAAAoB,MACA,iBACA,IAAApB,EAAAgK,WACA5J,EAAAA,EAAAuI,QAAA,aAEAvI,EAAAA,EAAA4H,QAAA,GAAAhI,EAAAI,WAEA,MACA,iBACA,KAAA6J,mBAAAjK,GACA,MACA,iBACA,eACA,KAAAkK,iBAAAlK,GACA,MACA,gBACA,iBACA,eACA,KAAAmK,qBAAAnK,GACA,MACA,gBACA,KAAAoK,sBAAApK,GACA,MACA,QACAL,QAAA4J,KAAA,WAAAvJ,EAAAoB,WA5BA,KAAA8I,iBAAAlK,EA8BA,EACAoK,qBAAAA,CAAApK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,IACA,IAAAlF,EAAAgK,YACA,KAAAnC,uBAAA3C,EAAAtH,IAAA,WACAsH,EAAAuB,WAAA,KACAvB,EAAAU,WAAA,EACAV,EAAAwB,KAAA,IAEA,KAAAmB,uBAAA3C,EAAAtH,IAAA,SACAwC,EAAAA,EAAAV,MAAA,WAAAM,EAAAI,YAEA,KAAAmK,eAEA,EACAN,kBAAAA,CAAAjK,GACA,MAAAwK,EAAAxK,EAAAsK,QAAAjP,KAAA,CAAAqC,EAAAC,KACA,MAAA8M,EAAA,CACA7M,IAAA,GAAAoC,EAAArD,QAAAe,KAAAC,IACAhB,KAAAe,EACA8I,OAAAxG,EAAArD,KACA+J,KAAA,EACA9F,QAAA,GACAiE,QAAA7E,EAAA6E,SAKA,OAHA,KAAA8B,UAAAa,SAAAkD,IACAD,EAAAC,EAAA9M,KAAA,QAEA6M,CAAA,IAEA,KAAAxF,SAAA,KAAAA,SAAA0F,OAAAH,GACA,KAAApD,UAAAK,KAAAzH,EAAArD,KACA,EAEAuN,gBAAAA,CAAAlK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MACA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SACA5M,EAAAkD,UAAAZ,EAAAY,UAGAsE,IACA,cAAAlF,EAAAoB,MACA8D,EAAAU,WAAA,EACAV,EAAAuB,WAAA,UACA,KAAAmE,WAAA5K,GAEA,KAAAuK,iBAEArF,EAAAU,WAAA,EACAV,EAAAuB,WAAA,eAAAzG,EAAAoB,KAAA,iBACA,KAAAkF,KAAApB,EAAA,aAAAA,EAAAuB,YACA,KAAAoE,UAAA7K,GAEA8H,YAAA,KACA,eAAA9H,EAAAoB,OACA8D,EAAAtE,QAAA,IAEA,KAAA0F,KAAApB,EAAA,UAAAA,EAAAtE,QAAA,GACA,KAEA,KAAA2J,gBAGA,EACAJ,oBAAAA,CAAAnK,GACA,MAAA8K,EAAA,KAAA7F,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAkK,IACA,cAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GACA,eAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,IAEAA,EAAA9K,EAAAtC,MAAA,OACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GAGA,EACAF,UAAAA,CAAA5K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACA,GAAAsE,IAAA,KAAAgC,OAAAhC,EAAAtH,KAAA,CACA,IAAAmN,EAAA,EACA,KAAA7D,OAAAhC,EAAAtH,KAAAoN,aAAA,KACAD,IACA7F,EAAAwB,KAAA,KAAAuE,WAAAF,GACA,KAAA9F,SAAAC,EAAAtH,KAAA8I,KAAAxB,EAAAwB,IAAA,GACA,IACA,CACA,EAEAmE,SAAAA,CAAA7K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,GAAA,KAAAgC,OAAAhC,EAAAtH,OACAsN,cAAA,KAAAhE,OAAAhC,EAAAtH,aACA,KAAAsJ,OAAAhC,EAAAtH,KAEA,EAEAqN,UAAAA,CAAAF,GACA,MAAAI,EAAAC,KAAAC,MAAAN,EAAA,IACAO,EAAAP,EAAA,GACA,SAAAI,EAAAI,WAAAC,SAAA,UAAAF,EAAAC,WAAAC,SAAA,QACA,GAEA3O,OAAAA,GACAiL,YAAA,KACA,KAAAkB,eAAA,GACA,IACA,EACAyC,aAAAA,GACAzP,OAAAD,KAAA,KAAAmL,QAAAM,SAAA5J,IACAsN,cAAA,KAAAhE,OAAAtJ,GAAA,IAEA,KAAAqJ,WAAAO,SAAAS,IACAA,GACAA,EAAAqB,OACA,GAEA,GCvZsQ,KCQlQ,IAAY,OACd,GACA,EACA,GACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BlN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,mBAAmB,CAACZ,EAAG,SAAS,CAACY,YAAY,aAAaV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIqP,KAAK,OAAS,UAAUrO,GAAG,CAAC,OAAShB,EAAIsP,cAAc,CAACpP,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,WAAW,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,YAAaI,WAAW,iBAAiBhJ,YAAY,CAAC,MAAQ,SAASd,MAAM,CAAC,YAAc,OAAOY,GAAG,CAAC,OAAShB,EAAIuP,uBAAuB,CAACrP,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIe,GAAG,WAAWb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIe,GAAG,UAAUb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIe,GAAG,aAAa,IAAI,GAAGb,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,UAAW,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAIqL,oBAAuBnB,WAAW,6DAA6D9J,MAAM,CAAC,YAAc,QAAQ,UAAY,OAAO,GAAGF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,iBAAiB,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,aAAcI,WAAW,kBAAkBpJ,YAAY,sBAAsBV,MAAM,CAAC,YAAc,CAAC,OAAQ,QAAQ,OAASJ,EAAI0P,QAAQ1O,GAAG,CAAC,OAAShB,EAAI2P,0BAA0B,GAAGzP,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,WAAW,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,SAAU,CAAE8F,aAAc,IAAM1F,WAAW,oCAAoChJ,YAAY,CAAC,MAAQ,UAAU,CAAChB,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAIe,GAAG,UAAUb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAIe,GAAG,eAAeb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAIe,GAAG,eAAeb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,IAAI,CAACJ,EAAIe,GAAG,YAAY,IAAI,GAAGb,EAAG,cAAc,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,UAAUY,GAAG,CAAC,MAAQhB,EAAIsP,cAAc,CAACtP,EAAIe,GAAG,QAAQb,EAAG,WAAW,CAACgB,YAAY,CAAC,cAAc,OAAOF,GAAG,CAAC,MAAQhB,EAAI6P,cAAc,CAAC7P,EAAIe,GAAG,SAAS,IAAI,IAAI,GAAGb,EAAG,SAAS,CAACY,YAAY,cAAcV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,WAAW,CAAC4P,KAAK,SAAS,CAAC5P,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAI+P,gBAAgB,CAAC7P,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,cAAcJ,EAAIe,GAAG,UAAU,IAAI,GAAGb,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI2I,QAAQ,WAAa3I,EAAI2D,KAAK,QAAU3D,EAAIgQ,QAAQ,WAAahQ,EAAIiQ,YAAYjP,GAAG,CAAC,OAAShB,EAAIkQ,mBAAmBnH,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,YAAY0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOsH,aAAa,CAACnQ,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOsH,YAAY,OAAO,GAAG,CAAC5O,IAAI,YAAY0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOuH,aAAa,CAACpQ,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOuH,YAAY,OAAO,GAAG,CAAC7O,IAAI,YAAY0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOwH,aAAa,CAACrQ,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOwH,YAAY,OAAO,GAAG,CAAC9O,IAAI,cAAc0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOyH,eAAe,CAACtQ,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOyH,cAAc,OAAO,QAAQ,IAAI,EACjoG,EACIjQ,GAAkB,GC6EtB,IACAsD,IAAAA,GACA,OACA+L,OAAA,CACAa,KAAA,CACAC,YAAA,QACAC,gBAAA,IACAC,iBAAA,IACAC,eAAA,IACAC,gBAAA,IACAC,iBAAA,gBACAC,MAAA,KACAC,IAAA,KACAC,GAAA,KACAC,MAAA,KACAC,SAAA,KACAC,SAAA,KACAC,UAAA,KACAC,UAAA,KACAC,YAAA,OACAC,WAAA,OACAC,aAAA,OACAC,WAAA,QACAC,YAAA,MACAC,WAAA,aACAC,UAAA,KACAC,eAAA,sBACAC,WAAA,WACAC,aAAA,MACAC,SAAA,QACAC,GAAA,KACAC,GAAA,MAEAC,iBAAA,CACA3B,YAAA,UAGAnB,KAAA,KAAA+C,MAAAC,WAAA,MACArC,SAAA,EACArM,KAAA,GACAsM,WAAA,CACAqC,QAAA,EACAC,SAAA,GACAC,gBAAA,uBACAC,UAAAA,CAAAC,EAAAC,IACAA,EAAA,OAAAA,EAAA,QAAAD,EAAA,IAEAE,iBAAA,EACAC,iBAAA,EACAH,MAAA,GAEA/J,QAAA,CACA,CAAA/H,MAAA,QAAA6J,UAAA,eAAAlJ,IAAA,gBACA,CAAAX,MAAA,OAAA6J,UAAA,eAAAlJ,IAAA,gBACA,CAAAX,MAAA,OAAA6J,UAAA,eAAAlJ,IAAA,gBACA,CAAAX,MAAA,UAAA6J,UAAA,YAAAlJ,IAAA,YAAAwH,YAAA,CAAA4B,aAAA,cACA,CAAA/J,MAAA,SAAA6J,UAAA,YAAAlJ,IAAA,YAAAwH,YAAA,CAAA4B,aAAA,cACA,CAAA/J,MAAA,SAAA6J,UAAA,YAAAlJ,IAAA,YAAAwH,YAAA,CAAA4B,aAAA,cACA,CAAA/J,MAAA,SAAA6J,UAAA,cAAAlJ,IAAA,cAAAwH,YAAA,CAAA4B,aAAA,iBAEAmI,UAAA,EACAC,QAAA,EACAC,UAAA,kEACAC,gBAAA,GAEA,EACAvS,QAAA,CACA6O,oBAAAA,CAAAzF,GACA,KAAAmJ,gBAAAnJ,EACA,KAAAuF,KAAA6D,eAAA,CAAA3O,QAAA,KACA,KAAA8K,KAAA8D,eAAA,aAAAC,OAAA,GACA,EACA/H,eAAAA,CAAAgI,EAAAvJ,EAAAC,GACA,QAAAkJ,kBACAnJ,EAEA,YADAC,EAAA,SAIA,IAAAD,EAEA,YADAC,IAGA,MAAAuJ,EAAA,QACA,GAAAA,EAAA/H,KAAAzB,GAIA,YAAAmJ,iBACA,YACA,YACAnJ,EAAAyJ,OAAA,GAAAzJ,EAAAyJ,OAAA,GACAxJ,EAAA,eAEAA,IAEA,MACA,YACA,KAAAD,EAAAyJ,OACAxJ,EAAA,aAEAA,IAEA,MACA,QACAA,SApBAA,EAAA,SAsBA,EACA4F,qBAAAA,CAAA6D,GACAA,GAAA,IAAAA,EAAAD,QACA,KAAAT,UAAAU,EAAA,GAAAC,QAAA,OAAAC,OACA,KAAAX,QAAAS,EAAA,GAAAG,MAAA,OAAAD,SAEA,KAAAZ,UAAA,EACA,KAAAC,QAAA,EAEA,EACAzD,WAAAA,CAAA/P,GACAA,EAAAqU,iBACA,KAAAvE,KAAA8D,gBAAA,CAAAU,EAAAC,KACAD,GACA,KAAAzL,UAAA0L,EACA,GAEA,EACAjE,WAAAA,GACA,KAAAR,KAAA0E,cACA,KAAAd,gBAAA,GACA,KAAAH,UAAA,EACA,KAAAC,QAAA,CACA,EACAhD,aAAAA,GACA,KAAAV,KAAA8D,gBAAA,CAAAU,EAAAC,KACA,GAAAD,EAOA,KAAAG,SAAA3Q,MAAA,mBAPA,CACA,MAAAoD,EAAAqN,EAAArN,UAAA,GACAlC,EAAAuP,EAAAvP,SAAA,GACAiC,EAAAC,EAAAlC,EAAA,KAAAuO,UAAA,KAAAC,QAAAe,EAAAjQ,OAAA,KAAAoM,WAAAqC,QAAA,KAAArC,WAAAsC,UAAA0B,OAAA5Q,IACAC,QAAAC,IAAAF,EAAA,GAEA,CAEA,GAEA,EACA6M,iBAAAA,CAAAD,GACA,KAAAA,WAAAA,EACA,KAAAZ,KAAA8D,gBAAA,CAAAU,EAAAC,KACAD,GACA,KAAAzL,UAAA0L,EACA,GAEA,EACA1L,SAAAA,CAAA0L,GACA,KAAA9D,SAAA,EACA,MAAAvJ,EAAAqN,EAAArN,UAAA,GACAlC,EAAAuP,EAAAvP,SAAA,GACAjB,QAAAC,IAAA,KAAAuP,WACAxP,QAAAC,IAAA,KAAAwP,SAEA5M,EAAAM,EAAAlC,EAAA,KAAAuO,UAAA,KAAAC,QAAAe,EAAAjQ,QAAA,OAAAoM,WAAAqC,QAAA,KAAArC,WAAAsC,UAAA9J,MAAAC,IACA,KAAAuH,WAAAyC,MAAAhK,EAAAwL,WAAA,KAAAjE,WAAAsC,SACA,KAAA5O,KAAA+E,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA2D,aAAA5D,EAAA4D,aACAkL,UAAA9O,EAAA8O,UACAjL,aAAA7D,EAAA6D,aACAkL,UAAA/O,EAAA+O,UACAjL,aAAA9D,EAAA8D,aACAkL,UAAAhP,EAAAgP,UACAC,YAAAjP,EAAAiP,eACA,IACA2D,OAAA5Q,IACAC,QAAAC,IAAAF,GACA,KAAA2Q,SAAA3Q,MAAA,aACA+Q,SAAA,KACA,KAAApE,SAAA,IAEA,EACAzF,cAAAA,CAAA1G,GACA,MAAAwQ,EAAA,yBACA,OAAA1U,OAAAD,KAAAmE,GAAA0P,OACA,OAAAc,EAAA,GAEA,IAAAC,GAAA,EACAC,GAAA,EACA,UAAAhT,KAAAsC,EAAA,CACA,MAAAiG,EAAAjG,EAAAtC,GACA,QAAAuI,EAAA,CACAwK,GAAA,EACA,KACA,SAAAxK,IACAyK,GAAA,EAEA,CACA,OAAAD,EACAD,EAAA,GACAE,EACAF,EAAA,GAEAA,EAAA,EACA,EACA7J,aAAAA,CAAA3G,GACA,MAAA2Q,EAAA,kCACA,OAAA7U,OAAAD,KAAAmE,GAAA0P,OACA,OAAAiB,EAAA,GAEA,IAAAF,GAAA,EACAC,GAAA,EACA,UAAAhT,KAAAsC,EAAA,CACA,MAAAiG,EAAAjG,EAAAtC,GACA,QAAAuI,EAAA,CACAwK,GAAA,EACA,KACA,SAAAxK,IACAyK,GAAA,EAEA,CACA,OAAAD,EACAE,EAAA,GACAD,EACAC,EAAA,GAEAA,EAAA,EACA,GAEAhU,OAAAA,GACA,KAAA4H,UAAA,KAAAiH,KAAAoF,iBACA,GClTkQ,MCQ9P,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5B1U,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,WAAW,CAACY,YAAY,UAAU,CAACZ,EAAG,MAAM,CAACwU,MAAO,CAAEC,WAAY,OAAQC,QAAS,OAAQC,UAAW,UAAY,CAAC3U,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,SAAS,UAAW,IAAQ,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI8U,eAAe,cAAc9U,EAAI+U,YAAY,YAAa,GAAOhM,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,SAAS0H,GAAG,SAASC,GAAM,MAAO,CAAChJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAiB,QAAT8I,EAAiB,QAAmB,OAATA,EAAgB,OAAS,QAAQ,CAAClJ,EAAIe,GAAG,IAAIf,EAAI0B,GAAGwH,GAAM,OAAO,QAAQ,IAAI,GAAGhJ,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,MAAQ,OAAO,UAAW,IAAQ,CAACF,EAAG,mBAAmB,CAACA,EAAG,cAAc,CAACgB,YAAY,CAAC,aAAa,MAAM,QAAU,SAASd,MAAM,CAAC,MAAQ,SAAS,MAAQJ,EAAIgV,eAAe,UAAY,GAAGjM,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,SAAS0H,GAAG,WAAW,MAAO,CAAC/I,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,aAAa,EAAE6U,OAAM,OAAU/U,EAAG,cAAc,CAACgB,YAAY,CAAC,aAAa,OAAO,QAAU,SAASd,MAAM,CAAC,MAAQ,UAAU,MAAQJ,EAAIkV,eAAe,UAAY,GAAGnM,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,SAAS0H,GAAG,WAAW,MAAO,CAAC/I,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,gBAAgB,EAAE6U,OAAM,QAAW,IAAI,IAAI,IAAI,GAAG/U,EAAG,SAAS,CAACgB,YAAY,CAAC,aAAa,QAAQd,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAImV,YAAY,cAAcnV,EAAI4I,UAAUG,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,WAAW0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,aAAa,CAACE,MAAM,CAAC,QAAUyI,EAAOuM,SAAS,KAAO,WAAW,GAAG,CAAC7T,IAAI,SAAS0H,GAAG,SAASC,GAAM,MAAO,CAAChJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAiB,QAAT8I,EAAiB,OAAkB,OAATA,EAAgB,QAAU,WAAW,CAAClJ,EAAIe,GAAG,IAAIf,EAAI0B,GAAGwH,GAAM,OAAO,QAAQ,GAAGhJ,EAAG,SAAS,CAACgB,YAAY,CAAC,aAAa,QAAQd,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,WAAa,aAAa,WAAaJ,EAAIqV,eAAetM,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,aAAa0H,GAAG,SAAS5H,GAAM,OAAOnB,EAAG,cAAc,CAAC,EAAE,CAACA,EAAG,mBAAmB,CAACE,MAAM,CAAC,YAAciB,EAAK4C,cAAc,CAAC/D,EAAG,WAAW,CAAC4P,KAAK,SAAS,CAAC5P,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,iBAAiB,CAACJ,EAAIe,GAAGf,EAAI0B,GAAGL,EAAKT,YAAYV,EAAG,WAAW,CAACwU,MAAO,CAAEY,gBAAiBjU,EAAKkU,OAAS,UAAY,WAAanV,MAAM,CAAC,KAAO,SAAS,KAAOiB,EAAKkU,OAAS,QAAU,SAASzF,KAAK,YAAY,GAAG5P,EAAG,MAAM,CAACF,EAAIe,GAAGf,EAAI0B,GAAGL,EAAKgJ,UAAU,EAAE,QAAQ,IAAI,GAAGnK,EAAG,kBAAkB,CAACgB,YAAY,CAAC,aAAa,WAAW,CAAClB,EAAIe,GAAG,qDAAqD,EAC/3E,EACIV,GAAkB,GCqFtB,IACAsD,IAAAA,GACA,OACAmR,eAAA,CACA,CAAAlU,MAAA,OAAA6J,UAAA,KAAAlJ,IAAA,MACA,CAAAX,MAAA,KAAA6J,UAAA,SAAAlJ,IAAA,SAAAwH,YAAA,CAAA4B,aAAA,WACA,CAAA/J,MAAA,SAAA6J,UAAA,gBAAAlJ,IAAA,iBACA,CAAAX,MAAA,SAAA6J,UAAA,iBAAAlJ,IAAA,mBAEAwT,YAAA,CACA,CAAA5V,GAAA,OAAA0E,OAAA,MAAA2R,cAAA,WAAAC,eAAA,GACA,CAAAtW,GAAA,OAAA0E,OAAA,KAAA2R,cAAA,IAAAC,eAAA,GACA,CAAAtW,GAAA,OAAA0E,OAAA,MAAA2R,cAAA,WAAAC,eAAA,GACA,CAAAtW,GAAA,OAAA0E,OAAA,KAAA2R,cAAA,IAAAC,eAAA,IAEAT,eAAA,EACAE,eAAA,EACAC,YAAA,CACA,CAAAvU,MAAA,OAAA6J,UAAA,WAAAlJ,IAAA,YACA,CAAAX,MAAA,OAAA6J,UAAA,WAAAlJ,IAAA,YACA,CAAAX,MAAA,KAAA6J,UAAA,WAAAlJ,IAAA,WAAAwH,YAAA,CAAA4B,aAAA,aACA,CAAA/J,MAAA,KAAA6J,UAAA,SAAAlJ,IAAA,SAAAwH,YAAA,CAAA4B,aAAA,YAEA/B,SAAA,CACA,CAAA8M,SAAA,WAAArH,SAAA,QAAA+G,SAAA,GAAAvR,OAAA,OACA,CAAA6R,SAAA,WAAArH,SAAA,OAAA+G,SAAA,GAAAvR,OAAA,OACA,CAAA6R,SAAA,WAAArH,SAAA,OAAA+G,SAAA,IAAAvR,OAAA,OAEAwR,cAAA,CACA,CAAAzU,MAAA,gBAAAqD,YAAA,YAAAsR,QAAA,EAAAlL,KAAA,oBACA,CAAAzJ,MAAA,gBAAAqD,YAAA,UAAAsR,QAAA,EAAAlL,KAAA,oBACA,CAAAzJ,MAAA,gBAAAqD,YAAA,WAAAsR,QAAA,EAAAlL,KAAA,qBAGA,GCzHgQ,MCQ5P,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BtK,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,qBAAqB,CAACZ,EAAG,SAAS,CAACY,YAAY,aAAaV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIqP,MAAMrO,GAAG,CAAC,OAAShB,EAAI2V,eAAe,CAACzV,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,YAAa,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,YAAc,CAAE0L,UAAWzP,EAAI6V,mBAAsB3L,WAAW,uGAAuG9J,MAAM,CAAC,YAAc,WAAW,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,YAAY,CAACF,EAAG,gBAAgB,CAACE,MAAM,CAAC,QAAU,KAAK,CAACF,EAAG,iBAAiB,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,WAAY,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,SAAW,CAAE0L,UAAWzP,EAAI8V,kBAAqB5L,WAAW,kGAAkGhJ,YAAY,CAAC,MAAQ,OAAOd,MAAM,CAAC,IAAM,EAAE,IAAM,QAAU,IAAI,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,WAAW,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,SAAU,CAAEiM,cAAe,UAAWnG,cAAc,IAAS1F,WAAW,iEAAiE9J,MAAM,CAAC,mBAAmB,KAAK,sBAAsB,UAAU,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,QAAQY,GAAG,CAAC,MAAQhB,EAAIgW,YAAY,CAAChW,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,IAAI,GAAGb,EAAG,SAAS,CAACY,YAAY,cAAcV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIiW,UAAU,OAAS,UAAUjV,GAAG,CAAC,OAAShB,EAAIsP,cAAc,CAACpP,EAAG,QAAQ,CAACgB,YAAY,CAAC,OAAS,OAAO,mBAAmB,UAAU,QAAU,OAAO,cAAc,UAAUd,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,YAAa,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAqB3L,WAAW,6DAA6D9J,MAAM,CAAC,YAAc,QAAQ,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,WAAW,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,eAAgBI,WAAW,oBAAoBhJ,YAAY,CAAC,MAAQ,SAASd,MAAM,CAAC,YAAc,UAAU,CAACF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,KAAK,CAACJ,EAAIe,GAAG,QAAQb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACJ,EAAIe,GAAG,QAAQb,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACJ,EAAIe,GAAG,UAAU,IAAI,IAAI,GAAGb,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACA,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,UAAUY,GAAG,CAAC,MAAQhB,EAAIsP,cAAc,CAACtP,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,GAAGb,EAAG,UAAU,CAACgB,YAAY,CAAC,aAAa,QAAQd,MAAM,CAAC,OAAS,MAAM,QAAUJ,EAAI2I,QAAQ,WAAa3I,EAAIkW,aAAa,WAAalW,EAAImW,mBAAmBnV,GAAG,CAAC,OAAShB,EAAIoW,yBAAyBrN,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,SAAS0H,GAAG,SAASC,GAAM,MAAO,CAAChJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQ8I,EAAO,QAAU,QAAQ,CAAClJ,EAAIe,GAAG,IAAIf,EAAI0B,GAAGwH,EAAO,KAAO,OAAO,OAAO,GAAG,CAAC3H,IAAI,SAAS0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAGA,EAAOpJ,KAAyDO,EAAIqW,KAAvDnW,EAAG,OAAO,CAACY,YAAY,WAAW,CAACd,EAAIe,GAAG,SAAkBb,EAAG,WAAW,CAACY,YAAY,gBAAgBV,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAIsW,WAAWzN,EAAO,IAAI,CAAC7I,EAAIe,GAAG,UAAU,QAAQ,GAAGb,EAAG,uBAAuB,CAACqW,IAAI,uBAAuBnW,MAAM,CAAC,YAAcJ,EAAIuE,QAAQ,cAAgBvE,EAAIwW,UAAUxV,GAAG,CAAC,gBAAgBhB,EAAIyW,uBAAuB,EACv6H,EACIpW,GAAkB,GCFlBN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,UAAU,QAAUJ,EAAI0W,QAAQ,kBAAkB1W,EAAI2W,eAAe,OAAS,MAAM3V,GAAG,CAAC,GAAKhB,EAAI4B,SAAS,OAAS5B,EAAI6B,eAAe,CAAC3B,EAAG,SAAS,CAACE,MAAM,CAAC,SAAWJ,EAAIgQ,UAAU,CAAC9P,EAAG,SAAS,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,QAAQd,MAAM,CAAC,KAAOJ,EAAI4W,YAAY5V,GAAG,CAAC,OAAShB,EAAI6W,eAAe,CAAC3W,EAAG,cAAc,CAACgB,YAAY,CAAC,cAAc,MAAM,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,mBAAmB,KAAK,sBAAsB,MAAM,SAAWJ,EAAI2W,gBAAgB9M,MAAM,CAACC,MAAO9J,EAAI8W,YAAa/M,SAAS,SAAUC,GAAMhK,EAAI8W,YAAY9M,CAAG,EAAEE,WAAW,kBAAkB,GAAGhK,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,QAAQ,CAAChB,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,OAAO,QAAUJ,EAAI2W,gBAAgB3V,GAAG,CAAC,MAAQhB,EAAI6W,eAAe,CAAC7W,EAAIe,GAAG,WAAW,IAAI,IAAI,IAAI,IAAI,EACngC,EACIV,GAAkB,GCwCtB,IACAC,KAAA,uBACAyW,MAAA,CACAC,YAAA,CACAjS,KAAAkS,OACArB,UAAA,GAEAsB,cAAA,CACAnS,KAAAoS,QACAvB,UAAA,IAGAjS,IAAAA,GACA,OACAyT,aAAA,GACAN,YAAA,KAAAI,cACAN,WAAA,KAAAxE,MAAAC,WAAA,MACAqE,SAAA,EACAC,gBAAA,EACA3G,SAAA,EACAqH,aAAA,GAEA,EACApM,OAAAA,GACA,KAAAqM,QAAAhP,IAAA,0BAAAiP,IACA,KAAAT,YAAAS,CAAA,IAEA,KAAAD,QAAAhP,IAAA,2BAAAiP,IACA,KAAAH,aAAAG,CAAA,GAEA,EACA7W,QAAA,CACAO,SAAAA,GACA,KAAAyV,SAAA,CACA,EACA7U,YAAAA,GACA,KAAA6U,SAAA,EACA,KAAAE,WAAA7C,cACA,KAAAsD,aAAA,EACA,EACAR,YAAAA,CAAAtX,GACAA,EAAAqU,iBACA,KAAAgD,WAAAzD,gBAAA,MAAAU,EAAAC,KAEA,GADAxQ,QAAAC,IAAAuQ,IACAD,EAAA,CACA,KAAA8C,gBAAA,EACA,IACA9R,EAAA,KAAAuS,aAAA,KAAAN,aAAArO,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAA8O,MAAA,gBAAA9O,EAAAiF,YACA,IAAAjF,EAAAiF,YACA,KAAAqG,SAAA1H,QAAA,CACAmL,QAAA,YACAvT,SAAA,IAEA,KAAArC,gBAEAmC,EAAAA,EAAAX,MAAA,CACAU,QAAA,OACAE,YAAAyE,EAAA3E,QACAG,SAAA,GAEA,GAEA,OAAAb,GACAW,EAAAA,EAAAX,MAAA,CACAU,QAAA,OACAE,YAAAZ,EAAAK,SAAAC,KAAAI,QACAG,SAAA,GAEA,SACA,KAAAyS,gBAAA,CACA,CACA,IAEA,EACA/U,QAAAA,GACA,KAAAgV,WAAAc,QACA,ICxHmQ,MCQ/P,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCkFhC,IACAnX,WAAA,CACAoX,qBAAAA,IAEAhU,IAAAA,GACA,OACAwS,kBAAA,CACA7D,QAAA,EACAC,SAAA,GACAC,gBAAA,uBACAC,UAAAA,CAAAC,EAAAC,IACAA,EAAA,OAAAA,EAAA,QAAAD,EAAA,IAEAE,iBAAA,EACAC,iBAAA,EACAH,MAAA,GAEAnO,QAAA,GACAiS,UAAA,EACAnH,KAAA,KAAA+C,MAAAC,WAAA,MACA4D,UAAA,KAAA7D,MAAAC,WAAA,MACA6D,aAAA,GACAvN,QAAA,CACA,CAAA/H,MAAA,KAAA6J,UAAA,OAAAlJ,IAAA,QACA,CAAAX,MAAA,KAAA6J,UAAA,SAAAlJ,IAAA,SAAAwH,YAAA,CAAA4B,aAAA,WACA,CAAA/J,MAAA,KAAAW,IAAA,SAAAwH,YAAA,CAAA4B,aAAA,YAEAiN,gBAAA,GAEA,EACApX,OAAAA,GACA,KAAA4H,WACA,EACA1H,QAAA,CACA+V,kBAAAA,CAAA1S,GACA,IAAAA,GACA,KAAAqE,WAEA,EACAA,SAAAA,GACA,KAAA6N,UAAA9C,gBAAA,CAAAU,EAAAC,KACAD,GACAvP,EAAAwP,EAAA+D,UAAA/D,EAAAgE,YAAA,KAAA3B,kBAAA5D,SAAA,KAAA4D,kBAAA7D,SAAA7J,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAyN,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACA,KAAA2D,aAAAxN,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA7B,KAAA4B,EAAAkD,QACAV,OAAAxC,EAAAmD,UACA,GAEA,GAEA,EAEAqR,cAAAA,CAAAxC,EAAAvJ,EAAAC,GACA,IAAAD,EAEA,YADAC,IAGA,MAAAgO,EAAAC,OAAAlO,GACAxG,QAAAC,IAAAwU,GACAC,OAAAC,MAAAF,GACAhO,EAAA,SACAD,EAAAoF,WAAAqE,OAAA,GAAAzJ,EAAAoF,WAAAqE,OAAA,GACAxJ,EAAA,eAEAA,GAEA,EACA+L,aAAAA,CAAAzC,EAAAvJ,EAAAC,GACA,QAAAnG,IAAAkG,GAAA,OAAAA,GAAA,KAAAA,EAEA,YADAC,IAIA,MAAAmO,EAAAF,OAAAlO,GACAmO,MAAAC,GACAnO,EAAA,IAAAvK,MAAA,UAIA0Y,EAAA,MAKA,QAAA3M,KAAA2M,EAAAhJ,YAGAnF,IAFAA,EAAA,IAAAvK,MAAA,oBALAuK,EAAA,IAAAvK,MAAA,gBASA,EACA4W,uBAAAA,CAAAnG,GACA,KAAAkG,kBAAAlG,EACA,KAAAgG,UAAA9C,gBAAA,CAAAU,EAAAC,KACAD,GACAvP,EAAAwP,EAAA+D,UAAA/D,EAAAgE,YAAA,KAAA3B,kBAAA5D,SAAA,KAAA4D,kBAAA7D,SAAA7J,MAAAC,IACA,KAAAyN,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACAjP,QAAAC,IAAAmF,GACA,KAAAwN,aAAAxN,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA7B,KAAA4B,EAAAkD,QACAV,OAAAxC,EAAAmD,UACA,GAEA,GAEA,EACA,eAAAwR,GACA,KAAA3G,KAAA8D,gBAAA,CAAAU,EAAAC,KACAD,GACAjP,EAAAkP,EAAAqE,UAAArE,EAAAjQ,OAAAiQ,EAAAsE,UAAA3P,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAuN,UAAA9C,gBAAA,CAAAU,EAAAC,KACAD,IACAvP,EAAAwP,EAAA+D,UAAA/D,EAAAgE,YAAA,KAAA3B,kBAAA5D,SAAA,KAAA4D,kBAAA7D,SAAA7J,MAAAC,IACApF,QAAAC,IAAAmF,GACA,IAAAA,EAAAiF,YACA,KAAAwI,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACA,KAAA2D,aAAAxN,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA7B,KAAA4B,EAAAkD,QACAV,OAAAxC,EAAAmD,YAGA,KAAAwP,SAAA3Q,MAAAqF,EAAA3E,QACA,IAEA,KAAAsL,KAAA0E,cACA,GACA,GAEA,GAEA,EACA4B,YAAAA,CAAApW,GACAA,EAAAqU,iBACA,KAAAvE,KAAA8D,gBAAA,CAAAU,EAAAC,KACAD,IACA,KAAAG,SAAA1H,QAAA,UACAhJ,QAAAC,IAAA,YAAAuQ,GAEA,GAEA,EACAxE,WAAAA,CAAA/P,GACAA,EAAAqU,iBACA,KAAAqC,UAAA9C,gBAAA,CAAAU,EAAAC,KACAD,GACAvP,EAAAwP,EAAA+D,UAAA/D,EAAAgE,YAAA,KAAA3B,kBAAA5D,SAAA,KAAA4D,kBAAA7D,SAAA7J,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAyN,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACA,KAAA2D,aAAAxN,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA7B,KAAA4B,EAAAkD,QACAV,OAAAxC,EAAAmD,UACA,GAEA,GAEA,EACA8R,UAAAA,CAAApN,GACA,IACA,KAAA3E,QAAA2E,EAAAzJ,KACA,KAAA+W,SAAAtN,EAAArF,OACA,KAAA2T,MAAA,8BAAAhB,UACA,KAAAgB,MAAA,+BAAAjT,SACA,KAAA8T,MAAAV,qBAAA/W,MAAA,SACA,KAAAyX,MAAAV,qBAAAjB,SAAA,CACA,OAAArT,GACAC,QAAAD,MAAA,iCAAAA,EACA,CACA,IClRsQ,MCQlQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BtD,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIqP,KAAK,OAAS,aAAa,CAACnP,EAAG,SAAS,CAACY,YAAY,aAAa,CAACZ,EAAG,cAAc,CAACY,YAAY,kBAAkBV,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,MAAM,CAACY,YAAY,yBAAyB,CAACZ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,WAAWyJ,MAAM,CAACC,MAAO9J,EAAIsY,eAAgBvO,SAAS,SAAUC,GAAMhK,EAAIsY,eAAetO,CAAG,EAAEE,WAAW,oBAAoBhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAIuY,oBAAoB,CAACvY,EAAIe,GAAG,eAAe,KAAKb,EAAG,SAAS,CAACY,YAAY,eAAeV,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACY,YAAY,WAAW,CAACZ,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,WAAWyJ,MAAM,CAACC,MAAO9J,EAAIwY,MAAOzO,SAAS,SAAUC,GAAMhK,EAAIwY,MAAMxO,CAAG,EAAEE,WAAW,WAAWhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAIyY,MAAM,QAASzY,EAAIwY,MAAM,IAAI,CAACxY,EAAIe,GAAG,aAAa,GAAGb,EAAG,KAAK,CAACY,YAAY,iBAAiB,CAACd,EAAIe,GAAG,YAAYb,EAAG,SAAS,CAACY,YAAY,gBAAgBV,MAAM,CAAC,SAAW,GAAG,WAAaJ,EAAI0Y,UAAU3P,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,aAAa0H,GAAG,SAAS5H,EAAMC,GAAO,OAAOpB,EAAG,cAAc,CAAC,EAAE,CAACA,EAAG,UAAU,CAAC2J,MAAM,CAACC,MAAO9J,EAAI0Y,SAASpX,GAAQyI,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI0Y,SAAUpX,EAAO0I,EAAI,EAAEE,WAAW,qBAAqBhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAI2Y,SAAS,QAASrX,EAAM,IAAI,CAACtB,EAAIe,GAAG,WAAW,EAAE,QAAQ,GAAGb,EAAG,MAAM,CAACY,YAAY,sBAAsB,CAACZ,EAAG,KAAK,CAACY,YAAY,oBAAoB,CAACd,EAAIe,GAAG,WAAWb,EAAG,aAAa,CAACE,MAAM,CAAC,cAAgBJ,EAAI4Y,mBAAmB,QAAU5Y,EAAI6Y,eAAe7X,GAAG,CAAC,OAAShB,EAAI8Y,wBAAwB,CAAC9Y,EAAIe,GAAG,UAAUb,EAAG,YAAY,CAACY,YAAY,YAAYZ,EAAG,mBAAmB,CAACc,GAAG,CAAC,OAAShB,EAAI+Y,eAAelP,MAAM,CAACC,MAAO9J,EAAIgZ,cAAejP,SAAS,SAAUC,GAAMhK,EAAIgZ,cAAchP,CAAG,EAAEE,WAAW,kBAAkB,CAAChK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,CAAC,GAAI,MAAMJ,EAAImB,GAAInB,EAAIiZ,gBAAgB,SAAS5X,GAAM,OAAOnB,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAInB,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQiB,EAAKE,MAAM,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAGL,EAAKT,OAAO,QAAQ,EAAE,IAAG,IAAI,IAAI,KAAKV,EAAG,SAAS,CAACY,YAAY,eAAeV,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACY,YAAY,WAAW,CAACZ,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,WAAWyJ,MAAM,CAACC,MAAO9J,EAAIkZ,OAAQnP,SAAS,SAAUC,GAAMhK,EAAIkZ,OAAOlP,CAAG,EAAEE,WAAW,YAAYhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAIyY,MAAM,SAAUzY,EAAIkZ,OAAO,IAAI,CAAClZ,EAAIe,GAAG,aAAa,GAAGb,EAAG,KAAK,CAACY,YAAY,iBAAiB,CAACd,EAAIe,GAAG,YAAYb,EAAG,SAAS,CAACY,YAAY,gBAAgBV,MAAM,CAAC,SAAW,GAAG,WAAaJ,EAAImZ,WAAWpQ,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,aAAa0H,GAAG,SAAS5H,EAAMC,GAAO,OAAOpB,EAAG,cAAc,CAAC,EAAE,CAACA,EAAG,UAAU,CAAC2J,MAAM,CAACC,MAAO9J,EAAImZ,UAAU7X,GAAQyI,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAImZ,UAAW7X,EAAO0I,EAAI,EAAEE,WAAW,sBAAsBhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAI2Y,SAAS,SAAUrX,EAAM,IAAI,CAACtB,EAAIe,GAAG,WAAW,EAAE,QAAQ,GAAGb,EAAG,MAAM,CAACY,YAAY,sBAAsB,CAACZ,EAAG,KAAK,CAACY,YAAY,oBAAoB,CAACd,EAAIe,GAAG,WAAWb,EAAG,aAAa,CAACE,MAAM,CAAC,cAAgBJ,EAAIoZ,oBAAoB,QAAUpZ,EAAIqZ,gBAAgBrY,GAAG,CAAC,OAAShB,EAAIsZ,yBAAyB,CAACtZ,EAAIe,GAAG,UAAUb,EAAG,YAAY,CAACY,YAAY,YAAYZ,EAAG,mBAAmB,CAACc,GAAG,CAAC,OAAShB,EAAIuZ,gBAAgB1P,MAAM,CAACC,MAAO9J,EAAIwZ,eAAgBzP,SAAS,SAAUC,GAAMhK,EAAIwZ,eAAexP,CAAG,EAAEE,WAAW,mBAAmB,CAAChK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,CAAC,GAAI,MAAMJ,EAAImB,GAAInB,EAAIyZ,iBAAiB,SAASpY,GAAM,OAAOnB,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAInB,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQiB,EAAKE,MAAM,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAGL,EAAKT,OAAO,QAAQ,EAAE,IAAG,IAAI,IAAI,GAAGV,EAAG,SAAS,CAACY,YAAY,mBAAmBV,MAAM,CAAC,MAAQ,aAAa,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,WAAWyJ,MAAM,CAACC,MAAO9J,EAAI0Z,gBAAiB3P,SAAS,SAAUC,GAAMhK,EAAI0Z,gBAAgB1P,CAAG,EAAEE,WAAW,sBAAsB,IAAI,IAAI,GAAGhK,EAAG,SAAS,CAACY,YAAY,eAAeV,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACY,YAAY,WAAW,CAACZ,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,WAAWyJ,MAAM,CAACC,MAAO9J,EAAI2Z,YAAa5P,SAAS,SAAUC,GAAMhK,EAAI2Z,YAAY3P,CAAG,EAAEE,WAAW,iBAAiBhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAIyY,MAAM,cAAezY,EAAI2Z,YAAY,IAAI,CAAC3Z,EAAIe,GAAG,aAAa,GAAGb,EAAG,KAAK,CAACY,YAAY,iBAAiB,CAACd,EAAIe,GAAG,YAAYb,EAAG,SAAS,CAACY,YAAY,gBAAgBV,MAAM,CAAC,SAAW,GAAG,WAAaJ,EAAI4Z,gBAAgB7Q,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,aAAa0H,GAAG,SAAS5H,EAAMC,GAAO,OAAOpB,EAAG,cAAc,CAAC,EAAE,CAACA,EAAG,UAAU,CAAC2J,MAAM,CAACC,MAAO9J,EAAI4Z,eAAetY,GAAQyI,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI4Z,eAAgBtY,EAAO0I,EAAI,EAAEE,WAAW,2BAA2BhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAI2Y,SAAS,cAAerX,EAAM,IAAI,CAACtB,EAAIe,GAAG,WAAW,EAAE,QAAQ,GAAGb,EAAG,MAAM,CAACY,YAAY,sBAAsB,CAACZ,EAAG,KAAK,CAACY,YAAY,oBAAoB,CAACd,EAAIe,GAAG,WAAWb,EAAG,aAAa,CAACE,MAAM,CAAC,cAAgBJ,EAAI6Z,yBAAyB,QAAU7Z,EAAI8Z,qBAAqB9Y,GAAG,CAAC,OAAShB,EAAI+Z,8BAA8B,CAAC/Z,EAAIe,GAAG,UAAUb,EAAG,YAAY,CAACY,YAAY,YAAYZ,EAAG,mBAAmB,CAACc,GAAG,CAAC,OAAShB,EAAIga,qBAAqBnQ,MAAM,CAACC,MAAO9J,EAAIia,oBAAqBlQ,SAAS,SAAUC,GAAMhK,EAAIia,oBAAoBjQ,CAAG,EAAEE,WAAW,wBAAwB,CAAChK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,CAAC,GAAI,MAAMJ,EAAImB,GAAInB,EAAIka,sBAAsB,SAAS7Y,GAAM,OAAOnB,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAInB,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQiB,EAAKE,MAAM,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAGL,EAAKT,OAAO,QAAQ,EAAE,IAAG,IAAI,IAAI,KAAKV,EAAG,MAAM,CAACY,YAAY,UAAU,CAACZ,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAIma,aAAa,CAACna,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,EACz5L,EACIV,GAAkB,GCmLtB,I,SAAA,CACAC,KAAA,gBACAqD,IAAAA,GACA,OACA0L,KAAA,KAAA+C,MAAAC,WAAA,MACAiG,eAAA,GACAoB,gBAAA,GACAlB,MAAA,GACAU,OAAA,GACAS,YAAA,GACAjB,SAAA,GACAS,UAAA,GACAS,eAAA,GACAX,eAAA,GACAQ,gBAAA,GACAS,qBAAA,GACAlB,cAAA,GACAQ,eAAA,GACAS,oBAAA,GACApB,eAAA,EACAD,oBAAA,EACAS,gBAAA,EACAD,qBAAA,EACAU,qBAAA,EACAD,0BAAA,EAEA,EACA5O,OAAAA,GACA,KAAAmP,cACA,EACA5Z,OAAAA,GACA,KAAA6Z,iBACA,KAAAC,sBACA,EACA5Z,QAAA,CACA6X,iBAAAA,GACA,MAAAgC,EAAA,eAAAjC,iBACAxV,aAAA0X,QAAA,iBAAA5X,KAAAwJ,UAAAmO,IACA/X,EAAAiY,SAAA9X,QAAA4X,EAEA9O,YAAA,KACA,KAAA2O,eACA,KAAAC,iBACA,KAAAC,uBACAnW,IAAAsE,MAAAC,IACA,MAAAH,EAAA,CACAjI,KAAAoI,EAAApI,KACAkI,QAAAE,EAAAF,SAEAH,GAAAmP,MAAA,oBAAAjP,EAAA,IAEAxE,EAAAA,EAAAuI,QAAA,WACA,IACA,EACA8N,YAAAA,GACA,MAAApY,EAAAC,OAAAC,SAAAC,MACAC,EAAAC,GAAAL,EAAAM,MAAA,KACAC,EAAA,GAAAH,GAAA,eAAAC,GAAA,SACAqY,EAAA9X,KAAAC,MAAAC,aAAAC,QAAA,uBAEA,KAAAuV,eAAAoC,EAAAC,QAAA,eAAApY,EAEA,KAAAmW,SAAA9V,KAAAC,MAAAC,aAAAC,QAAA,iBACA,KAAAoW,UAAAvW,KAAAC,MAAAC,aAAAC,QAAA,kBACA,KAAA6W,eAAAhX,KAAAC,MAAAC,aAAAC,QAAA,uBAEA,KAAAiW,cAAApW,KAAAC,MAAAC,aAAAC,QAAA,sBACA,KAAAyW,eAAA5W,KAAAC,MAAAC,aAAAC,QAAA,uBACA,KAAAkX,oBAAArX,KAAAC,MAAAC,aAAAC,QAAA,2BACA,EACAsX,cAAAA,GACAxS,IAAAY,MAAAC,IACA,KAAAuQ,eAAAvQ,EAAAkS,MAAA,GAAAzN,MAAAnO,KAAAqC,IAAA,CACAE,IAAAF,EAAAA,KACAT,MAAAS,EAAAf,SAEA,KAAAmZ,gBAAA/Q,EAAAkS,MAAA,GAAAzN,MAAAnO,KAAAqC,IAAA,CACAE,IAAAF,EAAAA,KACAT,MAAAS,EAAAf,SAEA,KAAA4Z,qBAAAxR,EAAAkS,MAAA,GAAAzN,MAAAnO,KAAAqC,IAAA,CACAE,IAAAF,EAAAA,KACAT,MAAAS,EAAAf,SAGA,SAAA0Y,cAAAzF,QAAA,OAAAzQ,aAAAC,QAAA,yBACA,KAAAiW,cAAA,KAAAC,eAAAja,KAAAqC,GAAAA,EAAAE,OAEA,SAAAiY,eAAAjG,QAAA,OAAAzQ,aAAAC,QAAA,0BACA,KAAAyW,eAAA,KAAAC,gBAAAza,KAAAqC,GAAAA,EAAAE,OAEA,SAAA0Y,oBAAA1G,SACA,KAAA0G,oBAAA,IAEA,KAAAY,sBAAA,IACA5G,OAAA5Q,IACAC,QAAAC,IAAAF,EAAA,GAEA,EACAiX,oBAAAA,GACA9S,IAAAiB,MAAAC,IACA,IAAAA,EAAAiF,aACA,KAAA+L,gBAAAhR,EAAAoS,aACA,GAEA,EACArC,KAAAA,CAAAsC,EAAA3Y,GACAA,GAGA,QAAA2Y,QAAA3P,KAAAhJ,GACA,KAAA2Y,GAAA,IAHAhX,EAAAA,EAAAV,MAAA,cAKA,EACAsV,QAAAA,CAAAoC,EAAAzZ,GACA,QAAAyZ,QAAAC,OAAA1Z,EAAA,EACA,EACAwX,qBAAAA,CAAAvZ,GACAI,OAAAsb,OAAA,MACAjC,cAAAzZ,EAAA2b,OAAAC,QAAA,KAAAlC,eAAAja,KAAAqC,GAAAA,EAAAE,MAAA,GACAqX,oBAAA,EACAC,cAAAtZ,EAAA2b,OAAAC,SAEA,EACApC,aAAAA,CAAAqC,GACA,MAAAC,EAAAD,EAAA7H,OACA,KAAAsF,cAAAwC,IAAA,KAAApC,eAAA1F,OACA,KAAAqF,mBAAAyC,EAAA,GAAAA,EAAA,KAAApC,eAAA1F,MACA,EACA+F,sBAAAA,CAAA/Z,GACAI,OAAAsb,OAAA,MACAzB,eAAAja,EAAA2b,OAAAC,QAAA,KAAA1B,gBAAAza,KAAAqC,GAAAA,EAAAE,MAAA,GACA6X,qBAAA,EACAC,eAAA9Z,EAAA2b,OAAAC,SAEA,EACA5B,cAAAA,CAAA6B,GACA,MAAAC,EAAAD,EAAA7H,OACA,KAAA8F,eAAAgC,IAAA,KAAA5B,gBAAAlG,OACA,KAAA6F,oBAAAiC,EAAA,GAAAA,EAAA,KAAA5B,gBAAAlG,MACA,EACAwG,2BAAAA,CAAAxa,GACAI,OAAAsb,OAAA,MACAhB,oBAAA1a,EAAA2b,OAAAC,QAAA,KAAAjB,qBAAAlb,KAAAqC,GAAAA,EAAAE,MAAA,GACAsY,0BAAA,EACAC,oBAAAva,EAAA2b,OAAAC,SAEA,EACAnB,mBAAAA,CAAAoB,GACA,MAAAC,EAAAD,EAAA7H,OACA,KAAAuG,oBAAAuB,IAAA,KAAAnB,qBAAA3G,OACA,KAAAsG,yBAAAwB,EAAA,GAAAA,EAAA,KAAAnB,qBAAA3G,MACA,EACAsH,oBAAAA,GACA,KAAA9B,cAAA,KAAAC,eACA,KAAAO,eAAA,KAAAC,gBACA,KAAAQ,oBAAA,KAAAC,oBACA,EACAE,UAAAA,GACArX,aAAA0X,QAAA,gBAAA5X,KAAAwJ,UAAA,KAAA4M,gBACAlW,aAAA0X,QAAA,iBAAA5X,KAAAwJ,UAAA,KAAAoN,iBACA1W,aAAA0X,QAAA,sBAAA5X,KAAAwJ,UAAA,KAAA6N,sBAEA,KAAAqB,mBAAA,KAAAtC,cAAAha,KAAAqC,IAAA,CACAE,IAAAF,EACAT,MAAA,KAAAqY,eAAAjL,MAAAzM,GAAAA,EAAAA,MAAAF,IAAAT,UAEA,KAAA2a,oBAAA,KAAA/B,eAAAxa,KAAAqC,IAAA,CACAE,IAAAF,EACAT,MAAA,KAAA6Y,gBAAAzL,MAAAzM,GAAAA,EAAAA,MAAAF,IAAAT,UAEA,KAAA4a,yBAAA,KAAAvB,oBAAAjb,KAAAqC,IAAA,CACAE,IAAAF,EACAT,MAAA,KAAAsZ,qBAAAlM,MAAAzM,GAAAA,EAAAA,MAAAF,IAAAT,UAGA,MAAA6a,EAAA,KAAAxC,eAAAyC,QAAAra,IACA,SAAA2X,cAAAvP,QAAApI,EAAAE,OAEAoa,EAAA,KAAAlC,gBAAAiC,QAAAra,IACA,SAAAmY,eAAA/P,QAAApI,EAAAE,OAGAuB,aAAA0X,QAAA,sBAAA5X,KAAAwJ,UAAAqP,IACA3Y,aAAA0X,QAAA,uBAAA5X,KAAAwJ,UAAAuP,IAEA7Y,aAAA0X,QAAA,qBAAA5X,KAAAwJ,UAAA,KAAAkP,qBACAxY,aAAA0X,QAAA,sBAAA5X,KAAAwJ,UAAA,KAAAmP,sBACAzY,aAAA0X,QAAA,2BAAA5X,KAAAwJ,UAAA,KAAAoP,2BAEA1Y,aAAA0X,QAAA,WAAA5X,KAAAwJ,UAAA,KAAAsM,WACA5V,aAAA0X,QAAA,YAAA5X,KAAAwJ,UAAA,KAAA+M,YACArW,aAAA0X,QAAA,iBAAA5X,KAAAwJ,UAAA,KAAAwN,iBAEAnO,YAAA,KACA,KAAA2O,eACA,KAAAC,iBACA,KAAAC,uBACA7S,EAAA,KAAA0R,UAAA,KAAAO,iBAAAjR,MAAAC,IACApF,QAAAC,IAAAmF,EAAA,IAKA3E,EAAAA,EAAAuI,QAAA,WACA,IACA,KCnYqQ,MCQjQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BvM,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,qBAAqB,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI2I,QAAQ,cAAc3I,EAAI4I,SAAS,YAAa,EAAM,OAASC,GAAUA,EAAOtH,IAAI,OAAS,CAAEuH,EAAG,MAAOC,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,UAAU0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBC,MAAMrJ,EAAIsJ,qBAAqBT,GAAQzI,MAAM,CAAC,UAAY,GAAG,YAAc,QAAQ,SAAWyI,EAAOU,WAAWvI,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAIA,EAAOzE,KAAK0E,QAAQ,QAAQzJ,EAAI0J,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOjI,IAAI,SAAgB,KAAYvB,EAAI4J,oBAAoBf,EAAO,GAAGgB,MAAM,CAACC,MAAOjB,EAAOtE,QAASwF,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKpB,EAAQ,UAAWmB,EAAI,EAAEE,WAAW,oBAAoB,GAAG,CAAC3I,IAAI,SAAS0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOsB,WAAWjK,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,OAAO,CAACY,YAAY,YAAY,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOvI,MAAM,OAAOJ,EAAG,OAAO,CAACY,YAAY,WAAW,CAACd,EAAIe,GAAG,IAAIf,EAAI0B,GAAGmH,EAAOL,gBAAgB,GAAG,CAACjH,IAAI,OAAO0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,OAAO,CAACmJ,MAAM,CACztC,eAAgBR,EAAOU,UACvB,eAAsC,YAAtBV,EAAOuB,WACvB,aAAoC,SAAtBvB,EAAOuB,aACpB,CAACpK,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOwB,SAAS,GAAGrK,EAAImB,GAAInB,EAAIsK,WAAW,SAASjJ,GAAM,MAAO,CAACE,IAAIF,EAAKE,IAAI0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAIL,YAAY,CAAC,MAAQ,QAAQd,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOxH,EAAKE,QAAQ,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOxH,EAAKE,OAAO,OAAO,EAAE,KAAI,MAAK,MAAS,IAAI,EAC7U,EACIlB,GAAkB,GCmDtB,IACAC,KAAA,mBACAqD,IAAAA,GACA,OACA2G,UAAA,GACA3B,QAAA,CACA,CACA/H,MAAA,KACA6J,UAAA,SACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,WAEA,CACA/J,MAAA,KACA6J,UAAA,OACAC,MAAA,GACA3B,YAAA,CAAA4B,aAAA,SAEA,CACA/J,MAAA,KACA6J,UAAA,UACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,aAGA/B,SAAA,GACAgC,WAAA,GACAC,OAAA,GACAC,mBAAA,GACAC,UAAA,GACAC,iBAAA,GAEA,EACAC,OAAAA,GAEA,MAAAC,EAAApI,aAAAC,QAAA,4BACAmI,IACA,KAAAZ,UAAA1H,KAAAC,MAAAqI,GAAAlM,KAAAqC,IAAA,CACAE,IAAAF,EAAAE,IACAX,MAAAS,EAAAT,UAIA,KAAA0J,UAAAa,SAAA9J,IACA,KAAAsH,QAAAyC,KAAA,CACAxK,MAAAS,EAAAT,MACA6J,UAAApJ,EAAAE,IACAmJ,MAAA,IACA3B,YAAA,CAAA4B,aAAAtJ,EAAAE,MACA,IAGA,EACAb,QAAA,CAEA2K,eAAAA,CAAA9G,GACA,MAAA+G,EAAA,WACA,OAAAA,EAAAC,KAAAhH,EACA,EAEAiH,sBAAAA,CAAAjK,EAAAsC,GACA,KAAAoG,KAAA,KAAAe,iBAAAzJ,EAAAsC,GAEA4H,YAAA,KACA,KAAAxB,KAAA,KAAAe,iBAAAzJ,EAAA,QACA,IACA,EAGA+H,oBAAAA,CAAAT,GACA,OACA,iCAAAmC,iBAAAnC,EAAAtH,KACA,6BAAAyJ,iBAAAnC,EAAAtH,KAEA,EAGAqI,mBAAAA,CAAAf,GACA,IAAA5G,OAAAyJ,iBAIA,YAAAL,gBAAAxC,EAAAtE,cAMAsE,EAAAU,UACAxF,EAAAA,EAAA4H,QAAA,iBAIA,KAAAf,WAAAO,SAAA,CAAAS,EAAAtK,KACA,MAAAuK,EAAA,KAAAd,UAAAe,WAAAC,GAAAA,IAAAlD,EAAAsB,SACAyB,EAAAI,aAAAC,UAAAC,MAAA5K,IAAAuK,IACAD,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,YACA0B,SAAA,UACA0D,OAAAtB,EAAAvI,KACAiE,QAAAsE,EAAAtE,WAEA,KAAAiH,uBAAA3C,EAAAtH,IAAA,WAIA,MAvBAwC,EAAAA,EAAAV,MAAA,oBACA,KAAAmI,uBAAA3C,EAAAtH,IAAA,UALAU,OAAAyJ,kBAAA,CA6BA,EA0BAnB,cAAAA,CAAA1G,GACA,MAAAwI,EAAA,CACAC,QAAA,UACAC,KAAA,UACAC,QAAA,UACAC,QAAA,WAEA,OAAAJ,EAAAxI,IAAAwI,EAAAI,OACA,EAGAjC,aAAAA,CAAA3G,GACA,MAAA6I,EAAA,CACAJ,QAAA,KACAC,KAAA,KACAC,QAAA,MACAC,QAAA,OAEA,OAAAC,EAAA7I,IAAA6I,EAAAD,OACA,EAEAE,aAAAA,GACA,MAAAzB,EAAApI,aAAAC,QAAA,kBACA,KAAA+H,mBAAAlI,KAAAC,MAAAqI,GAAAlM,KAAAoD,GAAA,QAAAA,WACA,KAAA0I,mBAAAK,SAAA1F,IACA,IAEAmH,EAFAhB,EAAA,IAAAK,UAAAxG,GACAoH,EAAA,IAEA,MAAAC,EAAA,IACA,IAAAC,GAAA,EAEA,MAAAC,EAAAA,KACA,GAAApB,EAAAI,aAAAC,UAAAC,KAAA,CAEA,GADAW,GAAAC,EACAD,EAAA,IAIA,OAHAvJ,QAAAD,MAAA,oBAAAoC,MACA1B,EAAAA,EAAAV,MAAA,UAAAoC,sBACAmG,EAAAqB,QAGA3J,QAAA4J,KAAA,6BAAAL,UAAApH,MACA1B,EAAAA,EAAA4H,QAAA,UAAAlG,uBACAmH,EAAAnB,WAAAuB,EAAAH,EACA,MACAE,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,UACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,GAEAK,aAAAR,EACA,EAEAhB,EAAAyB,OAAA,KACA/J,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAAuI,QAAA,qBAAA7G,KACAsH,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,UACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,EACA,EAEAnB,EAAA0B,QAAAjK,IACAC,QAAAD,MAAA,kBAAAoC,MAAApC,GACAU,EAAAA,EAAAV,MAAA,UAAAoC,kBAAA,EAEAmG,EAAA2B,QAAA,KACAjK,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAA4H,QAAA,qBAAAlG,IAAA,EAEAmG,EAAA4B,UAAA,KAAAC,uBAEAb,EAAAnB,WAAAuB,EAAAH,GACA,KAAAjC,WAAAQ,KAAAQ,EAAA,GAEA,EAEA6B,sBAAAA,CAAAC,GACA,MAAA/J,EAAAf,KAAAC,MAAA6K,EAAA/J,MAGA,GAFAL,QAAAC,IAAAI,GAEA,cAAAA,EAAAoB,KAKA,OAAApB,EAAAoB,MACA,iBACA,IAAApB,EAAAgK,WACA5J,EAAAA,EAAAuI,QAAA,aAEAvI,EAAAA,EAAA4H,QAAA,GAAAhI,EAAAI,WAEA,MACA,iBACA,KAAA6J,mBAAAjK,GACA,MACA,iBACA,eACA,KAAAkK,iBAAAlK,GACA,MACA,gBACA,iBACA,eACA,KAAAmK,qBAAAnK,GACA,MACA,gBACA,KAAAoK,sBAAApK,GACA,MACA,QACAL,QAAA4J,KAAA,WAAAvJ,EAAAoB,WA5BA,KAAA8I,iBAAAlK,EA8BA,EAEAoK,qBAAAA,CAAApK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,IACA,IAAAlF,EAAAgK,YACA,KAAAnC,uBAAA3C,EAAAtH,IAAA,WACAsH,EAAAuB,WAAA,KACAvB,EAAAU,WAAA,EACAV,EAAAwB,KAAA,IAEA,KAAAmB,uBAAA3C,EAAAtH,IAAA,SACAwC,EAAAA,EAAAV,MAAA,WAAAM,EAAAI,YAEA,KAAAmK,eAEA,EACAN,kBAAAA,CAAAjK,GACA,MAAAwK,EAAAxK,EAAAsK,QAAAjP,KAAA,CAAAqC,EAAAC,KACA,MAAA8M,EAAA,CACA7M,IAAA,GAAAoC,EAAArD,QAAAe,KAAAC,IACAhB,KAAAe,EACA8I,OAAAxG,EAAArD,KACA+J,KAAA,EACA9F,QAAA,GACAiE,QAAA7E,EAAA6E,SAKA,OAHA,KAAA8B,UAAAa,SAAAkD,IACAD,EAAAC,EAAA9M,KAAA,QAEA6M,CAAA,IAEA,KAAAxF,SAAA,KAAAA,SAAA0F,OAAAH,GACA,KAAApD,UAAAK,KAAAzH,EAAArD,KACA,EAEAuN,gBAAAA,CAAAlK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MACA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SACA5M,EAAAkD,UAAAZ,EAAAY,UAGAsE,IACA,cAAAlF,EAAAoB,MACA8D,EAAAU,WAAA,EACAV,EAAAuB,WAAA,UACA,KAAAmE,WAAA5K,GAEA,KAAAuK,iBAEArF,EAAAU,WAAA,EACAV,EAAAuB,WAAA,eAAAzG,EAAAoB,KAAA,iBACA,KAAAkF,KAAApB,EAAA,aAAAA,EAAAuB,YACA,KAAAoE,UAAA7K,GAEA8H,YAAA,KACA,eAAA9H,EAAAoB,OACA8D,EAAAtE,QAAA,IAEA,KAAA0F,KAAApB,EAAA,UAAAA,EAAAtE,QAAA,GACA,KAEA,KAAA2J,gBAGA,EACAJ,oBAAAA,CAAAnK,GACA,MAAA8K,EAAA,KAAA7F,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAkK,IACA,cAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GACA,eAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,IAEAA,EAAA9K,EAAAtC,MAAA,OACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GAGA,EACAF,UAAAA,CAAA5K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACA,GAAAsE,IAAA,KAAAgC,OAAAhC,EAAAtH,KAAA,CACA,IAAAmN,EAAA,EACA,KAAA7D,OAAAhC,EAAAtH,KAAAoN,aAAA,KACAD,IACA7F,EAAAwB,KAAA,KAAAuE,WAAAF,GACA,KAAA9F,SAAAC,EAAAtH,KAAA8I,KAAAxB,EAAAwB,IAAA,GACA,IACA,CACA,EAEAmE,SAAAA,CAAA7K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,GAAA,KAAAgC,OAAAhC,EAAAtH,OACAsN,cAAA,KAAAhE,OAAAhC,EAAAtH,aACA,KAAAsJ,OAAAhC,EAAAtH,KAEA,EAEAqN,UAAAA,CAAAF,GACA,MAAAI,EAAAC,KAAAC,MAAAN,EAAA,IACAO,EAAAP,EAAA,GACA,SAAAI,EAAAI,WAAAC,SAAA,UAAAF,EAAAC,WAAAC,SAAA,QACA,GAEA3O,OAAAA,GACAiL,YAAA,KACA,KAAAkB,eAAA,GACA,IACA,EACAyC,aAAAA,GACAzP,OAAAD,KAAA,KAAAmL,QAAAM,SAAA5J,IACAsN,cAAA,KAAAhE,OAAAtJ,GAAA,IAEA,KAAAqJ,WAAAO,SAAAS,IACAA,GACAA,EAAAqB,OACA,GAEA,GC7a0Q,MCQtQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BlN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,qBAAqB,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAI2I,QAAQ,cAAc3I,EAAI4I,SAAS,YAAa,EAAM,OAASC,GAAUA,EAAOtH,IAAI,OAAS,CAAEuH,EAAG,MAAOC,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,UAAU0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBC,MAAMrJ,EAAIsJ,qBAAqBT,GAAQzI,MAAM,CAAC,UAAY,GAAG,YAAc,QAAQ,SAAWyI,EAAOU,WAAWvI,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAIA,EAAOzE,KAAK0E,QAAQ,QAAQzJ,EAAI0J,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOjI,IAAI,SAAgB,KAAYvB,EAAI4J,oBAAoBf,EAAO,GAAGgB,MAAM,CAACC,MAAOjB,EAAOtE,QAASwF,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKpB,EAAQ,UAAWmB,EAAI,EAAEE,WAAW,oBAAoB,GAAG,CAAC3I,IAAI,SAAS0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOsB,WAAWjK,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,OAAO,CAACY,YAAY,YAAY,CAACd,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOvI,MAAM,OAAOJ,EAAG,OAAO,CAACY,YAAY,WAAW,CAACd,EAAIe,GAAG,IAAIf,EAAI0B,GAAGmH,EAAOL,gBAAgB,GAAG,CAACjH,IAAI,OAAO0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,OAAO,CAACmJ,MAAM,CACztC,eAAgBR,EAAOU,UACvB,eAAsC,YAAtBV,EAAOuB,WACvB,aAAoC,SAAtBvB,EAAOuB,aACpB,CAACpK,EAAIe,GAAGf,EAAI0B,GAAGmH,EAAOwB,SAAS,GAAGrK,EAAImB,GAAInB,EAAIsK,WAAW,SAASjJ,GAAM,MAAO,CAACE,IAAIF,EAAKE,IAAI0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,QAAQ,CAACqB,IAAIF,EAAKE,IAAIL,YAAY,CAAC,MAAQ,QAAQd,MAAM,CAAC,MAAQJ,EAAIuK,eAAe1B,EAAOxH,EAAKE,QAAQ,CAACvB,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAIwK,cAAc3B,EAAOxH,EAAKE,OAAO,OAAO,EAAE,KAAI,MAAK,MAAS,IAAI,EAC7U,EACIlB,GAAkB,GCmDtB,IACAC,KAAA,mBACAqD,IAAAA,GACA,OACA2G,UAAA,GACA3B,QAAA,CACA,CACA/H,MAAA,KACA6J,UAAA,SACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,WAEA,CACA/J,MAAA,KACA6J,UAAA,OACAC,MAAA,GACA3B,YAAA,CAAA4B,aAAA,SAEA,CACA/J,MAAA,KACA6J,UAAA,UACAC,MAAA,IACA3B,YAAA,CAAA4B,aAAA,aAGA/B,SAAA,GACAgC,WAAA,GACAC,OAAA,GACAC,mBAAA,GACAC,UAAA,GACAC,iBAAA,GAEA,EACAC,OAAAA,GAEA,MAAAC,EAAApI,aAAAC,QAAA,uBACAmI,IACA,KAAAZ,UAAA1H,KAAAC,MAAAqI,GAAAlM,KAAAqC,IAAA,CACAE,IAAAF,EAAAE,IACAX,MAAAS,EAAAT,UAIA,KAAA0J,UAAAa,SAAA9J,IACA,KAAAsH,QAAAyC,KAAA,CACAxK,MAAAS,EAAAT,MACA6J,UAAApJ,EAAAE,IACAmJ,MAAA,IACA3B,YAAA,CAAA4B,aAAAtJ,EAAAE,MACA,IAGA,EACAb,QAAA,CAEA2K,eAAAA,CAAA9G,GACA,MAAA+G,EAAA,WACA,OAAAA,EAAAC,KAAAhH,EACA,EACAiH,sBAAAA,CAAAjK,EAAAsC,GACA,KAAAoG,KAAA,KAAAe,iBAAAzJ,EAAAsC,GACA4H,YAAA,KACA,KAAAxB,KAAA,KAAAe,iBAAAzJ,EAAA,QACA,IACA,EAEA+H,oBAAAA,CAAAT,GACA,OACA,iCAAAmC,iBAAAnC,EAAAtH,KACA,6BAAAyJ,iBAAAnC,EAAAtH,KAEA,EAEAqI,mBAAAA,CAAAf,GACA,IAAA5G,OAAAyJ,iBAIA,YAAAL,gBAAAxC,EAAAtE,cAMAsE,EAAAU,UACAxF,EAAAA,EAAA4H,QAAA,iBAIA,KAAAf,WAAAO,SAAA,CAAAS,EAAAtK,KACA,MAAAuK,EAAA,KAAAd,UAAAe,WAAAC,GAAAA,IAAAlD,EAAAsB,SACAyB,EAAAI,aAAAC,UAAAC,MAAA5K,IAAAuK,IACAD,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,YACA0B,SAAA,QACA0D,OAAAtB,EAAAvI,KACAiE,QAAAsE,EAAAtE,WAEA,KAAAiH,uBAAA3C,EAAAtH,IAAA,WAIA,MAvBAwC,EAAAA,EAAAV,MAAA,oBACA,KAAAmI,uBAAA3C,EAAAtH,IAAA,UALAU,OAAAyJ,kBAAA,CA6BA,EA2BAnB,cAAAA,CAAA1G,GACA,MAAAwI,EAAA,CACAC,QAAA,UACAC,KAAA,UACAC,QAAA,UACAC,QAAA,WAEA,OAAAJ,EAAAxI,IAAAwI,EAAAI,OACA,EAGAjC,aAAAA,CAAA3G,GACA,MAAA6I,EAAA,CACAJ,QAAA,KACAC,KAAA,KACAC,QAAA,MACAC,QAAA,OAEA,OAAAC,EAAA7I,IAAA6I,EAAAD,OACA,EAEAE,aAAAA,GACA,MAAAzB,EAAApI,aAAAC,QAAA,aACA,KAAA+H,mBAAAlI,KAAAC,MAAAqI,GAAAlM,KAAAoD,GAAA,QAAAA,WACA,KAAA0I,mBAAAK,SAAA1F,IACA,IAEAmH,EAFAhB,EAAA,IAAAK,UAAAxG,GACAoH,EAAA,IAEA,MAAAC,EAAA,IACA,IAAAC,GAAA,EAEA,MAAAC,EAAAA,KACA,GAAApB,EAAAI,aAAAC,UAAAC,KAAA,CAEA,GADAW,GAAAC,EACAD,EAAA,IAIA,OAHAvJ,QAAAD,MAAA,oBAAAoC,MACA1B,EAAAA,EAAAV,MAAA,UAAAoC,sBACAmG,EAAAqB,QAGA3J,QAAA4J,KAAA,6BAAAL,UAAApH,MACA1B,EAAAA,EAAA4H,QAAA,UAAAlG,uBACAmH,EAAAnB,WAAAuB,EAAAH,EACA,MACAE,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,QACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,GAEAK,aAAAR,EACA,EAEAhB,EAAAyB,OAAA,KACA/J,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAAuI,QAAA,qBAAA7G,KACAsH,IACAnB,EAAAO,KAAAvJ,KAAAwJ,UAAA,CACArH,KAAA,aACA0B,SAAA,QACA0G,MAAA,KAAA7C,UAAAtL,KAAAqC,GAAAA,EAAAE,SAEAwL,GAAA,EACA,EAEAnB,EAAA0B,QAAAjK,IACAC,QAAAD,MAAA,kBAAAoC,MAAApC,GACAU,EAAAA,EAAAV,MAAA,UAAAoC,kBAAA,EAEAmG,EAAA2B,QAAA,KACAjK,QAAAC,IAAA,qBAAAkC,KACA1B,EAAAA,EAAA4H,QAAA,qBAAAlG,IAAA,EAEAmG,EAAA4B,UAAA,KAAAC,uBAEAb,EAAAnB,WAAAuB,EAAAH,GACA,KAAAjC,WAAAQ,KAAAQ,EAAA,GAEA,EAEA6B,sBAAAA,CAAAC,GACA,MAAA/J,EAAAf,KAAAC,MAAA6K,EAAA/J,MAGA,GAFAL,QAAAC,IAAAI,GAEA,cAAAA,EAAAoB,KAKA,OAAApB,EAAAoB,MACA,iBACA,IAAApB,EAAAgK,WACA5J,EAAAA,EAAAuI,QAAA,aAEAvI,EAAAA,EAAA4H,QAAA,GAAAhI,EAAAI,WAEA,MACA,iBACA,KAAA6J,mBAAAjK,GACA,MACA,iBACA,eACA,KAAAkK,iBAAAlK,GACA,MACA,gBACA,iBACA,eACA,KAAAmK,qBAAAnK,GACA,MACA,gBACA,KAAAoK,sBAAApK,GACA,MACA,QACAL,QAAA4J,KAAA,WAAAvJ,EAAAoB,WA5BA,KAAA8I,iBAAAlK,EA8BA,EACAoK,qBAAAA,CAAApK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,IACA,IAAAlF,EAAAgK,YACA,KAAAnC,uBAAA3C,EAAAtH,IAAA,WACAsH,EAAAuB,WAAA,KACAvB,EAAAU,WAAA,EACAV,EAAAwB,KAAA,IAEA,KAAAmB,uBAAA3C,EAAAtH,IAAA,SACAwC,EAAAA,EAAAV,MAAA,WAAAM,EAAAI,YAEA,KAAAmK,eAEA,EACAN,kBAAAA,CAAAjK,GACA,MAAAwK,EAAAxK,EAAAsK,QAAAjP,KAAA,CAAAqC,EAAAC,KACA,MAAA8M,EAAA,CACA7M,IAAA,GAAAoC,EAAArD,QAAAe,KAAAC,IACAhB,KAAAe,EACA8I,OAAAxG,EAAArD,KACA+J,KAAA,EACA9F,QAAA,GACAiE,QAAA7E,EAAA6E,SAKA,OAHA,KAAA8B,UAAAa,SAAAkD,IACAD,EAAAC,EAAA9M,KAAA,QAEA6M,CAAA,IAEA,KAAAxF,SAAA,KAAAA,SAAA0F,OAAAH,GACA,KAAApD,UAAAK,KAAAzH,EAAArD,KACA,EAEAuN,gBAAAA,CAAAlK,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MACA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SACA5M,EAAAkD,UAAAZ,EAAAY,UAGAsE,IACA,cAAAlF,EAAAoB,MACA8D,EAAAU,WAAA,EACAV,EAAAuB,WAAA,UACA,KAAAmE,WAAA5K,GAEA,KAAAuK,iBAEArF,EAAAU,WAAA,EACAV,EAAAuB,WAAA,eAAAzG,EAAAoB,KAAA,iBACA,KAAAkF,KAAApB,EAAA,aAAAA,EAAAuB,YACA,KAAAoE,UAAA7K,GAEA8H,YAAA,KACA,eAAA9H,EAAAoB,OACA8D,EAAAtE,QAAA,IAEA,KAAA0F,KAAApB,EAAA,UAAAA,EAAAtE,QAAA,GACA,KAEA,KAAA2J,gBAGA,EACAJ,oBAAAA,CAAAnK,GACA,MAAA8K,EAAA,KAAA7F,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAkK,IACA,cAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GACA,eAAA9K,EAAAoB,MACA0J,EAAA9K,EAAAtC,MAAA,UACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,IAEAA,EAAA9K,EAAAtC,MAAA,OACA,KAAAuH,SAAA6F,EAAAlN,KAAAkN,GAGA,EACAF,UAAAA,CAAA5K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACA,GAAAsE,IAAA,KAAAgC,OAAAhC,EAAAtH,KAAA,CACA,IAAAmN,EAAA,EACA,KAAA7D,OAAAhC,EAAAtH,KAAAoN,aAAA,KACAD,IACA7F,EAAAwB,KAAA,KAAAuE,WAAAF,GACA,KAAA9F,SAAAC,EAAAtH,KAAA8I,KAAAxB,EAAAwB,IAAA,GACA,IACA,CACA,EAEAmE,SAAAA,CAAA7K,GACA,MAAAkF,EAAA,KAAAD,SAAAoF,MAAA3M,GAAAA,EAAAf,OAAAqD,EAAAsK,SAAA5M,EAAAkD,UAAAZ,EAAAY,UACAsE,GAAA,KAAAgC,OAAAhC,EAAAtH,OACAsN,cAAA,KAAAhE,OAAAhC,EAAAtH,aACA,KAAAsJ,OAAAhC,EAAAtH,KAEA,EAEAqN,UAAAA,CAAAF,GACA,MAAAI,EAAAC,KAAAC,MAAAN,EAAA,IACAO,EAAAP,EAAA,GACA,SAAAI,EAAAI,WAAAC,SAAA,UAAAF,EAAAC,WAAAC,SAAA,QACA,GAEA3O,OAAAA,GACAiL,YAAA,KACA,KAAAkB,eAAA,GACA,IACA,EACAyC,aAAAA,GACAzP,OAAAD,KAAA,KAAAmL,QAAAM,SAAA5J,IACAsN,cAAA,KAAAhE,OAAAtJ,GAAA,IAEA,KAAAqJ,WAAAO,SAAAS,IACAA,GACAA,EAAAqB,OACA,GAEA,GCzawQ,MCQpQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BlN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,MAAM,CAACY,YAAY,4BAA4B,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAI4b,QAAQ,OAAS,eAAe,CAAC1b,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,YAAY,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,UAAU,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,WAAa,CAAE0L,UAAWzP,EAAI6b,sBAAwB3R,WAAW,qGAAqG9J,MAAM,CAAC,YAAc,mBAAmB,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,gBAAgB,CAACE,MAAM,CAAC,QAAU,KAAK,CAACF,EAAG,iBAAiB,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,QAAS,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,SAAU,CAAE0L,UAAWzP,EAAI8V,kBAAqB5L,WAAW,8FAA8F9J,MAAM,CAAC,IAAM,EAAE,IAAM,QAAU,IAAI,IAAI,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,YAAY,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,UAAU,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,WAAY,CAAE0L,UAAWzP,EAAI6V,mBAAqB3L,WAAW,iGAAiG9J,MAAM,CAAC,YAAc,gBAAgB,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,gBAAgB,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,cAAe,CAAE8F,aAAc5P,EAAI8b,cAAgB5R,WAAW,mDAAmD9J,MAAM,CAAC,YAAc,iBAAiB,OAASJ,EAAI0P,OAAO,OAAS,iBAAiB,IAAI,GAAGxP,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAAC2J,MAAM,CAACC,MAAO9J,EAAIiG,KAAM8D,SAAS,SAAUC,GAAMhK,EAAIiG,KAAK+D,CAAG,EAAEE,WAAW,SAAS,CAAClK,EAAIe,GAAG,eAAe,IAAI,GAAGb,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,QAAQ,CAAE0F,MAAO,CAAC,CAAEoG,UAAU,EAAM7R,QAAS,SAAU,CAAE0L,UAAWzP,EAAI6V,mBAAqB3L,WAAW,6FAA6F9J,MAAM,CAAC,YAAc,gBAAgB,UAAY,OAAO,IAAI,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,eAAgBI,WAAW,oBAAoB9J,MAAM,CAAC,YAAc,QAAQ,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,cAAeI,WAAW,mBAAmB9J,MAAM,CAAC,YAAc,QAAQ,UAAY,MAAM,IAAI,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,WAAY,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAsB3L,WAAW,6DAA6D9J,MAAM,CAAC,YAAc,QAAQ,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,aAAcI,WAAW,kBAAkB9J,MAAM,CAAC,YAAc,QAAQ,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACgB,YAAY,CAAC,aAAa,SAASd,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACwU,MAAO,CAAEhK,MAAO,QAASqR,OAAQ,OAAQC,SAAU,QAAU5b,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAIic,gBAAgB,CAACjc,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,GAAGb,EAAG,SAAS,CAACY,YAAY,cAAcV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIiW,UAAU,OAAS,UAAUjV,GAAG,CAAC,OAAShB,EAAIsP,cAAc,CAACpP,EAAG,QAAQ,CAACgB,YAAY,CAAC,OAAS,QAAQ,mBAAmB,UAAU,QAAU,OAAO,cAAc,WAAW,CAAChB,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,UAAU,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAqB3L,WAAW,0DAA0D9J,MAAM,CAAC,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,UAAU,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAqB3L,WAAW,0DAA0D9J,MAAM,CAAC,UAAY,GAAG,WAAa,CAAEsU,MAAO,CAAEhK,MAAO,cAAgB,IAAI,GAAGxK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,QAAQ,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAqB3L,WAAW,wDAAwD9J,MAAM,CAAC,UAAY,OAAO,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,eAAgBI,WAAW,oBAAoB9J,MAAM,CAAC,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,WAAY,CAAE0F,MAAO,CAAC,CAAEC,UAAWzP,EAAI6V,mBAAsB3L,WAAW,6DAA6D9J,MAAM,CAAC,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,gBAAgB,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,eAAgBI,WAAW,oBAAoB9J,MAAM,CAAC,YAAc,OAAO,OAASJ,EAAI0P,OAAO,OAAS,iBAAiB,IAAI,GAAGxP,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,cAAeI,WAAW,mBAAmB9J,MAAM,CAAC,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,YAAY8I,QAAQ,cAAcU,MAAO,CAAC,aAAcI,WAAW,kBAAkB9J,MAAM,CAAC,UAAY,MAAM,IAAI,GAAGF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,UAAUY,GAAG,CAAC,MAAQhB,EAAIsP,cAAc,CAACtP,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,GAAGb,EAAG,UAAU,CAACgB,YAAY,CAAC,aAAa,QAAQd,MAAM,CAAC,OAAS,MAAM,QAAUJ,EAAI2I,QAAQ,WAAa3I,EAAIkc,UAAU,WAAalc,EAAIiQ,YAAYjP,GAAG,CAAC,OAAShB,EAAIkQ,mBAAmBnH,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,OAAO0H,GAAG,SAASC,GAAM,MAAO,CAAChJ,EAAG,QAAQ,CAACE,MAAM,CAAC,MAAQ8I,EAAO,QAAU,QAAQ,CAAClJ,EAAIe,GAAG,IAAIf,EAAI0B,GAAGwH,EAAO,MAAQ,OAAO,OAAO,GAAG,CAAC3H,IAAI,YAAY0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,WAAW,CAACY,YAAY,gBAAgBV,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAImc,WAAWtT,EAAO,IAAI,CAAC7I,EAAIe,GAAG,SAAS,QAAQ,GAAGb,EAAG,mBAAmB,CAACqW,IAAI,mBAAmBnW,MAAM,CAAC,eAAiBJ,EAAIuE,QAAQ,eAAiBvE,EAAIyF,QAAQ,aAAezF,EAAI0F,MAAM,mBAAqB1F,EAAI2F,YAAY,gBAAkB3F,EAAI4F,SAAS,mBAAqB5F,EAAI6F,YAAY,kBAAoB7F,EAAI8F,WAAW,iBAAmB9F,EAAI+F,UAAU,YAAc/F,EAAIoc,WAAW,EAC79P,EACI/b,GAAkB,GCFlBN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,SAAS,QAAUJ,EAAI0W,QAAQ,kBAAkB1W,EAAI2W,eAAe,OAAS,KAAK,MAAQ,SAAS,OAAS,UAAU3V,GAAG,CAAC,GAAKhB,EAAI4B,SAAS,OAAS5B,EAAI6B,eAAe,CAAC3B,EAAG,SAAS,CAACE,MAAM,CAAC,SAAWJ,EAAIgQ,UAAU,CAAC9P,EAAG,MAAM,CAACY,YAAY,+BAA+B,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAI4W,WAAW,OAAS,eAAe,CAAC1W,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAO9X,QAAU,QAAU,GAAG,KAAOvE,EAAIqc,OAAO9X,UAAU,CAACrE,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,mBAAmB,SAAW,GAAG,UAAY,GAAG,UAAW,GAAMY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,UAAW,CAAC,GAAI,IAAI,GAAGzS,MAAM,CAACC,MAAO9J,EAAIuc,aAAcxS,SAAS,SAAUC,GAAMhK,EAAIuc,aAAavS,CAAG,EAAEE,WAAW,mBAAmB,IAAI,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAO5W,QAAU,QAAU,GAAG,KAAOzF,EAAIqc,OAAO5W,UAAU,CAACvF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,gBAAgB,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,UAAW,GAAG,GAAGzS,MAAM,CAACC,MAAO9J,EAAIwc,aAAczS,SAAS,SAAUC,GAAMhK,EAAIwc,aAAaxS,CAAG,EAAEE,WAAW,mBAAmB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAOxW,YAAc,QAAU,GAAG,KAAO7F,EAAIqc,OAAOxW,cAAc,CAAC3F,EAAG,gBAAgB,CAACE,MAAM,CAAC,YAAc,kBAAkBY,GAAG,CAAC,OAAShB,EAAIyc,cAAc5S,MAAM,CAACC,MAAO9J,EAAI0c,iBAAkB3S,SAAS,SAAUC,GAAMhK,EAAI0c,iBAAiB1S,CAAG,EAAEE,WAAW,uBAAuB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,aAAa,CAAC2J,MAAM,CAACC,MAAO9J,EAAI2c,UAAW5S,SAAS,SAAUC,GAAMhK,EAAI2c,UAAU3S,CAAG,EAAEE,WAAW,cAAc,CAAClK,EAAIe,GAAG,eAAe,IAAI,GAAGb,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,MAAM,kBAAkBJ,EAAIqc,OAAO3W,MAAQ,QAAU,GAAG,KAAO1F,EAAIqc,OAAO3W,QAAQ,CAACxF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,gBAAgB,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,QAAS,GAAG,GAAGzS,MAAM,CAACC,MAAO9J,EAAI4c,WAAY7S,SAAS,SAAUC,GAAMhK,EAAI4c,WAAW5S,CAAG,EAAEE,WAAW,iBAAiB,IAAI,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAO1W,YAAc,QAAU,GAAG,KAAO3F,EAAIqc,OAAO1W,cAAc,CAACzF,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,UAAY,GAAGY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,cAAe,GAAG,EAAM,GAAGzS,MAAM,CAACC,MAAO9J,EAAI6c,iBAAkB9S,SAAS,SAAUC,GAAMhK,EAAI6c,iBAAiB7S,CAAG,EAAEE,WAAW,uBAAuB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAOvW,WAAa,QAAU,GAAG,KAAO9F,EAAIqc,OAAOvW,aAAa,CAAC5F,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,UAAY,GAAGY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,aAAc,GAAG,EAAM,GAAGzS,MAAM,CAACC,MAAO9J,EAAI8c,gBAAiB/S,SAAS,SAAUC,GAAMhK,EAAI8c,gBAAgB9S,CAAG,EAAEE,WAAW,sBAAsB,IAAI,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAOzW,SAAW,QAAU,GAAG,KAAO5F,EAAIqc,OAAOzW,WAAW,CAAC1F,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,UAAY,GAAGY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,WAAY,GAAG,EAAM,GAAGzS,MAAM,CAACC,MAAO9J,EAAI+c,cAAehT,SAAS,SAAUC,GAAMhK,EAAI+c,cAAc/S,CAAG,EAAEE,WAAW,oBAAoB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,IAAM,OAAOd,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIqc,OAAOtW,UAAY,QAAU,GAAG,KAAO/F,EAAIqc,OAAOtW,YAAY,CAAC7F,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,UAAY,GAAGY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIsc,cAAc,YAAa,GAAG,EAAM,GAAGzS,MAAM,CAACC,MAAO9J,EAAIgd,eAAgBjT,SAAS,SAAUC,GAAMhK,EAAIgd,eAAehT,CAAG,EAAEE,WAAW,qBAAqB,IAAI,GAAGhK,EAAG,QAAQ,CAACgB,YAAY,CAAC,aAAa,SAASd,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,WAAW,CAACwU,MAAO,CAAEhK,MAAO,QAASqR,OAAQ,OAAQC,SAAU,QAAU5b,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAIid,mBAAmB,CAACjd,EAAIe,GAAG,WAAW,IAAI,IAAI,IAAI,MAAM,IAAI,EACjwJ,EACIV,GAAkB,G,kCC4KtB,IACAC,KAAA,mBACAyW,MAAA,CACAmG,eAAA,CAAAnY,KAAAkS,OAAArB,UAAA,GACAuH,eAAA,CAAApY,KAAAkS,OAAArB,UAAA,GACAwH,aAAA,CAAArY,KAAAkS,OAAArB,UAAA,GACAyH,mBAAA,CAAAtY,KAAAkS,OAAArB,UAAA,GACA0H,gBAAA,CAAAvY,KAAAkS,OAAArB,UAAA,GACA2H,mBAAA,CAAAxY,KAAAkS,OAAArB,UAAA,GACA4H,kBAAA,CAAAzY,KAAAkS,OAAArB,UAAA,GACA6H,iBAAA,CAAA1Y,KAAAkS,OAAArB,UAAA,GACA8H,YAAA,CAAA3Y,KAAAoS,QAAAvB,UAAA,IAEAjS,IAAAA,GACA,OACA4Y,aAAA,KAAAW,eACAV,aAAA,KAAAW,eACAP,WAAA,KAAAQ,aACAP,iBAAA,KAAAQ,mBACAN,cAAA,KAAAO,gBACAZ,iBAAAiB,KAAA,KAAAJ,oBACAT,gBAAA,KAAAU,kBACAR,eAAA,KAAAS,iBACAd,UAAA,KAAAe,YACA9G,WAAA,KAAAxE,MAAAC,WAAA,MACAqE,SAAA,EACAC,gBAAA,EACA3G,SAAA,EACAqM,OAAA,CACA9X,QAAA,GACAkB,QAAA,GACAC,MAAA,GACAC,YAAA,GACAC,SAAA,GACAC,YAAA,GACAC,WAAA,GACAC,UAAA,IAGA,EACAkF,OAAAA,GACA,MAAA2S,EAAA,CACA,qDACA,iDAGAA,EAAAzS,SAAA0S,IACA,KAAAvG,QAAAhP,IAAA,WAAAuV,aAAAC,IACA,MAAAC,EAAA,WAAAF,EAAA,OAAAA,EAEA,aAAAE,KADA,gBAAAF,EACAF,KAAAG,GAEAA,CACA,GACA,GAEA,EACApd,QAAA,CACA4b,aAAAA,CAAAuB,EAAAG,EAAAC,GAAA,GACA,MAAAnU,EAAA,aAAA+T,KACA/T,EAIAmU,IAAA,QAAA1S,KAAAzB,GACA,KAAAuS,OAAAwB,GAAA,UACAK,MAAAC,QAAAH,GACAA,EAAAI,SAAAtU,EAAAyJ,QAGA,KAAA8I,OAAAwB,GAAA,GAFA,KAAAxB,OAAAwB,GAAA,MAAAG,EAAAK,KAAA,UAIAvU,EAAAyJ,SAAAyK,GAAAC,EACA,KAAA5B,OAAAwB,GAAA,MAAAG,OACAlU,EAAAyJ,OAAAyK,IAAAC,EACA,KAAA5B,OAAAwB,GAAA,QAAAG,IAEA,KAAA3B,OAAAwB,GAAA,GAhBA,YAAAA,IACA,KAAAxB,OAAAwB,GAAA,UAiBA,EACApB,YAAAA,GACA,KAAAC,iBAGA,KAAAL,OAAAxW,YAAA,GAFA,KAAAwW,OAAAxW,YAAA,OAIA,EACAyY,iBAAAA,GACA,KAAAhC,cAAA,mBACA,KAAAA,cAAA,cACA,KAAAA,cAAA,YACA,KAAAA,cAAA,oBACA,KAAAA,cAAA,iBACA,KAAAA,cAAA,mBACA,KAAAA,cAAA,kBACA,KAAAG,cACA,EACAQ,gBAAAA,GAGA,GAFA,KAAAqB,oBAEA3e,OAAAmU,OAAA,KAAAuI,QAAAkC,MAAAlb,GAAA,KAAAA,IAEA,YADA,KAAA2Q,SAAA3Q,MAAA,eAIA,SAAAkZ,aAEA,YADA,KAAAvI,SAAA3Q,MAAA,YAIA,MAAAmb,EAAA,KAAA9B,iBAAA+B,OAAA,cACAvY,EACA,KAAAqW,aACA,KAAAC,aACA,KAAAI,WACA,KAAAC,iBACA,KAAAE,cACAyB,EACA,KAAA1B,gBACA,KAAAE,eACA,KAAAL,WACAlU,MAAAC,IACApF,QAAAC,IAAAmF,GACA,IAAAA,EAAAiF,aACA,KAAAqG,SAAA1H,QAAA,CACAmL,QAAA,OACAvT,SAAA,IAEA,KAAAsT,MAAA,gBAAA9O,EAAAiF,YACA,KAAA9L,eACA,IACAoS,OAAA5Q,IACAW,EAAAA,EAAAX,MAAA,CACAU,QAAA,OACAE,YAAAZ,EAAAK,SAAAC,KAAAI,QACAG,SAAA,GACA,GAEA,EACAjD,SAAAA,GACA,KAAAyV,SAAA,CACA,EACA7U,YAAAA,GACA,KAAA6U,SAAA,EACA,KAAAE,WAAA7C,cACApU,OAAAD,KAAA,KAAA2c,QAAAlR,SAAA5J,GAAA,KAAA8a,OAAA9a,GAAA,IACA,EACAK,QAAAA,GACA,KAAAgV,WAAAc,QACA,ICnU+P,MCQ3P,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QC4IhC,IACAnX,WAAA,CACAme,iBAAAA,IAEA/a,IAAAA,GACA,OACAmY,YAAA,KACAvX,QAAA,GACAkB,QAAA,GACAC,MAAA,GACAC,YAAA,GACAC,SAAA,GACAC,YAAA,GACAC,WAAA,GACAC,UAAA,GACAqW,QAAA,EACA1M,OAAA,CACAa,KAAA,CACAC,YAAA,QACAC,gBAAA,IACAC,iBAAA,IACAC,eAAA,IACAC,gBAAA,IACAC,iBAAA,gBACAC,MAAA,KACAC,IAAA,KACAC,GAAA,KACAC,MAAA,KACAC,SAAA,KACAC,SAAA,KACAC,UAAA,KACAC,UAAA,KACAC,YAAA,OACAC,WAAA,OACAC,aAAA,OACAC,WAAA,QACAC,YAAA,MACAC,WAAA,aACAC,UAAA,KACAC,eAAA,sBACAC,WAAA,WACAC,aAAA,MACAC,SAAA,QACAC,GAAA,KACAC,GAAA,MAEAC,iBAAA,CACA3B,YAAA,UAGAmO,MAAA,GACAnG,MAAA,GACAoG,MAAA,GACA3I,UAAA,KAAA7D,MAAAC,WAAA,MACAuJ,QAAA,KAAAxJ,MAAAC,WAAA,MACApM,MAAA,EACA0C,QAAA,CACA,CAAA/H,MAAA,OAAA6J,UAAA,YAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,UAAAC,MAAA,KACA,CAAA9J,MAAA,KAAA6J,UAAA,UAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,kBAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,eAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,iBAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,mBAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,kBAAAC,MAAA,KACA,CAAA9J,MAAA,OAAA6J,UAAA,OAAA1B,YAAA,CAAA4B,aAAA,QAAAD,MAAA,KACA,CAAA9J,MAAA,KAAAW,IAAA,YAAAwH,YAAA,CAAA4B,aAAA,aAAAD,MAAA,MAEAwR,UAAA,GACAjM,WAAA,CACAqC,QAAA,EACAC,SAAA,GACAC,gBAAA,uBACAC,UAAAA,CAAAC,EAAAC,IACAA,EAAA,OAAAA,EAAA,QAAAD,EAAA,IAEAE,iBAAA,EACAC,iBAAA,EACAH,MAAA,GAEAmM,SAAA,GAEA,EACAre,OAAAA,GACA,KAAAsb,YAAA,KAAAgD,sBACA,KAAA1W,WACA,EACA1H,QAAA,CACAoe,mBAAAA,GACA,IAAAC,EAAApB,OAAAc,OAAA,cACAO,EAAArB,KAAAoB,EAAA,cACA,OAAAC,EAAAC,UACAD,EAEA,IACA,EACA5W,SAAAA,GACA,KAAA6N,UAAA9C,gBAAA,CAAAU,EAAAC,KACA,IAAAD,EAAA,CACA,IAAA2K,OACA5a,IAAAkQ,EAAAjO,aAAA,OAAAiO,EAAAjO,cACA,KAAAgZ,SAAA/K,EAAAjO,YACA2Y,EAAA,KAAAK,SAAAJ,OAAA,eAEAjZ,EAAAsO,EAAAvP,QAAAuP,EAAArO,QAAAqO,EAAApO,MAAAoO,EAAAnO,YAAAmO,EAAAlO,SAAA4Y,EAAA1K,EAAAhO,WAAAgO,EAAA/N,UAAA,KAAAkK,WAAAqC,QAAA,KAAArC,WAAAsC,UAAA9J,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAuH,WAAAyC,MAAAhK,EAAAwL,WAAA,KAAAjE,WAAAsC,SACA,KAAA2J,UAAAxT,EAAAyL,QAAAnV,KAAA,CAAAqC,EAAAC,KAAA,CACAC,IAAAD,EACA4d,UAAA7d,EAAAkD,QACAkB,QAAApE,EAAAoE,QACA0Z,QAAA9d,EAAAqE,MACA0Z,gBAAA/d,EAAAsE,YACA0Z,aAAAhe,EAAAuE,SACA0Z,eAAAje,EAAAwE,YACA0Z,iBAAAle,EAAAyE,WACA0Z,gBAAAne,EAAA0E,UACAE,KAAA5E,EAAA4E,QACA,GAEA,IAEA,EACAiK,iBAAAA,CAAAD,GACA,KAAAA,WAAAA,EACA,KAAA7H,WACA,EACA0N,aAAAA,CAAAzC,EAAAvJ,EAAAC,GACA,QAAAnG,IAAAkG,GAAA,OAAAA,GAAA,KAAAA,EAEA,YADAC,IAIA,MAAAmO,EAAAF,OAAAlO,GACAmO,MAAAC,GACAnO,EAAA,IAAAvK,MAAA,UAIA0Y,EAAA,MAKA,QAAA3M,KAAA2M,EAAAhJ,YAGAnF,IAFAA,EAAA,IAAAvK,MAAA,oBALAuK,EAAA,IAAAvK,MAAA,gBASA,EACAqc,iBAAAA,CAAAxI,EAAAvJ,EAAAC,GACA,IAAAD,EAEA,YADAC,IAGA,MAAAgO,EAAAC,OAAAlO,GACAkO,OAAAC,MAAAF,GACAhO,EAAA,kBACA,KAAAD,EAAAyJ,QAAA,KAAAzJ,EAAAyJ,OACAxJ,EAAA,oBAEAA,GAEA,EACA8L,cAAAA,CAAAxC,EAAAvJ,EAAAC,GACA,eAAAsJ,EAAAwK,MAAA,CACA,IAAA/T,EAEA,YADAC,IAGA,MAAAgO,EAAAC,OAAAlO,GACAkO,OAAAC,MAAAF,GACAhO,EAAA,kBACA,KAAAD,EAAAoF,WAAAqE,OACAxJ,EAAA,cAEAA,GAEA,qBAAAsJ,EAAAwK,OAAA,UAAAxK,EAAAwK,MAAA,CACA,IAAA/T,EAEA,YADAC,IAGA,MAAAgO,EAAAC,OAAAlO,GACAkO,OAAAC,MAAAF,GACAhO,EAAA,kBACA,KAAAD,EAAAoF,WAAAqE,OACAxJ,EAAA,eAEAA,GAEA,sBAAAsJ,EAAAwK,MAAA,CACA,IAAA/T,EAEA,YADAC,IAGA,MAAAgO,EAAAC,OAAAlO,GACAkO,OAAAC,MAAAF,GACAhO,EAAA,kBACAD,EAAAoF,WAAAqE,OAAA,EACAxJ,EAAA,YAEAA,GAEA,MACAA,GAEA,EACAuF,WAAAA,CAAA/P,GACAA,EAAAqU,iBACA,KAAAxL,WACA,EACA6T,aAAAA,GACA,KAAAL,QAAAzI,gBAAA,CAAAU,EAAAC,KACA,KAAA+K,SAAA/K,EAAAjO,YACA,MAAA2Y,EAAA,KAAAK,SAAAJ,OAAA,cACAnb,QAAAC,IAAAib,GACA3K,GACA7N,EAAA8N,EAAAvP,QAAAuP,EAAArO,QAAAqO,EAAApO,MAAAoO,EAAAnO,YAAAmO,EAAAlO,SAAA4Y,EAAA1K,EAAAhO,WAAAgO,EAAA/N,UAAA,KAAAE,KAAA6N,EAAArP,OAAAgE,MAAAC,IACApF,QAAAC,IAAAmF,GACA,IAAAA,EAAAiF,WACA,KAAAqG,SAAA1H,QAAA,QAEA,KAAA0H,SAAA3Q,MAAAqF,EAAA3E,QACA,GAEA,GAEA,EACAoY,UAAAA,CAAAtT,GACA,IACA,KAAAtE,QAAAsE,EAAAqW,UACA,KAAAzZ,QAAAoD,EAAApD,QACA,KAAAC,MAAAmD,EAAAsW,QACA,KAAAxZ,YAAAkD,EAAAuW,gBACA,KAAAxZ,SAAAiD,EAAAwW,aACA,KAAAxZ,YAAAgD,EAAAyW,eACAhc,QAAAC,IAAA,KAAAsC,aACA,KAAAC,WAAA+C,EAAA0W,iBACA,KAAAxZ,UAAA8C,EAAA2W,gBACA,KAAApD,OAAAvT,EAAA5C,KACA3C,QAAAC,IAAA,KAAAob,OACA,KAAAnH,MAAA,+BAAAjT,SACA,KAAAiT,MAAA,+BAAA/R,SACA,KAAA+R,MAAA,6BAAA9R,OACA,KAAA8R,MAAA,mCAAA7R,aACA,KAAA6R,MAAA,gCAAA5R,UACA,KAAA4R,MAAA,mCAAA3R,aACA,KAAA2R,MAAA,kCAAA1R,YACA,KAAA0R,MAAA,iCAAAzR,WACA,KAAAyR,MAAA,8BAAA4E,QACA,KAAA/D,MAAAqG,iBAAAhI,SAAA,CACA,OAAArT,GACAC,QAAAD,MAAA,iCAAAA,EACA,CACA,IC9ZgR,MCQ5Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BtD,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,qBAAqB,CAACZ,EAAG,SAAS,CAACY,YAAY,eAAeV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIqP,KAAK,OAAS,aAAa,CAACnP,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBhJ,MAAM,CAAC,YAAc,WAAW,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIyf,YAAY,QAAQ,GAAG5V,MAAM,CAACC,MAAO9J,EAAI0f,SAASC,UAAW5V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI0f,SAAU,YAAa1V,EAAI,EAAEE,WAAW,wBAAyBlK,EAAI8D,aAAa6a,OAAiC,KAA1B1e,KAAKyf,SAASC,UAAgBzf,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAI8D,aAAa6a,UAAU3e,EAAIqW,KAAMrW,EAAI6D,OAAO8a,MAAOze,EAAG,OAAO,CAACmJ,MAAM,CAAC,cAAerJ,EAAI6D,OAAO8a,MAAM5Z,OAAO,CAAC/E,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAI6D,OAAO8a,MAAM5a,SAAS,OAAO/D,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBhJ,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIyf,YAAY,QAAQ,GAAG5V,MAAM,CAACC,MAAO9J,EAAI0f,SAASE,UAAW7V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI0f,SAAU,YAAa1V,EAAI,EAAEE,WAAW,wBAAyBlK,EAAI8D,aAAa0U,OAAiC,KAA1BvY,KAAKyf,SAASE,UAAgB1f,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAI8D,aAAa0U,UAAUxY,EAAIqW,KAAMrW,EAAI6D,OAAO2U,MAAOtY,EAAG,OAAO,CAACmJ,MAAM,CAAC,cAAerJ,EAAI6D,OAAO2U,MAAMzT,OAAO,CAAC/E,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAI6D,OAAO2U,MAAMzU,SAAS,OAAO/D,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,mBAAmB8I,QAAQ,uBAAuBhJ,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIyf,YAAY,QAAQ,GAAG5V,MAAM,CAACC,MAAO9J,EAAI0f,SAASG,UAAW9V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI0f,SAAU,YAAa1V,EAAI,EAAEE,WAAW,wBAAyBlK,EAAI8D,aAAa8a,OAAiC,KAA1B3e,KAAKyf,SAASG,UAAgB3f,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAI8D,aAAa8a,UAAU5e,EAAIqW,KAAMrW,EAAI6D,OAAO+a,MAAO1e,EAAG,OAAO,CAACmJ,MAAM,CAAC,cAAerJ,EAAI6D,OAAO+a,MAAM7Z,OAAO,CAAC/E,EAAIe,GAAG,IAAIf,EAAI0B,GAAG1B,EAAI6D,OAAO+a,MAAM7a,SAAS,OAAO/D,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,WAAW,CAACgB,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,aAAa,QAAQd,MAAM,CAAC,KAAO,WAAWY,GAAG,CAAC,MAAQhB,EAAI8f,aAAa,CAAC9f,EAAIe,GAAG,SAAS,IAAI,IAAI,GAAGb,EAAG,SAAS,CAACY,YAAY,iBAAiBV,MAAM,CAAC,UAAW,IAAQ,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,eAAe,CAACF,EAAG,QAAQ,CAACgB,YAAY,CAAC,OAAS,QAAQ,mBAAmB,UAAU,QAAU,OAAO,cAAc,UAAUd,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,cAAc8I,QAAQ,kBAAkBhJ,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAI6V,eAAe,QAAQ,GAAGhM,MAAM,CAACC,MAAO9J,EAAI+f,WAAWJ,UAAW5V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI+f,WAAY,YAAa/V,EAAI,EAAEE,WAAW,0BAA2BlK,EAAIqD,MAAMsb,OAAmC,KAA5B1e,KAAK8f,WAAWJ,UAAgBzf,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAIqD,MAAMsb,UAAU3e,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,cAAc8I,QAAQ,kBAAkBhJ,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAI6V,eAAe,QAAQ,GAAGhM,MAAM,CAACC,MAAO9J,EAAI+f,WAAWH,UAAW7V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI+f,WAAY,YAAa/V,EAAI,EAAEE,WAAW,0BAA2BlK,EAAIqD,MAAMmV,OAAmC,KAA5BvY,KAAK8f,WAAWH,UAAgB1f,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAIqD,MAAMmV,UAAUxY,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,UAAU,CAACiJ,WAAW,CAAC,CAAC7I,KAAK,cAAc8I,QAAQ,kBAAkBhJ,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIY,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAI6V,eAAe,QAAQ,GAAGhM,MAAM,CAACC,MAAO9J,EAAI+f,WAAWF,UAAW9V,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAI+f,WAAY,YAAa/V,EAAI,EAAEE,WAAW,0BAA2BlK,EAAIqD,MAAMub,OAAmC,KAA5B3e,KAAK8f,WAAWF,UAAgB3f,EAAG,IAAI,CAACgB,YAAY,CAAC,MAAQ,QAAQ,CAAClB,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAIqD,MAAMub,UAAU5e,EAAIqW,MAAM,IAAI,GAAGnW,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,IAAI,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,UAAUY,GAAG,CAAC,MAAQhB,EAAIggB,eAAe,CAAChgB,EAAIe,GAAG,SAAS,IAAI,IAAI,IAAI,GAAGb,EAAG,UAAU,CAACgB,YAAY,CAAC,aAAa,QAAQd,MAAM,CAAC,OAAS,MAAM,QAAUJ,EAAI2I,QAAQ,cAAc3I,EAAIkc,UAAU,WAAalc,EAAImW,kBAAkB,aAAe8J,CAACpX,EAAQvH,IAAWA,EAAQ,IAAM,EAAI,kBAAoB,kBAAmBN,GAAG,CAAC,OAAShB,EAAIoW,yBAAyBrN,YAAY/I,EAAIgJ,GAAG,CAAC,CAACzH,IAAI,UAAU0H,GAAG,SAASC,EAAML,GAAQ,MAAO,CAAC3I,EAAG,WAAW,CAACY,YAAY,gBAAgBV,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAI6W,aAAahO,EAAO,IAAI,CAAC7I,EAAIe,GAAG,UAAUb,EAAG,WAAW,CAACgB,YAAY,CAAC,cAAc,QAAQd,MAAM,CAAC,KAAO,UAAU,KAAO,UAAUY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAOxJ,EAAIkgB,aAAarX,EAAO,IAAI,CAAC7I,EAAIe,GAAG,UAAU,QAAQ,GAAGb,EAAG,uBAAuB,CAACqW,IAAI,uBAAuBnW,MAAM,CAAC,iBAAmBJ,EAAI2e,MAAM,iBAAmB3e,EAAIwY,MAAM,iBAAmBxY,EAAI4e,OAAO5d,GAAG,CAAC,gBAAgBhB,EAAIyW,sBAAsBvW,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQJ,EAAImgB,MAAMvf,MAAM,OAASZ,EAAImgB,MAAMC,OAAO,WAAapgB,EAAImgB,MAAME,YAAYrf,GAAG,CAAC,GAAKhB,EAAIsgB,oBAAoBzW,MAAM,CAACC,MAAO9J,EAAImgB,MAAMzJ,QAAS3M,SAAS,SAAUC,GAAMhK,EAAIiK,KAAKjK,EAAImgB,MAAO,UAAWnW,EAAI,EAAEE,WAAW,kBAAkB,CAAChK,EAAG,IAAI,CAACF,EAAIe,GAAGf,EAAI0B,GAAG1B,EAAImgB,MAAM1I,eAAe,EACpsL,EACIpX,GAAkB,GCFlBN,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,SAAS,QAAUJ,EAAI0W,QAAQ,kBAAkB1W,EAAI2W,eAAe,OAAS,MAAM3V,GAAG,CAAC,GAAKhB,EAAI4B,SAAS,OAAS5B,EAAI6B,eAAe,CAAC3B,EAAG,SAAS,CAACE,MAAM,CAAC,SAAWJ,EAAIgQ,UAAU,CAAC9P,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAI4W,WAAW,OAAS,YAAY5V,GAAG,CAAC,OAAShB,EAAI6W,eAAe,CAAC3W,EAAG,QAAQ,CAACE,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,QAAQ,kBAAkBJ,EAAIugB,kBAAoB,QAAU,GAAG,KAAOvgB,EAAIugB,mBAAqB,KAAK,CAACrgB,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,SAAWJ,EAAI2W,eAAe,UAAY,IAAI3V,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIqL,gBAAgB,QAAQ,GAAGxB,MAAM,CAACC,MAAO9J,EAAIwgB,WAAYzW,SAAS,SAAUC,GAAMhK,EAAIwgB,WAAWxW,CAAG,EAAEE,WAAW,iBAAiB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,kBAAkBJ,EAAIygB,kBAAoB,QAAU,GAAG,KAAOzgB,EAAIygB,mBAAqB,KAAK,CAACvgB,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,SAAWJ,EAAI2W,eAAe,UAAY,IAAI3V,GAAG,CAAC,KAAO,SAASwI,GAAQ,OAAOxJ,EAAIqL,gBAAgB,QAAQ,GAAGxB,MAAM,CAACC,MAAO9J,EAAI0gB,WAAY3W,SAAS,SAAUC,GAAMhK,EAAI0gB,WAAW1W,CAAG,EAAEE,WAAW,iBAAiB,IAAI,GAAGhK,EAAG,QAAQ,CAACE,MAAM,CAAC,KAAO,KAAK,CAACF,EAAG,cAAc,CAACE,MAAM,CAAC,MAAQ,OAAO,kBAAkBJ,EAAI2gB,kBAAoB,QAAU,GAAG,KAAO3gB,EAAI2gB,mBAAqB,KAAK,CAACzgB,EAAG,UAAU,CAACE,MAAM,CAAC,YAAc,QAAQ,UAAW,EAAK,UAAY,IAAIyJ,MAAM,CAACC,MAAO9J,EAAI4gB,WAAY7W,SAAS,SAAUC,GAAMhK,EAAI4gB,WAAW5W,CAAG,EAAEE,WAAW,iBAAiB,IAAI,GAAGhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,YAAY,SAAS,KAAO,OAAO,QAAUJ,EAAI2W,eAAe,MAAQ,IAAI3V,GAAG,CAAC,MAAQhB,EAAI6W,eAAe,CAAC7W,EAAIe,GAAG,WAAW,IAAI,IAAI,IAAI,IAAI,EAChxD,EACIV,GAAkB,GCiFtB,IACAC,KAAA,uBACAyW,MAAA,CACA8J,iBAAA,CACA9b,KAAAkS,OACArB,UAAA,GAEAkL,iBAAA,CACA/b,KAAAkS,OACArB,UAAA,GAEAmL,iBAAA,CACAhc,KAAAkS,OACArB,UAAA,IAGAjS,IAAAA,GACA,OACA6c,WAAA,KAAAK,iBACAH,WAAA,KAAAI,iBACAF,WAAA,KAAAG,iBACAnK,WAAA,KAAAxE,MAAAC,WAAA,MACAqE,SAAA,EACAC,gBAAA,EACA3G,SAAA,EACAuQ,kBAAA,GACAE,kBAAA,GACAE,kBAAA,GAEA,EACA1V,OAAAA,GACA,KAAAqM,QAAAhP,IAAA,yBAAAiP,IACA,KAAAiJ,WAAAjJ,CAAA,IAEA,KAAAD,QAAAhP,IAAA,yBAAAiP,IACA,KAAAmJ,WAAAnJ,CAAA,IAEA,KAAAD,QAAAhP,IAAA,yBAAAiP,IACA,KAAAqJ,WAAArJ,CAAA,GAEA,EACA7W,QAAA,CACA2K,eAAAA,CAAAtG,GACA,MAAAtF,EAAA,UAAAsF,EAAA,KAAAyb,WAAA,KAAAE,WACAM,EAAA,GAAAjc,gBAEAtF,EAEA,aAAA8L,KAAA9L,GAGA,KAAAuhB,GAAA,GAFA,KAAAA,GAAA,cAFA,KAAAA,GAAA,QAMA,EACA/f,SAAAA,GACA,KAAAyV,SAAA,CACA,EACA7U,YAAAA,GACA,KAAA6U,SAAA,EACA,KAAAE,WAAA7C,cACA,KAAAwM,kBAAA,GACA,KAAAE,kBAAA,GACA,KAAAE,kBAAA,EACA,EACA9J,YAAAA,CAAAtX,GACAA,EAAAqU,iBACA,KAAAvI,gBAAA,SACA,KAAAA,gBAAA,SAEA,KAAAkV,mBAAA,KAAAE,oBAIA,KAAA9J,gBAAA,EACAtR,EAAA,KAAAmb,WAAA,KAAAE,WAAA,KAAAE,YACAnY,MAAAC,IACA,IAAAA,EAAAiF,aACA,KAAAqG,SAAA1H,QAAA,CACAmL,QAAA,OACAvT,SAAA,IAEA,KAAAsT,MAAA,gBAAA9O,EAAAiF,YACA,KAAA9L,eACA,IACAoS,OAAA5Q,IACAW,EAAAA,EAAAX,MAAA,CACAU,QAAA,OACAE,YAAAZ,EAAAK,SAAAC,KAAAI,QACAG,SAAA,GACA,IAEAkQ,SAAA,KACA,KAAAuC,gBAAA,KAEA,EACA/U,QAAAA,GACA,KAAAgV,WAAAc,QACA,ICpLmQ,MCQ/P,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCkIhC,IACAnX,WAAA,CACA0gB,qBAAAA,IAEAtd,IAAAA,GACA,OACAgb,MAAA,GACAnG,MAAA,GACAoG,MAAA,GACAvb,MAAA,CACAsb,MAAA,GACAnG,MAAA,GACAoG,MAAA,IAEA9a,aAAA,CACA6a,MAAA,GACAnG,MAAA,GACAoG,MAAA,IAEAzI,kBAAA,CACA7D,QAAA,EACAC,SAAA,GACAC,gBAAA,uBACAC,UAAAA,CAAAC,EAAAC,IACAA,EAAA,OAAAA,EAAA,QAAAD,EAAA,IAEAE,iBAAA,EACAC,iBAAA,EACAH,MAAA,GAEArD,KAAA,KAAA+C,MAAAC,WAAA,MACAqN,SAAA,CACAC,UAAA,GACAC,UAAA,GACAC,UAAA,IAEAhc,OAAA,CACA8a,MAAA,KACAnG,MAAA,KACAoG,MAAA,MAEAmB,WAAA,CACAJ,UAAA,GACAC,UAAA,GACAC,UAAA,IAEAlX,QAAA,CACA,CAAA/H,MAAA,QAAA6J,UAAA,YAAAlJ,IAAA,aACA,CAAAX,MAAA,OAAA6J,UAAA,YAAAlJ,IAAA,aACA,CAAAX,MAAA,OAAA6J,UAAA,YAAAlJ,IAAA,aACA,CACAX,MAAA,KACAW,IAAA,UACAwH,YAAA,CAAA4B,aAAA,aAGAuR,UAAA,GACAiE,MAAA,CACAzJ,SAAA,EACA9V,MAAA,GACA6W,QAAA,GACA2I,OAAA,KACAC,WAAA,KACAtW,SAAA,MAEAmX,WAAA,EACAC,cAAA,KACA3c,OAAA,CACAma,MAAA,KACAnG,MAAA,KACAoG,MAAA,MAGA,EACApe,OAAAA,GACA,KAAAwf,cACA,EACAtf,QAAA,CACA0V,uBAAAA,CAAAnG,GACA,KAAAkG,kBAAAlG,EACA,KAAA+P,cACA,EACAvJ,kBAAAA,CAAA1S,GACA,IAAAA,GACA,KAAAqE,WAEA,EACAA,SAAAA,GACA,KAAA4X,cACA,EAEAnK,cAAAA,CAAA9Q,GACA,MAAAtF,EAAA,KAAAsgB,WAAA,GAAAhb,SACAtF,EAIA,UAAAsF,EACA,WAAAwG,KAAA9L,GAGA,KAAA4D,MAAA0B,GAAA,GAFA,KAAA1B,MAAA0B,GAAA,WAKA,aAAAwG,KAAA9L,GAGA,KAAA4D,MAAA0B,GAAA,GAFA,KAAA1B,MAAA0B,GAAA,eAXA,KAAA1B,MAAA0B,GAAA,EAgBA,EACA,iBAAA0a,CAAA1a,GACA,MAAAtF,EAAA,KAAAigB,SAAA,GAAA3a,SACA,GAAAtF,EAIA,aAAAsF,EACA,cAAAwG,KAAA9L,GAGA,CACA,KAAAqE,aAAAiB,GAAA,GACA,IACAD,EAAArF,EAAAsF,GAAA0D,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAlE,OAAAO,GAAA2D,EAAA0Y,OACA,KAAAvd,OAAAkB,GAAA,CACAA,KAAA,QAAA2D,EAAA0Y,OAAA,kBACArd,QAAA2E,EAAA0Y,OACA,GAEA,OAAA/d,GACA,KAAAQ,OAAAkB,GAAA,CACAA,KAAA,QACAhB,QAAA,OAEA,CACA,MAnBA,KAAAF,OAAAkB,GAAA,GACA,KAAAjB,aAAAiB,GAAA,gBAoBA,gBAAAwG,KAAA9L,GAGA,CACA,KAAAqE,aAAAiB,GAAA,GACA,IACAD,EAAArF,EAAAsF,GAAA0D,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAlE,OAAAO,GAAA2D,EAAA0Y,OACA,KAAAvd,OAAAkB,GAAA,CACAA,MAAA,IAAA2D,EAAAlE,OAAA,kBACAT,QAAA2E,EAAA0Y,OACA,GAEA,OAAA/d,GACA,KAAAQ,OAAAkB,GAAA,CACAA,KAAA,QACAhB,QAAA,OAEA,CACA,MAnBA,KAAAF,OAAAkB,GAAA,GACA,KAAAjB,aAAAiB,GAAA,oBA5BA,KAAAjB,aAAAiB,GAAA,OAgDA,EAEA,gBAAA+a,GAEA,KAAAL,YAAA,SACA,KAAAA,YAAA,SACA,KAAAA,YAAA,SAGA,MAAA4B,EAAA1hB,OAAAmU,OAAA,KAAAhQ,cAAAya,MAAAlb,GAAA,KAAAA,IACA,GAAAge,EAEA,YADA,KAAArN,SAAA3Q,MAAA,qBAKA,MAAAie,EAAA3hB,OAAAmU,OAAA,KAAAjQ,QAAA0a,MACA1a,GAAAA,GAAA,UAAAA,EAAAkB,OAEAuc,IACA,aAAA9c,OAAAma,OAAA,aAAAna,OAAAoa,OAAA,aAAApa,OAAAgU,OACA,KAAAxE,SAAA3Q,MAAA,iBAUA,KAAAke,UAEA,EAEA,cAAAA,GACA,IACAnc,EAAA,KAAAsa,SAAAC,UAAA,KAAAD,SAAAE,UAAA,KAAAF,SAAAG,WAAApX,MAAAC,IACApF,QAAAC,IAAAmF,GACA,IAAAA,EAAAiF,aACA,KAAAqG,SAAA1H,QAAA,QACA,KAAAkV,YACA,KAAAtF,UAAA,GACAlX,EAAA,KAAA0a,SAAAC,UAAA,KAAAD,SAAAE,UAAA,KAAAF,SAAAG,UAAA,KAAA1J,kBAAA7D,QAAA,KAAA6D,kBAAA5D,UAAA9J,MAAAC,IACA,KAAAyN,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACAjP,QAAAC,IAAAmF,GACA,IAAA+Y,EAAA,EACA/Y,EAAAyL,QAAAhJ,SAAA9J,IACA,KAAA6a,UAAA9Q,KAAA,CACA7J,IAAAkgB,IACA9B,UAAAte,EAAA4D,aACA2a,UAAAve,EAAA6D,aACA2a,UAAAxe,EAAA8D,cACA,GACA,IAEA,GAEA,OAAA9B,GACA,KAAA2Q,SAAA3Q,MAAA,OACA,CACA,EAEA,kBAAA2c,GAEA,KAAAnK,eAAA,SACA,KAAAA,eAAA,SACA,KAAAA,eAAA,SAGA,MAAAwL,EAAA1hB,OAAAmU,OAAA,KAAAzQ,OAAAkb,MAAAlb,GAAA,KAAAA,IACA,IAAAge,EAAA,CAKA,KAAAnF,UAAA,GACA,IACA,MAAAxT,QAAA1D,EACA,KAAA+a,WAAAJ,UACA,KAAAI,WAAAH,UACA,KAAAG,WAAAF,UACA,KAAA1J,kBAAA7D,QACA,KAAA6D,kBAAA5D,UAEA,KAAA4D,kBAAAzD,MAAAhK,EAAAwL,WAAA,KAAAiC,kBAAA5D,SACA,IAAAkP,EAAA,EACA/Y,EAAAyL,QAAAhJ,SAAA9J,IACA,KAAA6a,UAAA9Q,KAAA,CACA7J,IAAAkgB,IACA9B,UAAAte,EAAA4D,aACA2a,UAAAve,EAAA6D,aACA2a,UAAAxe,EAAA8D,cACA,GAEA,OAAA9B,GACA,KAAA2Q,SAAA3Q,MAAA,OACA,CAvBA,CAwBA,EACAwT,YAAAA,CAAAhO,GACA,IACA,KAAA8V,MAAA9V,EAAA8W,UACA,KAAAnH,MAAA3P,EAAA+W,UACA,KAAAhB,MAAA/V,EAAAgX,UACAvc,QAAAC,IAAA,KAAAob,OACA,KAAAnH,MAAA,6BAAAmH,OACA,KAAAnH,MAAA,6BAAAgB,OACA,KAAAhB,MAAA,6BAAAoH,OACA,KAAAvG,MAAA4I,qBAAArgB,MAAA,SACA,KAAAyX,MAAA4I,qBAAAvK,SAAA,CAEA,OAAArT,GACA,KAAA2Q,SAAA3Q,MAAA,OACA,CACA,EAEA6c,YAAAA,CAAArX,GACA,KAAA5H,UAAA,CACAL,MAAA,OACA6W,QAAA,aACA1N,SAAAA,IAAA,KAAA2X,aAAA7Y,IAEA,EAEA,kBAAA6Y,CAAA7Y,GACA,IACA,MAAAlF,EAAA,CACA,QAAAkF,EAAAgX,WAGAta,EAAA5B,GAAA8E,MAAAC,IACApF,QAAAC,IAAAmF,GACA,KAAAwT,UAAA,KAAAA,UAAAR,QAAAra,GAAAA,EAAAE,MAAAsH,EAAAtH,MACA,KAAAyS,SAAA1H,QAAA,QACA,KAAA0T,cAAA,GAGA,OAAA3c,GACA,KAAA2Q,SAAA3Q,MAAA,OACA,CACA,EAEApC,SAAAA,EAAA,MAAAL,EAAA,QAAA6W,EAAA,SAAA1N,IACA,KAAAoW,MAAA,IACA,KAAAA,MACAzJ,SAAA,EACA9V,QACA6W,UACA1N,WAEA,EAEAuW,kBAAAA,GACA,KAAAH,MAAAzJ,SAAA,EACA,KAAAyJ,MAAApW,UACA,KAAAoW,MAAApW,UAEA,EAEAyX,SAAAA,GACA,KAAA9B,SAAA,CACAC,UAAA,GACAC,UAAA,GACAC,UAAA,IAEA,KAAAhc,OAAA,CACA8a,MAAA,KACAnG,MAAA,KACAoG,MAAA,MAEA,KAAAsC,WAAA,EACA,KAAAC,cAAA,IACA,ICne+Q,MCQ3Q,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCnB5BphB,GAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACY,YAAY,8BAA8B,CAACZ,EAAG,SAAS,CAACE,MAAM,CAAC,KAAOJ,EAAIqP,MAAMrO,GAAG,CAAC,OAAShB,EAAI2V,eAAe,CAACzV,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,OAAO,CAACF,EAAIe,GAAG,YAAYb,EAAG,UAAU,CAACqW,IAAI,eAAenW,MAAM,CAAC,UAAY,GAAG,YAAc,YAAYY,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAIA,EAAOzE,KAAK0E,QAAQ,QAAQzJ,EAAI0J,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOjI,IAAI,SAAgB,KAAYvB,EAAI2hB,oBAAoBC,MAAM,KAAMC,UAAU,GAAGhY,MAAM,CAACC,MAAO9J,EAAI8hB,QAAS/X,SAAS,SAAUC,GAAMhK,EAAI8hB,QAAQ9X,CAAG,EAAEE,WAAW,cAAc,GAAGhK,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,OAAO,CAACF,EAAIe,GAAG,aAAab,EAAG,UAAU,CAACqW,IAAI,eAAenW,MAAM,CAAC,UAAY,GAAG,YAAc,WAAW,UAAYJ,EAAI+hB,cAAc/gB,GAAG,CAAC,MAAQ,SAASwI,GAAQ,OAAIA,EAAOzE,KAAK0E,QAAQ,QAAQzJ,EAAI0J,GAAGF,EAAOG,QAAQ,QAAQ,GAAGH,EAAOjI,IAAI,SAAgB,KAAYvB,EAAIgiB,oBAAoBJ,MAAM,KAAMC,UAAU,GAAGhY,MAAM,CAACC,MAAO9J,EAAIiiB,QAASlY,SAAS,SAAUC,GAAMhK,EAAIiiB,QAAQjY,CAAG,EAAEE,WAAW,cAAc,GAAGhK,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,UAAU,UAAYJ,EAAIkiB,iBAAiBlhB,GAAG,CAAC,MAAQhB,EAAI2V,eAAe,CAAC3V,EAAIe,GAAG,WAAW,GAAGb,EAAG,MAAM,CAACY,YAAY,gBAAgB,CAACZ,EAAG,MAAM,CAACY,YAAY,YAAY,CAACZ,EAAG,KAAK,CAACF,EAAIe,GAAG,WAAYf,EAAImiB,aAAcjiB,EAAG,MAAM,CAACA,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAa5d,YAAYrE,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAa1c,YAAYvF,EAAG,IAAI,CAACF,EAAIe,GAAG,OAAOf,EAAI0B,GAAG1B,EAAImiB,aAAazc,UAAUxF,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAaxc,gBAAgBzF,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAavc,aAAa1F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAatc,gBAAgB3F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAarc,eAAe5F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAapc,cAAc7F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAImiB,aAAalc,WAAWjG,EAAIqW,OAAOnW,EAAG,MAAM,CAACY,YAAY,YAAY,CAACZ,EAAG,KAAK,CAACF,EAAIe,GAAG,YAAaf,EAAIoiB,aAAcliB,EAAG,MAAM,CAACA,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAa7d,YAAYrE,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAa3c,YAAYvF,EAAG,IAAI,CAACF,EAAIe,GAAG,OAAOf,EAAI0B,GAAG1B,EAAIoiB,aAAa1c,UAAUxF,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAazc,gBAAgBzF,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAaxc,aAAa1F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAavc,gBAAgB3F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAatc,eAAe5F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAarc,cAAc7F,EAAG,IAAI,CAACF,EAAIe,GAAG,SAASf,EAAI0B,GAAG1B,EAAIoiB,aAAanc,WAAWjG,EAAIqW,SAASnW,EAAG,UAAU,CAACE,MAAM,CAAC,QAAUJ,EAAIgQ,QAAQ,UAAW,EAAM,cAAe,EAAM,OAAS,KAAK,SAAW,KAAK,CAAC9P,EAAG,MAAM,CAACY,YAAY,mBAAmB,CAACZ,EAAG,UAAUA,EAAG,IAAI,CAACF,EAAIe,GAAG,yBAAyBb,EAAG,IAAI,CAACF,EAAIe,GAAG,QAAQf,EAAI0B,GAAG1B,EAAIqiB,eAAe,QAAQ,MAAM,IAAI,EACzyF,EACIhiB,GAAkB,GCmFtB,IACAsD,IAAAA,GACA,OACA0L,KAAA,KAAA+C,MAAAC,WAAA,MACAyP,QAAA,GACAG,QAAA,GACAF,cAAA,EACA/R,SAAA,EACAqS,cAAA,GACAC,MAAA,KACAH,aAAA,KACAC,aAAA,KAEA,EAEAna,SAAA,CACAia,eAAAA,GACA,YAAAH,cACA,KAAAE,SACA,KAAAE,cACA,KAAAC,eACA,KAAApS,OACA,GAGAtP,QAAA,CACAmb,iBAAAA,CAAApc,GACA,iBAAA8L,KAAA9L,EACA,EAEA,yBAAAkiB,GACA,SAAA9F,kBAAA,KAAAiG,SAGA,OAFA,KAAA9N,SAAA3Q,MAAA,mBACA,KAAA0e,cAAA,GAIA,IACA,MAAAre,QAAA,KAAA6e,eAAA,KAAAT,SACApe,EAAA8e,QACA,KAAAL,aAAAze,EACA,KAAAqe,cAAA,EACA,KAAAU,WAAA,KACA,KAAApK,MAAAqK,aAAAC,OAAA,MAGA,KAAA3O,SAAA3Q,MAAA,YACA,KAAA0e,cAAA,EAEA,OAAA1e,GACA,KAAA2Q,SAAA3Q,MAAA,YACA,KAAA0e,cAAA,CACA,CACA,EAEA,yBAAAC,GACA,QAAAnG,kBAAA,KAAAoG,SAKA,IACA,MAAAve,QAAA,KAAA6e,eAAA,KAAAN,SACAve,EAAA8e,QAGA,KAAAxO,SAAA3Q,MAAA,aACA,KAAA+e,aAAA,MAHA,KAAAA,aAAA1e,CAKA,OAAAL,GACA,KAAA2Q,SAAA3Q,MAAA,YACA,KAAA+e,aAAA,IACA,MAfA,KAAApO,SAAA3Q,MAAA,aAgBA,EAEA,kBAAAsS,CAAApW,GAEA,GADAA,GAAAA,EAAAqU,iBACA,KAAAsO,gBAAA,CAEA,KAAAlS,SAAA,EACA,KAAAqS,cAAA,GAEA,KAAAC,MAAA3T,aAAA,KACA,KAAA0T,gBACA,KAAAA,eAAA,IACAxT,cAAA,KAAAyT,OACA,KAAAtS,SAAA,EACA,KAAAgE,SAAA3Q,MAAA,QACA,GACA,KAEA,IACA,MAAAK,QAAA,KAAAkf,iBAAA,CACAd,QAAA,KAAAA,QACAG,QAAA,KAAAA,UAEApT,cAAA,KAAAyT,OACA,KAAAtS,SAAA,EACAtM,EAAA4I,SACA,KAAA0H,SAAA1H,QAAA,QACA,KAAAkV,aAEA,KAAAxN,SAAA3Q,MAAAK,EAAAK,SAAA,OAEA,OAAAV,GACAC,QAAAD,MAAA,eAAAA,GACAwL,cAAA,KAAAyT,OACA,KAAAtS,SAAA,EACA,KAAAgE,SAAA3Q,MAAA,OACA,CAhCA,CAiCA,EAEAme,SAAAA,GACA,KAAAM,QAAA,GACA,KAAAG,QAAA,GACA,KAAAF,cAAA,EACA,KAAAI,aAAA,KACA,KAAAC,aAAA,KACA,KAAAK,WAAA,KACA,KAAApK,MAAAwK,aAAAF,OAAA,GAEA,EAEA,oBAAAJ,CAAA9iB,GACA,IACA,MAAAiJ,QAAAZ,EAAArI,GAOA,OANA6D,QAAAC,IAAAmF,SACA,IAAAlF,SAAA5D,IACA6L,YAAA,KACA7L,GAAA,GACA,QAEA,CACA2E,QAAAmE,EAAAnE,QACAkB,QAAAiD,EAAAjD,QACAC,MAAAgD,EAAAhD,MACAC,YAAA+C,EAAA/C,YACAC,SAAA8C,EAAA9C,SACAC,YAAA6C,EAAA7C,YACAC,WAAA4C,EAAA5C,WACAC,UAAA2C,EAAA3C,UACAE,KAAAyC,EAAAzC,KACAuc,OAAA9Z,EAAAoa,KAEA,OAAAzf,GAEA,MADAC,QAAAD,MAAA,aAAAA,GACAA,CACA,CACA,EAEA,sBAAAuf,CAAAjf,GACA,IACA,MAAA+E,QAAAX,EAAApE,EAAAme,QAAAne,EAAAse,SAEA,OADA3e,QAAAC,IAAAmF,GACA,IAAAA,EAAAiF,WACA,CAAArB,SAAA,GAEA,CAAAA,SAAA,EAAAvI,QAAA2E,EAAA3E,SAAA,OAEA,OAAAV,GAEA,MADAC,QAAAD,MAAA,eAAAA,GACAA,CACA,CACA,GAGA+L,aAAAA,GACA,KAAAkT,OACAzT,cAAA,KAAAyT,MAEA,GC/PyQ,MCQrQ,IAAY,OACd,GACA,GACA,IACA,EACA,KACA,WACA,MAIF,GAAe,GAAiB,QCJhCS,EAAAA,GAAI5f,IAAI6f,EAAAA,IAGR,MAAMC,GAAS,IAAID,EAAAA,GAAO,CACtB/c,KAAM,UACNid,KAAK,SACLC,OAAO,CACH,CACIC,KAAK,IACLC,SAAS,cACT/iB,KAAK,OACLO,UAAUyiB,IAEd,CACIF,KAAK,QACLviB,UAAU0iB,EACVjjB,KAAK,KACLkjB,SAAS,CACL,CACIJ,KAAK,kBACL9iB,KAAK,OACLO,UAAU4iB,IAEd,CACIL,KAAK,cACL9iB,KAAK,OACLO,UAAUyiB,IAEd,CACIF,KAAK,YACL9iB,KAAK,KACLO,UAAU6iB,IAEd,CACIN,KAAK,kBACL9iB,KAAK,QACLO,UAAU8iB,IAGd,CACIP,KAAK,iBACL9iB,KAAK,OACLO,UAAU+iB,IAEd,CACIR,KAAK,sBACL9iB,KAAK,OACLO,UAAUgjB,IAGd,CACIT,KAAK,oBACL9iB,KAAK,OACLO,UAAUijB,IAGd,CACIV,KAAK,4BACL9iB,KAAK,OACLO,UAAUkjB,IAGd,CACIX,KAAK,2BACL9iB,KAAK,OACLO,UAAUmjB,IAGd,CACIZ,KAAK,qBACL9iB,KAAK,SACLO,UAAUojB,SAQ9B,UCzFAlB,EAAAA,GAAImB,UAAU,iBAAkB,CAC5BC,SAAU,SAAUC,GAChBA,EAAGC,iBAAiB,YAAY,SAAU9kB,GACtC0C,OAAOyJ,kBAAmB,EAC1BnM,EAAIA,GAAK0C,OAAOyL,MAChB,IAAI4W,EAAiC,kBAAf/kB,EAAEglB,SAAuBhlB,EAAEglB,SAAWhlB,EAAEoK,QAC9D,GAAiB,KAAb2a,EAAiB,CACjB/kB,EAAEqU,iBACF,IAAI4Q,EAAe7jB,SAAS8jB,cAC5B,GAAID,EAAc,CACd,IAAIE,EAAa,IAAIC,MAAM,SAC3BD,EAAW/a,QAAU,GACrB6a,EAAaI,cAAcF,EAC/B,CACAziB,OAAOyJ,kBAAmB,EAE1B,IADA,IAAImZ,EAAMlkB,SAASmkB,qBAAqB,SAC/BrD,EAAI,EAAGA,EAAIoD,EAAItR,OAAQkO,IAC5B,GAAIoD,EAAIpD,KAAO9gB,SAAS8jB,cAAe,CACnC,GAAIhD,IAAMoD,EAAItR,OAAS,EACnB,OAGJ,YADAsR,EAAIpD,EAAI,GAAGkB,OAEf,CAER,CACJ,GACJ,IAGJI,EAAAA,GAAImB,UAAU,aAAc,CACxBC,SAAU,SAAUC,GAChBA,EAAGC,iBAAiB,YAAY,SAAU9kB,GACtCA,EAAIA,GAAK0C,OAAOyL,MAChB,IAAI4W,EAAiC,kBAAf/kB,EAAEglB,SAAuBhlB,EAAEglB,SAAWhlB,EAAEoK,QAC9D,GAAiB,KAAb2a,EAAiB,CACjB/kB,EAAEqU,iBACFrU,EAAEwlB,kBAEF,IADA,IAAIF,EAAMlkB,SAASmkB,qBAAqB,SAC/BrD,EAAI,EAAGA,EAAIoD,EAAItR,OAAQkO,IAC5B,GAAIoD,EAAIpD,KAAO9gB,SAAS8jB,cAAe,CACnC,GAAIhD,IAAMoD,EAAItR,OAAS,EACnB,OAGJ,YADAsR,EAAIpD,EAAI,GAAGkB,OAEf,CAER,CACJ,GACJ,IC/CG,MAAMta,GAAW,IAAI0a,EAAAA,GAY5BA,EAAAA,GAAI5f,IAAI6hB,EAAAA,IAGRjC,EAAAA,GAAI3f,OAAO6hB,eAAgB,EAE3B,IAAIlC,EAAAA,GAAI,CACNE,OAAM,GACNljB,OAAQmlB,GAAKA,EAAEC,KACdC,OAAO,O,GC3BNC,EAA2B,CAAC,EAGhC,SAAShmB,EAAoBqW,GAE5B,IAAI4P,EAAeD,EAAyB3P,GAC5C,QAAqB9R,IAAjB0hB,EACH,OAAOA,EAAaxlB,QAGrB,IAAID,EAASwlB,EAAyB3P,GAAY,CACjDvW,GAAIuW,EACJ6P,QAAQ,EACRzlB,QAAS,CAAC,GAUX,OANA0lB,EAAoB9P,GAAU+P,KAAK5lB,EAAOC,QAASD,EAAQA,EAAOC,QAAST,GAG3EQ,EAAO0lB,QAAS,EAGT1lB,EAAOC,OACf,CAGAT,EAAoBqmB,EAAIF,E,WC5BxB,IAAIG,EAAW,GACftmB,EAAoBumB,EAAI,SAASphB,EAAQqhB,EAAU5c,EAAI6c,GACtD,IAAGD,EAAH,CAMA,IAAIE,EAAeC,IACnB,IAASvE,EAAI,EAAGA,EAAIkE,EAASpS,OAAQkO,IAAK,CACrCoE,EAAWF,EAASlE,GAAG,GACvBxY,EAAK0c,EAASlE,GAAG,GACjBqE,EAAWH,EAASlE,GAAG,GAE3B,IAJA,IAGIwE,GAAY,EACPC,EAAI,EAAGA,EAAIL,EAAStS,OAAQ2S,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAanmB,OAAOD,KAAKL,EAAoBumB,GAAGO,OAAM,SAAS5kB,GAAO,OAAOlC,EAAoBumB,EAAErkB,GAAKskB,EAASK,GAAK,IAChKL,EAAS7K,OAAOkL,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbN,EAAS3K,OAAOyG,IAAK,GACrB,IAAI2E,EAAInd,SACErF,IAANwiB,IAAiB5hB,EAAS4hB,EAC/B,CACD,CACA,OAAO5hB,CArBP,CAJCshB,EAAWA,GAAY,EACvB,IAAI,IAAIrE,EAAIkE,EAASpS,OAAQkO,EAAI,GAAKkE,EAASlE,EAAI,GAAG,GAAKqE,EAAUrE,IAAKkE,EAASlE,GAAKkE,EAASlE,EAAI,GACrGkE,EAASlE,GAAK,CAACoE,EAAU5c,EAAI6c,EAwB/B,C,eC5BAzmB,EAAoBgnB,EAAI,SAASxmB,GAChC,IAAIymB,EAASzmB,GAAUA,EAAO0mB,WAC7B,WAAa,OAAO1mB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAR,EAAoBmnB,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAjnB,EAAoBmnB,EAAI,SAAS1mB,EAAS4mB,GACzC,IAAI,IAAInlB,KAAOmlB,EACXrnB,EAAoBC,EAAEonB,EAAYnlB,KAASlC,EAAoBC,EAAEQ,EAASyB,IAC5E5B,OAAOgnB,eAAe7mB,EAASyB,EAAK,CAAEqlB,YAAY,EAAMC,IAAKH,EAAWnlB,IAG3E,C,eCPAlC,EAAoBynB,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO9mB,MAAQ,IAAI+mB,SAAS,cAAb,EAChB,CAAE,MAAOznB,GACR,GAAsB,kBAAX0C,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxB5C,EAAoBC,EAAI,SAAS2nB,EAAKC,GAAQ,OAAOvnB,OAAOwnB,UAAUC,eAAe3B,KAAKwB,EAAKC,EAAO,C,eCCtG7nB,EAAoB+mB,EAAI,SAAStmB,GACX,qBAAXunB,QAA0BA,OAAOC,aAC1C3nB,OAAOgnB,eAAe7mB,EAASunB,OAAOC,YAAa,CAAExd,MAAO,WAE7DnK,OAAOgnB,eAAe7mB,EAAS,aAAc,CAAEgK,OAAO,GACvD,C,eCNAzK,EAAoBkoB,IAAM,SAAS1nB,GAGlC,OAFAA,EAAO2nB,MAAQ,GACV3nB,EAAO2jB,WAAU3jB,EAAO2jB,SAAW,IACjC3jB,CACR,C,eCCA,IAAI4nB,EAAkB,CACrB,IAAK,GAaNpoB,EAAoBumB,EAAEM,EAAI,SAASwB,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4BjkB,GAC/D,IAKI+R,EAAUgS,EALV7B,EAAWliB,EAAK,GAChBkkB,EAAclkB,EAAK,GACnBmkB,EAAUnkB,EAAK,GAGI8d,EAAI,EAC3B,GAAGoE,EAAStH,MAAK,SAASpf,GAAM,OAA+B,IAAxBsoB,EAAgBtoB,EAAW,IAAI,CACrE,IAAIuW,KAAYmS,EACZxoB,EAAoBC,EAAEuoB,EAAanS,KACrCrW,EAAoBqmB,EAAEhQ,GAAYmS,EAAYnS,IAGhD,GAAGoS,EAAS,IAAItjB,EAASsjB,EAAQzoB,EAClC,CAEA,IADGuoB,GAA4BA,EAA2BjkB,GACrD8d,EAAIoE,EAAStS,OAAQkO,IACzBiG,EAAU7B,EAASpE,GAChBpiB,EAAoBC,EAAEmoB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAOroB,EAAoBumB,EAAEphB,EAC9B,EAEIujB,EAAqBC,KAAK,8BAAgCA,KAAK,+BAAiC,GACpGD,EAAmB5c,QAAQwc,EAAqBM,KAAK,KAAM,IAC3DF,EAAmB3c,KAAOuc,EAAqBM,KAAK,KAAMF,EAAmB3c,KAAK6c,KAAKF,G,IC/CvF,IAAIG,EAAsB7oB,EAAoBumB,OAAEhiB,EAAW,CAAC,MAAM,WAAa,OAAOvE,EAAoB,MAAQ,IAClH6oB,EAAsB7oB,EAAoBumB,EAAEsC,E", "sources": ["webpack://ant-design-vue/./node_modules/moment/locale/ sync ^\\.\\/.*$", "webpack://ant-design-vue/./src/App.vue", "webpack://ant-design-vue/src/App.vue", "webpack://ant-design-vue/./src/App.vue?c036", "webpack://ant-design-vue/./src/App.vue?0e40", "webpack://ant-design-vue/./src/views/Home.vue", "webpack://ant-design-vue/./src/utils/api.js", "webpack://ant-design-vue/./src/api/api.js", "webpack://ant-design-vue/src/views/Home.vue", "webpack://ant-design-vue/./src/views/Home.vue?a787", "webpack://ant-design-vue/./src/views/Home.vue?8611", "webpack://ant-design-vue/./src/views/content/BoardLevelTest.vue", "webpack://ant-design-vue/src/views/content/BoardLevelTest.vue", "webpack://ant-design-vue/./src/views/content/BoardLevelTest.vue?84e0", "webpack://ant-design-vue/./src/views/content/BoardLevelTest.vue?3094", "webpack://ant-design-vue/./src/views/content/DataManage.vue", "webpack://ant-design-vue/src/views/content/DataManage.vue", "webpack://ant-design-vue/./src/views/content/DataManage.vue?5530", "webpack://ant-design-vue/./src/views/content/DataManage.vue?fe08", "webpack://ant-design-vue/./src/views/content/HomePage.vue", "webpack://ant-design-vue/src/views/content/HomePage.vue", "webpack://ant-design-vue/./src/views/content/HomePage.vue?16cd", "webpack://ant-design-vue/./src/views/content/HomePage.vue?5880", "webpack://ant-design-vue/./src/views/content/PowerBoardTest.vue", "webpack://ant-design-vue/./src/components/UpdatePowerBoardTest.vue", "webpack://ant-design-vue/src/components/UpdatePowerBoardTest.vue", "webpack://ant-design-vue/./src/components/UpdatePowerBoardTest.vue?28f1", "webpack://ant-design-vue/./src/components/UpdatePowerBoardTest.vue?94ea", "webpack://ant-design-vue/src/views/content/PowerBoardTest.vue", "webpack://ant-design-vue/./src/views/content/PowerBoardTest.vue?0cb3", "webpack://ant-design-vue/./src/views/content/PowerBoardTest.vue?d19e", "webpack://ant-design-vue/./src/views/content/SystemSetting.vue", "webpack://ant-design-vue/src/views/content/SystemSetting.vue", "webpack://ant-design-vue/./src/views/content/SystemSetting.vue?dc77", "webpack://ant-design-vue/./src/views/content/SystemSetting.vue?8fc7", "webpack://ant-design-vue/./src/views/content/WholeMachineReTest.vue", "webpack://ant-design-vue/src/views/content/WholeMachineReTest.vue", "webpack://ant-design-vue/./src/views/content/WholeMachineReTest.vue?32e3", "webpack://ant-design-vue/./src/views/content/WholeMachineReTest.vue?668e", "webpack://ant-design-vue/./src/views/content/WholeMachineTest.vue", "webpack://ant-design-vue/src/views/content/WholeMachineTest.vue", "webpack://ant-design-vue/./src/views/content/WholeMachineTest.vue?5616", "webpack://ant-design-vue/./src/views/content/WholeMachineTest.vue?14ae", "webpack://ant-design-vue/./src/views/content/WriteNumberConfiguration.vue", "webpack://ant-design-vue/./src/components/UpdateNumberInfo.vue", "webpack://ant-design-vue/src/components/UpdateNumberInfo.vue", "webpack://ant-design-vue/./src/components/UpdateNumberInfo.vue?06cc", "webpack://ant-design-vue/./src/components/UpdateNumberInfo.vue?415e", "webpack://ant-design-vue/src/views/content/WriteNumberConfiguration.vue", "webpack://ant-design-vue/./src/views/content/WriteNumberConfiguration.vue?2cab", "webpack://ant-design-vue/./src/views/content/WriteNumberConfiguration.vue?ca5c", "webpack://ant-design-vue/./src/views/content/CompleteMachineAssembly.vue", "webpack://ant-design-vue/./src/components/UpdateAssemblyRelate.vue", "webpack://ant-design-vue/src/components/UpdateAssemblyRelate.vue", "webpack://ant-design-vue/./src/components/UpdateAssemblyRelate.vue?5578", "webpack://ant-design-vue/./src/components/UpdateAssemblyRelate.vue?f409", "webpack://ant-design-vue/src/views/content/CompleteMachineAssembly.vue", "webpack://ant-design-vue/./src/views/content/CompleteMachineAssembly.vue?2efa", "webpack://ant-design-vue/./src/views/content/CompleteMachineAssembly.vue?4c8a", "webpack://ant-design-vue/./src/views/content/ChangeAssetCoding.vue", "webpack://ant-design-vue/src/views/content/ChangeAssetCoding.vue", "webpack://ant-design-vue/./src/views/content/ChangeAssetCoding.vue?217d", "webpack://ant-design-vue/./src/views/content/ChangeAssetCoding.vue?82f6", "webpack://ant-design-vue/./src/router/router.js", "webpack://ant-design-vue/./src/utils/enterNextInput.js", "webpack://ant-design-vue/./src/main.js", "webpack://ant-design-vue/webpack/bootstrap", "webpack://ant-design-vue/webpack/runtime/chunk loaded", "webpack://ant-design-vue/webpack/runtime/compat get default export", "webpack://ant-design-vue/webpack/runtime/define property getters", "webpack://ant-design-vue/webpack/runtime/global", "webpack://ant-design-vue/webpack/runtime/hasOwnProperty shorthand", "webpack://ant-design-vue/webpack/runtime/make namespace object", "webpack://ant-design-vue/webpack/runtime/node module decorator", "webpack://ant-design-vue/webpack/runtime/jsonp chunk loading", "webpack://ant-design-vue/webpack/startup"], "sourcesContent": ["var map = {\n\t\"./af\": 22190,\n\t\"./af.js\": 22190,\n\t\"./ar\": 7218,\n\t\"./ar-dz\": 85785,\n\t\"./ar-dz.js\": 85785,\n\t\"./ar-kw\": 29417,\n\t\"./ar-kw.js\": 29417,\n\t\"./ar-ly\": 56904,\n\t\"./ar-ly.js\": 56904,\n\t\"./ar-ma\": 98617,\n\t\"./ar-ma.js\": 98617,\n\t\"./ar-ps\": 91318,\n\t\"./ar-ps.js\": 91318,\n\t\"./ar-sa\": 82699,\n\t\"./ar-sa.js\": 82699,\n\t\"./ar-tn\": 36789,\n\t\"./ar-tn.js\": 36789,\n\t\"./ar.js\": 7218,\n\t\"./az\": 23050,\n\t\"./az.js\": 23050,\n\t\"./be\": 8316,\n\t\"./be.js\": 8316,\n\t\"./bg\": 70310,\n\t\"./bg.js\": 70310,\n\t\"./bm\": 58884,\n\t\"./bm.js\": 58884,\n\t\"./bn\": 83469,\n\t\"./bn-bd\": 46672,\n\t\"./bn-bd.js\": 46672,\n\t\"./bn.js\": 83469,\n\t\"./bo\": 39118,\n\t\"./bo.js\": 39118,\n\t\"./br\": 13113,\n\t\"./br.js\": 13113,\n\t\"./bs\": 23626,\n\t\"./bs.js\": 23626,\n\t\"./ca\": 40921,\n\t\"./ca.js\": 40921,\n\t\"./cs\": 17799,\n\t\"./cs.js\": 17799,\n\t\"./cv\": 12828,\n\t\"./cv.js\": 12828,\n\t\"./cy\": 93521,\n\t\"./cy.js\": 93521,\n\t\"./da\": 56962,\n\t\"./da.js\": 56962,\n\t\"./de\": 93294,\n\t\"./de-at\": 16158,\n\t\"./de-at.js\": 16158,\n\t\"./de-ch\": 95960,\n\t\"./de-ch.js\": 95960,\n\t\"./de.js\": 93294,\n\t\"./dv\": 47963,\n\t\"./dv.js\": 47963,\n\t\"./el\": 3432,\n\t\"./el.js\": 3432,\n\t\"./en-au\": 20998,\n\t\"./en-au.js\": 20998,\n\t\"./en-ca\": 15931,\n\t\"./en-ca.js\": 15931,\n\t\"./en-gb\": 45930,\n\t\"./en-gb.js\": 45930,\n\t\"./en-ie\": 58081,\n\t\"./en-ie.js\": 58081,\n\t\"./en-il\": 71594,\n\t\"./en-il.js\": 71594,\n\t\"./en-in\": 23904,\n\t\"./en-in.js\": 23904,\n\t\"./en-nz\": 1507,\n\t\"./en-nz.js\": 1507,\n\t\"./en-sg\": 19409,\n\t\"./en-sg.js\": 19409,\n\t\"./eo\": 22177,\n\t\"./eo.js\": 22177,\n\t\"./es\": 84805,\n\t\"./es-do\": 39155,\n\t\"./es-do.js\": 39155,\n\t\"./es-mx\": 69791,\n\t\"./es-mx.js\": 69791,\n\t\"./es-us\": 76098,\n\t\"./es-us.js\": 76098,\n\t\"./es.js\": 84805,\n\t\"./et\": 96240,\n\t\"./et.js\": 96240,\n\t\"./eu\": 20391,\n\t\"./eu.js\": 20391,\n\t\"./fa\": 20612,\n\t\"./fa.js\": 20612,\n\t\"./fi\": 4220,\n\t\"./fi.js\": 4220,\n\t\"./fil\": 65570,\n\t\"./fil.js\": 65570,\n\t\"./fo\": 5466,\n\t\"./fo.js\": 5466,\n\t\"./fr\": 14461,\n\t\"./fr-ca\": 66306,\n\t\"./fr-ca.js\": 66306,\n\t\"./fr-ch\": 27081,\n\t\"./fr-ch.js\": 27081,\n\t\"./fr.js\": 14461,\n\t\"./fy\": 73484,\n\t\"./fy.js\": 73484,\n\t\"./ga\": 76957,\n\t\"./ga.js\": 76957,\n\t\"./gd\": 72978,\n\t\"./gd.js\": 72978,\n\t\"./gl\": 89866,\n\t\"./gl.js\": 89866,\n\t\"./gom-deva\": 65011,\n\t\"./gom-deva.js\": 65011,\n\t\"./gom-latn\": 84724,\n\t\"./gom-latn.js\": 84724,\n\t\"./gu\": 71601,\n\t\"./gu.js\": 71601,\n\t\"./he\": 79802,\n\t\"./he.js\": 79802,\n\t\"./hi\": 9358,\n\t\"./hi.js\": 9358,\n\t\"./hr\": 13907,\n\t\"./hr.js\": 13907,\n\t\"./hu\": 10218,\n\t\"./hu.js\": 10218,\n\t\"./hy-am\": 20533,\n\t\"./hy-am.js\": 20533,\n\t\"./id\": 52844,\n\t\"./id.js\": 52844,\n\t\"./is\": 97353,\n\t\"./is.js\": 97353,\n\t\"./it\": 6364,\n\t\"./it-ch\": 20774,\n\t\"./it-ch.js\": 20774,\n\t\"./it.js\": 6364,\n\t\"./ja\": 6008,\n\t\"./ja.js\": 6008,\n\t\"./jv\": 68221,\n\t\"./jv.js\": 68221,\n\t\"./ka\": 92417,\n\t\"./ka.js\": 92417,\n\t\"./kk\": 42071,\n\t\"./kk.js\": 42071,\n\t\"./km\": 76149,\n\t\"./km.js\": 76149,\n\t\"./kn\": 94572,\n\t\"./kn.js\": 94572,\n\t\"./ko\": 60659,\n\t\"./ko.js\": 60659,\n\t\"./ku\": 66285,\n\t\"./ku-kmr\": 59398,\n\t\"./ku-kmr.js\": 59398,\n\t\"./ku.js\": 66285,\n\t\"./ky\": 81609,\n\t\"./ky.js\": 81609,\n\t\"./lb\": 119,\n\t\"./lb.js\": 119,\n\t\"./lo\": 81748,\n\t\"./lo.js\": 81748,\n\t\"./lt\": 71973,\n\t\"./lt.js\": 71973,\n\t\"./lv\": 81347,\n\t\"./lv.js\": 81347,\n\t\"./me\": 53023,\n\t\"./me.js\": 53023,\n\t\"./mi\": 65747,\n\t\"./mi.js\": 65747,\n\t\"./mk\": 64341,\n\t\"./mk.js\": 64341,\n\t\"./ml\": 63840,\n\t\"./ml.js\": 63840,\n\t\"./mn\": 62058,\n\t\"./mn.js\": 62058,\n\t\"./mr\": 19182,\n\t\"./mr.js\": 19182,\n\t\"./ms\": 45197,\n\t\"./ms-my\": 89136,\n\t\"./ms-my.js\": 89136,\n\t\"./ms.js\": 45197,\n\t\"./mt\": 36408,\n\t\"./mt.js\": 36408,\n\t\"./my\": 74064,\n\t\"./my.js\": 74064,\n\t\"./nb\": 53141,\n\t\"./nb.js\": 53141,\n\t\"./ne\": 29344,\n\t\"./ne.js\": 29344,\n\t\"./nl\": 44703,\n\t\"./nl-be\": 84641,\n\t\"./nl-be.js\": 84641,\n\t\"./nl.js\": 44703,\n\t\"./nn\": 79873,\n\t\"./nn.js\": 79873,\n\t\"./oc-lnc\": 61217,\n\t\"./oc-lnc.js\": 61217,\n\t\"./pa-in\": 24612,\n\t\"./pa-in.js\": 24612,\n\t\"./pl\": 24457,\n\t\"./pl.js\": 24457,\n\t\"./pt\": 1089,\n\t\"./pt-br\": 79146,\n\t\"./pt-br.js\": 79146,\n\t\"./pt.js\": 1089,\n\t\"./ro\": 45950,\n\t\"./ro.js\": 45950,\n\t\"./ru\": 27292,\n\t\"./ru.js\": 27292,\n\t\"./sd\": 56774,\n\t\"./sd.js\": 56774,\n\t\"./se\": 87493,\n\t\"./se.js\": 87493,\n\t\"./si\": 3761,\n\t\"./si.js\": 3761,\n\t\"./sk\": 49711,\n\t\"./sk.js\": 49711,\n\t\"./sl\": 88558,\n\t\"./sl.js\": 88558,\n\t\"./sq\": 8633,\n\t\"./sq.js\": 8633,\n\t\"./sr\": 90688,\n\t\"./sr-cyrl\": 47903,\n\t\"./sr-cyrl.js\": 47903,\n\t\"./sr.js\": 90688,\n\t\"./ss\": 31991,\n\t\"./ss.js\": 31991,\n\t\"./sv\": 27020,\n\t\"./sv.js\": 27020,\n\t\"./sw\": 15891,\n\t\"./sw.js\": 15891,\n\t\"./ta\": 45714,\n\t\"./ta.js\": 45714,\n\t\"./te\": 30206,\n\t\"./te.js\": 30206,\n\t\"./tet\": 24768,\n\t\"./tet.js\": 24768,\n\t\"./tg\": 28276,\n\t\"./tg.js\": 28276,\n\t\"./th\": 57977,\n\t\"./th.js\": 57977,\n\t\"./tk\": 56928,\n\t\"./tk.js\": 56928,\n\t\"./tl-ph\": 8046,\n\t\"./tl-ph.js\": 8046,\n\t\"./tlh\": 41361,\n\t\"./tlh.js\": 41361,\n\t\"./tr\": 64367,\n\t\"./tr.js\": 64367,\n\t\"./tzl\": 10627,\n\t\"./tzl.js\": 10627,\n\t\"./tzm\": 12636,\n\t\"./tzm-latn\": 98148,\n\t\"./tzm-latn.js\": 98148,\n\t\"./tzm.js\": 12636,\n\t\"./ug-cn\": 68823,\n\t\"./ug-cn.js\": 68823,\n\t\"./uk\": 40461,\n\t\"./uk.js\": 40461,\n\t\"./ur\": 41366,\n\t\"./ur.js\": 41366,\n\t\"./uz\": 83454,\n\t\"./uz-latn\": 18374,\n\t\"./uz-latn.js\": 18374,\n\t\"./uz.js\": 83454,\n\t\"./vi\": 78572,\n\t\"./vi.js\": 78572,\n\t\"./x-pseudo\": 80464,\n\t\"./x-pseudo.js\": 80464,\n\t\"./yo\": 93709,\n\t\"./yo.js\": 93709,\n\t\"./zh-cn\": 65873,\n\t\"./zh-cn.js\": 65873,\n\t\"./zh-hk\": 17549,\n\t\"./zh-hk.js\": 17549,\n\t\"./zh-mo\": 52240,\n\t\"./zh-mo.js\": 52240,\n\t\"./zh-tw\": 90405,\n\t\"./zh-tw.js\": 90405\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 35358;", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: 'App',\n  components: {\n  },\n  mounted() {\n    this.updateTitle();\n  },\n  methods: {\n    updateTitle() {\n      document.title = 'II型光伏分布式电源测试系统';\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n}\n</style>\n", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=c59c44a2\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=c59c44a2&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-layout',{staticClass:\"admin-layout\"},[_c('a-layout-header',{staticClass:\"header\"},[_c('div',{staticClass:\"logo\"},[_vm._v(\"II型光伏分布式电源测试系统\")]),_c('div',{staticClass:\"header-right\"},[_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.showModal}},[_c('a-icon',{attrs:{\"type\":\"info-circle\"}}),_vm._v(\" 系统信息 \")],1)],1)]),_c('a-layout',[_c('a-layout-sider',{staticClass:\"sidebar\",attrs:{\"width\":\"200\"}},[_c('a-menu',{staticStyle:{\"height\":\"100%\"},attrs:{\"mode\":\"inline\"}},_vm._l((_vm.navItems),function(item,index){return _c('a-menu-item',{key:index + 1},[_c('router-link',{attrs:{\"to\":item.route}},[_c('a-icon',{attrs:{\"type\":item.icon}}),_c('span',[_vm._v(_vm._s(item.name))])],1)],1)}),1)],1),_c('a-layout-content',{staticClass:\"content\"},[_c('div',{staticClass:\"content-wrapper\"},[_c('router-view')],1)])],1),_c('a-modal',{attrs:{\"title\":\"系统信息\",\"visible\":_vm.modalVisible,\"footer\":null},on:{\"ok\":_vm.handleOk,\"cancel\":_vm.handleCancel}},[_c('div',{staticClass:\"container\"},[_c('div',{staticClass:\"info-card\"},[_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"后端名称:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.backendName))])]),_c('div',{staticClass:\"info-row\"},[_c('span',{staticClass:\"label\"},[_vm._v(\"后端版本:\")]),_c('span',{staticClass:\"value\"},[_vm._v(_vm._s(_vm.backendVersion))])])])])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import axios from 'axios';\r\nimport {notification} from 'ant-design-vue'\r\n\r\n// 获取当前网页的完整主机地址（包含协议、IP、端口等）\r\nconst currentHost = window.location.host;\r\n// 例如可能得到类似 'localhost:8080' 或者 'example.com'（如果没有端口则只显示域名）这样的结果\r\n\r\n// 可以进一步提取出IP与端口部分（如果是简单的本地开发环境等情况）\r\nconst [ip, port] = currentHost.split(':');\r\nconst defaultBackendAddress = `http://${ip || 'localhost'}:${port || '8080'}`;\r\n// const baseurl=`http://${JSON.parse(localStorage.getItem('backendAddress'))}`;\r\n\r\n// 创建axios实例\r\nconst service = axios.create({\r\n    baseURL: JSON.parse(localStorage.getItem('backendAddress')) || defaultBackendAddress,\r\n    // 先尝试从localStorage获取后端地址，如果不存在就用默认值\r\n    timeout: 5000 // 请求超时时间\r\n});\r\n\r\n// 请求拦截器\r\nservice.interceptors.request.use(\r\n    config => {\r\n        // 可以在这里添加请求头等信息\r\n        return config;\r\n    },\r\n    error => {\r\n        // 请求错误处理\r\n        console.log(error); // for debug\r\n        Promise.reject(error);\r\n    }\r\n);\r\n\r\n// 响应拦截器\r\nservice.interceptors.response.use(\r\n    response => {\r\n        // 对响应数据做处理，例如只返回data部分\r\n        return response.data;\r\n    },\r\n    error => {\r\n        // 响应错误处理\r\n        // 在这里捕获所有响应错误\r\n        console.log('全局拦截器捕获到错误:', error)\r\n        if(error.response!==undefined){\r\n            if(error.response.status === 400){\r\n                const errorMessage = error.response.data.message;\r\n                notification.error({\r\n                    message: '系统提示',\r\n                    description: errorMessage,\r\n                    duration: 4\r\n                });\r\n                // alert(`${errorMessage}`);\r\n            }\r\n            if(error.response.status === 500){\r\n                const errorMessage = error.response.data.message;\r\n                notification.error({\r\n                    message: '系统提示',\r\n                    description: errorMessage,\r\n                    duration: 4\r\n                });\r\n                // alert(`${errorMessage}`);\r\n            }\r\n        }\r\n        // 同样可以返回一个默认值来避免异常传播\r\n        return Promise.resolve({ data: [] });\r\n    }\r\n);\r\n\r\nexport default service;", "import service from '@/utils/api';\r\nimport axios from \"axios\";\r\n\r\n\r\n// 获取当前网页的完整主机地址（包含协议、IP、端口等）\r\nconst currentHost = window.location.host;\r\n// 例如可能得到类似 'localhost:8080' 或者 'example.com'（如果没有端口则只显示域名）这样的结果\r\n\r\n// 可以进一步提取出IP与端口部分（如果是简单的本地开发环境等情况）\r\nconst [ip, port] = currentHost.split(':');\r\nconst defaultBackendAddress = `http://${ip || 'localhost'}:${port || '8080'}`;\r\n// const baseurl=`http://${JSON.parse(localStorage.getItem('backendAddress'))}`;\r\n//首页\r\nexport function getInfo() {\r\n    return service({\r\n        url:'/api/info',\r\n        method: 'get',\r\n    })\r\n}\r\n\r\n//电源板测试\r\n//查询\r\nexport function checkTest(barcode,result,count,page) {\r\n    return service({\r\n        url:'/api/power',\r\n        method: 'get',\r\n        params: {\r\n            barcode:barcode,\r\n            result:result,\r\n            count:count,\r\n            page:page,\r\n        }\r\n    })\r\n}\r\n//添加\r\nexport function addTest(barcode,result,count) {\r\n    return service({\r\n        url:'/api/power',\r\n        method: 'post',\r\n        data: {\r\n            barcode:barcode,\r\n            result:result,\r\n            count:count,\r\n        }\r\n    })\r\n}\r\n\r\n//更新\r\nexport function updateTest(barcode,result) {\r\n    return service({\r\n        url:'/api/power',\r\n        method: 'put',\r\n        data: {\r\n            barcode:barcode,\r\n            result:result,\r\n        }\r\n    })\r\n}\r\n\r\n//整机组装\r\n\r\n//查询条码状态\r\nexport function checkState(barcode,type) {\r\n    return service({\r\n        url:'/api/barcode',\r\n        method: 'get',\r\n        params: {\r\n            type:type,\r\n            barcode:barcode,\r\n        }\r\n    })\r\n}\r\n//查询组装关系\r\nexport function checkRelate(powerBarcode,boardBarcode,wholeBarcode,page,count) {\r\n    return service({\r\n        url:'/api/assemble',\r\n        method: 'get',\r\n        params: {\r\n            powerBarcode:powerBarcode,\r\n            boardBarcode:boardBarcode,\r\n            wholeBarcode:wholeBarcode,\r\n            page:page,\r\n            count:count,\r\n        }\r\n    })\r\n}\r\n\r\n//添加组装关系\r\nexport function addRelate(powerBarcode,boardBarcode,wholeBarcode) {\r\n    return service({\r\n        url:'/api/assemble',\r\n        method: 'post',\r\n        data: {\r\n            powerBarcode:powerBarcode,\r\n            boardBarcode:boardBarcode,\r\n            wholeBarcode:wholeBarcode,\r\n        }\r\n    })\r\n}\r\n\r\n//更新组装关系\r\nexport function updateRelate(powerBarcode,boardBarcode,wholeBarcode) {\r\n    return service({\r\n        url:'/api/assemble',\r\n        method: 'put',\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n            \"powerBarcode\":powerBarcode,\r\n            \"boardBarcode\":boardBarcode,\r\n            \"wholeBarcode\":wholeBarcode,\r\n        }\r\n    })\r\n}\r\n//删除组装关系\r\nexport function deleteAction(data) {\r\n    return service({\r\n        url: '/api/assemble',\r\n        method: 'DELETE',\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        },\r\n        data: data\r\n    })\r\n}\r\n\r\n//写号配置\r\n//查询写号信息\r\nexport function checkNumber(address,barcode,meter,hardVersion,hardDate,produceDate,vendorCode,vendorExt,page,count) {\r\n    return service({\r\n        url: '/api/code',\r\n        method:'get' ,\r\n        params: {\r\n            barcode:barcode,\r\n            address:address,\r\n            meter:meter,\r\n            hardVersion:hardVersion,\r\n            hardDate:hardDate,\r\n            produceDate:produceDate,\r\n            vendorCode:vendorCode,\r\n            vendorExt:vendorExt,\r\n            page:page,\r\n            count:count,\r\n        }\r\n    })\r\n}\r\n//添加写号信息\r\nexport function addNumber(barcode,address,meter,hardVersion,hardDate,produceDate,vendorCode,vendorExt,mode,count) {\r\n    return service({\r\n        url:'/api/code',\r\n        method: 'post',\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n            \"barcode\":barcode,\r\n            \"address\":address,\r\n            \"meter\":meter,\r\n            \"hardVersion\":hardVersion,\r\n            \"hardDate\":hardDate,\r\n            \"produceDate\":produceDate,\r\n            \"vendorCode\":vendorCode,\r\n            \"vendorExt\":vendorExt,\r\n            \"mode\":mode,\r\n            \"count\":count,\r\n        }\r\n    })\r\n}\r\n//更新写号信息\r\nexport function updateNumber(barcode,address,meter,hardVersion,hardDate,produceDate,vendorCode,vendorExt,mode) {\r\n    return service({\r\n        url:'/api/code',\r\n        method: 'put',\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n            \"barcode\":barcode,\r\n            \"address\":address,\r\n            \"meter\":meter,\r\n            \"hardVersion\":hardVersion,\r\n            \"hardDate\":hardDate,\r\n            \"produceDate\":produceDate,\r\n            \"vendorCode\":vendorCode,\r\n            \"vendorExt\":vendorExt,\r\n            \"mode\":mode,\r\n        }\r\n    })\r\n}\r\n\r\n//数据管理\r\n//数据查询\r\nexport function dataManage(type,barcode,start,end,status,download,page,count) {\r\n    return service({\r\n        url: '/api/record',\r\n        method:'get' ,\r\n        params: {\r\n            type:type,\r\n            barcode:barcode,\r\n            start:start,\r\n            end:end,\r\n            status:status,\r\n            download:download,\r\n            page:page,\r\n            count:count,\r\n        }\r\n    })\r\n}\r\n//数据导出\r\nexport async function exportData(planType, barcode, start, end, status, page, count) {\r\n    try {\r\n        const downloadUrl = `${JSON.parse(localStorage.getItem('backendAddress')) || defaultBackendAddress}/api/record?planType=${planType}&barcode=${barcode}&start=${start}&end=${end}&status=${status}&download=true&page=${page}&count=${count}`;\r\n        const response = await axios({\r\n            method: 'get',\r\n            url: downloadUrl,\r\n            responseType: 'blob'\r\n        });\r\n        const blob = new Blob([response.data], { type: response.headers['content-type'] });\r\n        const link = document.createElement('a');\r\n        const href = URL.createObjectURL(blob);\r\n        // 将下载文件名从 exported_data.csv 更改为 exported_data.xlsx\r\n        link.href = href;\r\n        link.download = 'exported_data.xlsx';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        URL.revokeObjectURL(href);\r\n    } catch (error) {\r\n        console.error(error);\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n//系统设置\r\n//读取整机光伏模块校验参数\r\nexport function readParameter() {\r\n    return service({\r\n        url: '/api/config/whole',\r\n        method:'get' ,\r\n    })\r\n}\r\n\r\n//设置整机光伏模块校验参数\r\nexport function setParameter(url,softVersion) {\r\n    const promises = url.map(address => {\r\n        return axios({\r\n            url: `http://${address}/api/config/whole`,\r\n            method: 'put',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            data: {\r\n                \"soft_version\":softVersion,\r\n            }\r\n        });\r\n    });\r\n\r\n    return Promise.all(promises);\r\n}\r\n\r\n//获取支持的测试项列表\r\nexport function getTestItems() {\r\n    return service({\r\n        url:'/api/config/testitems',\r\n        method: 'get',\r\n    })\r\n}\r\n\r\n\r\n//更换资产编码\r\n//查询资产编码信息\r\nexport function checkAsset(barcode) {\r\n    return service({\r\n        url: '/api/codeused',\r\n        method:'get' ,\r\n        params: {\r\n            barcode:barcode,\r\n        }\r\n    })\r\n}\r\n//执行更换操作\r\nexport function updateAsset(barcode, newBarcode) {\r\n    return service({\r\n        url: '/api/codeused',\r\n        method: 'put',\r\n        headers: {\r\n            'Content-Type': 'application/json'\r\n        },\r\n        data: {\r\n            \"barcode\": barcode,\r\n            \"newBarcode\": newBarcode,\r\n        },\r\n        timeout: 60000 // 设置超时时间为60秒\r\n    });\r\n}", "<template>\r\n  <a-layout class=\"admin-layout\">\r\n    <a-layout-header class=\"header\">\r\n      <div class=\"logo\">II型光伏分布式电源测试系统</div>\r\n      <div class=\"header-right\">\r\n        <a-button type=\"primary\" @click=\"showModal\">\r\n          <a-icon type=\"info-circle\" />\r\n          系统信息\r\n        </a-button>\r\n      </div>\r\n    </a-layout-header>\r\n    <a-layout>\r\n      <a-layout-sider width=\"200\" class=\"sidebar\">\r\n        <a-menu\r\n            mode=\"inline\"\r\n            style=\"height: 100%\"\r\n        >\r\n          <a-menu-item v-for=\"(item, index) in navItems\" :key=\"index + 1\">\r\n            <router-link :to=\"item.route\">\r\n              <a-icon :type=\"item.icon\" />\r\n              <span>{{ item.name }}</span>\r\n            </router-link>\r\n          </a-menu-item>\r\n        </a-menu>\r\n      </a-layout-sider>\r\n      <a-layout-content class=\"content\">\r\n<!--        <a-breadcrumb style=\"text-align: left\">-->\r\n<!--          <a-breadcrumb-item>首页</a-breadcrumb-item>-->\r\n<!--          <a-breadcrumb-item>{{ currentRoute }}</a-breadcrumb-item>-->\r\n<!--        </a-breadcrumb>-->\r\n        <div class=\"content-wrapper\">\r\n          <router-view></router-view>\r\n        </div>\r\n      </a-layout-content>\r\n    </a-layout>\r\n\r\n    <a-modal\r\n        title=\"系统信息\"\r\n        :visible=\"modalVisible\"\r\n        @ok=\"handleOk\"\r\n        @cancel=\"handleCancel\"\r\n        :footer=\"null\"\r\n    >\r\n      <div class=\"container\">\r\n        <div class=\"info-card\">\r\n          <div class=\"info-row\">\r\n            <span class=\"label\">后端名称:</span>\r\n            <span class=\"value\">{{ backendName }}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"label\">后端版本:</span>\r\n            <span class=\"value\">{{ backendVersion }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </a-modal>\r\n  </a-layout>\r\n</template>\r\n\r\n<script>\r\nimport { EventBus } from '/src/main'\r\nimport {getInfo} from \"@/api/api\";\r\nexport default {\r\n  name: 'AdminLayout',\r\n  data() {\r\n    return {\r\n      backendName:'',\r\n      backendVersion:'',\r\n      navItems: [\r\n        {name: '数据管理', route: '/DataManage', icon: 'database'},\r\n        {name: '电源板测试', route: '/PowerBoardTest', icon: 'thunderbolt'},\r\n        {name: '板级测试', route: '/BoardLevelTest', icon: 'cluster'},\r\n        {name: '整机测试', route: '/WholeMachineTest', icon: 'desktop'},\r\n        {name: '整机复试', route: '/WholeMachineReTest', icon: 'sync'},\r\n        {name: '整机组装', route: '/CompleteMachineAssembly', icon: 'tool'},\r\n        {name: '更换资产编码', route: '/ChangeAssetCoding', icon: 'edit'},\r\n        {name: '写号配置', route: '/WriteNumberConfiguration', icon: 'setting'},\r\n        {name: '系统设置', route: '/SystemSetting', icon: 'setting'},\r\n      ],\r\n      modalVisible: false,\r\n    }\r\n  },\r\n  computed: {\r\n    currentRoute() {\r\n      return this.$route.name || 'Dashboard';\r\n    }\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    fetchData() {\r\n      EventBus.$on('updateBackendInfo', (newInfo) => {\r\n        this.backendName = newInfo.name\r\n        this.backendVersion = newInfo.version\r\n      })\r\n      if(this.backendName===''&&this.backendVersion===''){\r\n        getInfo().then(res => {\r\n          this.backendName = res.name\r\n          this.backendVersion = res.version\r\n        })\r\n      }\r\n    },\r\n    showModal() {\r\n      this.modalVisible = true;\r\n    },\r\n    handleOk() {\r\n      this.modalVisible = false;\r\n    },\r\n    handleCancel() {\r\n      this.modalVisible = false;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.admin-layout {\r\n  min-height: 100vh;\r\n}\r\n\r\n.logo {\r\n  color: rgba(0, 0, 0, 0.85);\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.header {\r\n  background: #f0f2f5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 24px;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.sidebar {\r\n  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);\r\n  position: relative;\r\n\r\n  background: #f0f2f5;\r\n}\r\n\r\n.content {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.content-wrapper {\r\n  background: #f0f2f5;\r\n  min-height: 360px;\r\n  border-radius: 2px;\r\n}\r\n.info-card { background-color: #fff; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); min-width: 300px; }\r\n.info-row { margin: 12px 0; font-size: 14px; color: #333; }\r\n.info-row:first-child { margin-top: 0; }\r\n.info-row:last-child { margin-bottom: 0; }\r\n.label { margin-right: 8px; }\r\n.value { font-weight: 500; }\r\n\r\n:deep(.ant-layout-sider) {\r\n  background: #f0f2f5;\r\n}\r\n\r\n:deep(.ant-menu) {\r\n  background: #f0f2f5;\r\n}\r\n\r\n:deep(.ant-menu-item-selected) {\r\n  background-color: #e6f7ff !important;\r\n}\r\n\r\n:deep(.ant-menu-item-selected a) {\r\n  color: #1890ff;\r\n}\r\n\r\n:deep(.ant-menu-item:hover) {\r\n  color: #1890ff;\r\n}\r\n\r\n:deep(.ant-breadcrumb) {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n:deep(.ant-breadcrumb-link) {\r\n  color: rgba(0, 0, 0, 0.65);\r\n}\r\n\r\n:deep(.ant-btn-primary) {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n:deep(.ant-btn-primary:hover) {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n:deep(.ant-menu-inline .ant-menu-item::after) {\r\n  border-right: 3px solid #1890ff;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Home.vue?vue&type=template&id=3f7ce64d&scoped=true\"\nimport script from \"./Home.vue?vue&type=script&lang=js\"\nexport * from \"./Home.vue?vue&type=script&lang=js\"\nimport style0 from \"./Home.vue?vue&type=style&index=0&id=3f7ce64d&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3f7ce64d\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"testing-interface\"},[_c('a-card',{attrs:{\"bordered\":false}},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.testData,\"pagination\":false,\"rowKey\":record => record.key,\"scroll\":{ x: 800 }},scopedSlots:_vm._u([{key:\"barcode\",fn:function(text, record){return [_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],class:_vm.getBarcodeInputClass(record),attrs:{\"maxLength\":20,\"placeholder\":\"请输入条码\",\"disabled\":record.isTesting},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleBarcodeSubmit(record)}},model:{value:(record.barcode),callback:function ($$v) {_vm.$set(record, \"barcode\", $$v)},expression:\"record.barcode\"}})]}},{key:\"device\",fn:function(text, record){return [_c('div',{staticClass:\"device-info\"},[_c('div',{staticClass:\"device-id\"},[_vm._v(_vm._s(record.device))]),_c('div',{staticClass:\"device-version\"},[_c('span',{staticClass:\"position\"},[_vm._v(_vm._s(record.name)+\"/\")]),_c('span',{staticClass:\"version\"},[_vm._v(\"v\"+_vm._s(record.version))])])])]}},{key:\"time\",fn:function(text, record){return [_c('span',{class:{\n          'time-default': !record.isTesting && (!record.testStatus || record.testStatus === 'waiting'),\n          'time-testing': record.isTesting || record.testStatus === 'testing',\n          'time-success': !record.isTesting && record.testStatus === 'success',\n          'time-error': !record.isTesting && record.testStatus === 'fail'\n        }},[_vm._v(_vm._s(record.time))])]}},_vm._l((_vm.testItems),function(item){return {key:item.key,fn:function(text, record){return [_c('a-tag',{key:item.key,staticStyle:{\"width\":\"50px\"},attrs:{\"color\":_vm.getStatusColor(record[item.key])}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record[item.key]))+\" \")])]}}})],null,true)})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"testing-interface\">\r\n    <a-card :bordered=\"false\">\r\n      <a-table\r\n          :columns=\"columns\"\r\n          :data-source=\"testData\"\r\n          :pagination=\"false\"\r\n          :rowKey=\"record => record.key\"\r\n          :scroll=\"{ x: 800 }\"\r\n\r\n      >\r\n        <!-- 条码输入框插槽 -->\r\n        <template slot=\"barcode\" slot-scope=\"text, record\">\r\n          <a-input\r\n              v-enter-next-input\r\n              v-model=\"record.barcode\"\r\n              :maxLength=\"20\"\r\n              placeholder=\"请输入条码\"\r\n              :disabled=\"record.isTesting\"\r\n              @keyup.enter=\"handleBarcodeSubmit(record)\"\r\n              :class=\"getBarcodeInputClass(record)\"\r\n          />\r\n        </template>\r\n\r\n        <!-- 工装信息显示插槽 -->\r\n        <template slot=\"device\" slot-scope=\"text, record\">\r\n          <div class=\"device-info\">\r\n            <div class=\"device-id\">{{ record.device }}</div>\r\n            <div class=\"device-version\">\r\n              <span class=\"position\">{{ record.name }}/</span>\r\n              <span class=\"version\">v{{ record.version }}</span>\r\n            </div>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 时间字段插槽 -->\r\n        <template slot=\"time\" slot-scope=\"text, record\">\r\n          <span :class=\"{\r\n            'time-default': !record.isTesting && (!record.testStatus || record.testStatus === 'waiting'),\r\n            'time-testing': record.isTesting || record.testStatus === 'testing',\r\n            'time-success': !record.isTesting && record.testStatus === 'success',\r\n            'time-error': !record.isTesting && record.testStatus === 'fail'\r\n          }\">{{ record.time }}</span>\r\n        </template>\r\n\r\n        <!-- 动态测试状态插槽 -->\r\n        <template v-for=\"item in testItems\" :slot=\"item.key\" slot-scope=\"text, record\">\r\n          <a-tag :key=\"item.key\" :color=\"getStatusColor(record[item.key])\" style=\"width: 50px\">\r\n            {{ getStatusText(record[item.key]) }}\r\n          </a-tag>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {message} from \"ant-design-vue\";\r\n\r\nexport default {\r\n  name: 'TestingInterface',\r\n  data() {\r\n    return {\r\n      testItems: [],\r\n      columns: [\r\n        {\r\n          title: '工装',\r\n          dataIndex: 'device',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'device' }\r\n        },\r\n        {\r\n          title: '时间',\r\n          dataIndex: 'time',\r\n          width: 80,\r\n          scopedSlots: { customRender: 'time' }\r\n        },\r\n        {\r\n          title: '条码',\r\n          dataIndex: 'barcode',\r\n          width: 180,\r\n          scopedSlots: { customRender: 'barcode' }\r\n        }\r\n      ],\r\n      testData: [],\r\n      websockets: [],\r\n      timers: {},\r\n      workbenchAddresses: [],\r\n      socketMap: [],\r\n    submissionStatus: {},\r\n    }\r\n  },\r\n  created() {\r\n    //board\r\n    const storedBackendUrl = localStorage.getItem('selectedBoardItems');\r\n    if (storedBackendUrl) {\r\n      this.testItems = JSON.parse(storedBackendUrl).map(item => ({\r\n        key: item.key,\r\n        title: item.title,\r\n      }));\r\n\r\n      // 动态添加测试项列\r\n      this.testItems.forEach(item => {\r\n        this.columns.push({\r\n          title: item.title,\r\n          dataIndex: item.key,\r\n          width: 150,\r\n          scopedSlots: { customRender: item.key }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // 验证条码\r\n    validateBarcode(barcode) {\r\n      const regex = /^\\d{4,20}$/\r\n      return regex.test(barcode)\r\n    },\r\n\r\n    updateSubmissionStatus(key, status) {\r\n      this.$set(this.submissionStatus, key, status)\r\n\r\n      setTimeout(() => {\r\n        this.$set(this.submissionStatus, key, null)\r\n      }, 2000)\r\n    },\r\n\r\n\r\n    getBarcodeInputClass(record) {\r\n      return {\r\n        'input-success': this.submissionStatus[record.key] === 'success',\r\n        'input-error': this.submissionStatus[record.key] === 'error'\r\n      }\r\n    },\r\n\r\n    // 处理条码提交\r\n    handleBarcodeSubmit(record) {\r\n      // 判断标志位，如果是指令导致的焦点转移就不执行后续业务逻辑\r\n      if (window.isDirectiveEnter) {\r\n        window.isDirectiveEnter = false; // 重置标志位，方便下次判断\r\n        return;\r\n      }\r\n      if (!this.validateBarcode(record.barcode)) {\r\n        message.error('条码必须为4-20位纯数字')\r\n        this.updateSubmissionStatus(record.key, 'error')\r\n        return\r\n      }\r\n\r\n      if (record.isTesting) {\r\n        message.warning('测试进行中，请等待测试完成')\r\n        return\r\n      }\r\n\r\n      this.websockets.forEach((socket, index) => {\r\n        const indexFind = this.socketMap.findIndex(socketObj => socketObj === record.device);\r\n        if (socket.readyState === WebSocket.OPEN && index === indexFind) {\r\n          socket.send(JSON.stringify({\r\n            type: 'boardCode',\r\n            planType: 'board',\r\n            device: record.name,\r\n            barcode: record.barcode\r\n          }))\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          // record.testStatus = null;\r\n          // record.isTesting = false; // 添加这行代码来重置测试中状态\r\n          // record.time = 0;\r\n        }\r\n      })\r\n    },\r\n    // 获取状态对应的颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        success: '#00FF99',\r\n        fail: '#FF3333',\r\n        testing: '#66CCFF',\r\n        waiting: '#999999'\r\n      };\r\n      return colorMap[status] || colorMap.waiting\r\n    },\r\n\r\n    // 获取状态对应的文本\r\n    getStatusText(status) {\r\n      const textMap = {\r\n        success: '成功',\r\n        fail: '失败',\r\n        testing: '测试中',\r\n        waiting: '未测试'\r\n      }\r\n      return textMap[status] || textMap.waiting\r\n    },\r\n\r\n    initWebSocket() {\r\n      const storedBackendUrl = localStorage.getItem('boardIPs');\r\n      this.workbenchAddresses = JSON.parse(storedBackendUrl).map(ip => `ws://${ip}/echo`);\r\n      this.workbenchAddresses.forEach((address) => {\r\n        let socket = new WebSocket(address);\r\n        let currentTimeout = 2000; // 初始超时时间设为2秒\r\n        let timeoutId;\r\n        const increaseTimeoutFactor = 1.5; // 超时时间增长系数，每次增加50%，可调整\r\n        let hasSentMessage = false; // 新增标识变量，用于记录是否已发送过消息\r\n\r\n        const checkConnection = () => {\r\n          if (socket.readyState!== WebSocket.OPEN) {\r\n            currentTimeout *= increaseTimeoutFactor; // 增加下次超时时间\r\n            if (currentTimeout > 10000) { // 最大超时时间限制为10秒，可调整\r\n              console.error(`WebSocket连接超时（地址：${address}）`);\r\n              message.error(`与工装（地址：${address}）的连接超时，请检查网络`);\r\n              socket.close();\r\n              return;\r\n            }\r\n            console.warn(`WebSocket连接未建立，正在延长超时等待时间至${currentTimeout}毫秒（地址：${address}）`);\r\n            message.warning(`与工装（地址：${address}）的连接较慢，正在尝试继续等待...`);\r\n            timeoutId = setTimeout(checkConnection, currentTimeout);\r\n          } else {\r\n            if (!hasSentMessage) { // 只有在未发送过消息时才发送\r\n              socket.send(JSON.stringify({\r\n                type: 'devicePlan',\r\n                planType: 'board',\r\n                items: this.testItems.map(item => item.key)\r\n              }));\r\n              hasSentMessage = true; // 标记已发送过消息\r\n            }\r\n            clearTimeout(timeoutId);\r\n          }\r\n        };\r\n        socket.onopen = () => {\r\n          console.log(`WebSocket连接已建立，地址：${address}`);\r\n          message.success(`WebSocket连接已建立，地址：${address}`);\r\n          if (!hasSentMessage) { // 同样判断，只有未发送过消息时才发送\r\n            socket.send(JSON.stringify({\r\n              type: 'devicePlan',\r\n              planType: 'board',\r\n              items: this.testItems.map(item => item.key)\r\n            }));\r\n            hasSentMessage = true; // 标记已发送过消息\r\n          }\r\n        };\r\n        socket.onerror = (error) => {\r\n          console.error(`WebSocket错误（地址：${address}）:`, error);\r\n          message.error(`与工装（地址：${address}）的连接出现错误，请检查网络`);\r\n        };\r\n        socket.onclose = () => {\r\n          console.log(`WebSocket连接已断开，地址：${address}`);\r\n          message.warning(`WebSocket连接已断开，地址：${address}`);\r\n        };\r\n        socket.onmessage = this.handleWebSocketMessage;\r\n\r\n        timeoutId = setTimeout(checkConnection, currentTimeout);\r\n        this.websockets.push(socket);\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(event) {\r\n      const data = JSON.parse(event.data)\r\n      console.log(data)\r\n\r\n      if (data.type === 'startTest') {\r\n        this.updateTestStatus(data)\r\n        return\r\n      }\r\n\r\n      switch (data.type) {\r\n        case 'devicePlan':\r\n          if(data.error_code === 0){\r\n            message.success('测试方案已配置成功')\r\n          } else {\r\n            message.warning(`${data.message}`)\r\n          }\r\n          break\r\n        case 'deviceInfo':\r\n          this.updateTestDataInfo(data)\r\n          break\r\n        case 'finishTest':\r\n        case 'failTest':\r\n          this.updateTestStatus(data)\r\n          break\r\n        case 'startItem':\r\n        case 'finishItem':\r\n        case 'failItem':\r\n          this.updateTestItemStatus(data)\r\n          break\r\n        case 'boardCode':\r\n          this.handleBarcodeResponse(data)\r\n          break\r\n        default:\r\n          console.warn('未知的消息类型:', data.type)\r\n      }\r\n    },\r\n    handleBarcodeResponse(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record) {\r\n        if (data.error_code === 0) {\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          record.testStatus = null\r\n          record.isTesting = false\r\n          record.time = 0\r\n        } else {\r\n          this.updateSubmissionStatus(record.key, 'error')\r\n          message.error(`条码提交失败: ${data.message}`)\r\n        }\r\n        this.$forceUpdate()\r\n      }\r\n    },\r\n    updateTestDataInfo(data) {\r\n      const newDataArr = data.devices.map((item, index) => {\r\n            const dataObj = {\r\n                key: `${data.name}-${item}-${index}`,\r\n                name: item,\r\n                device: data.name,\r\n                time: 0,\r\n                barcode: '',\r\n                version : data.version,\r\n              };\r\n        this.testItems.forEach(testItem => {\r\n          dataObj[testItem.key] = null; // 此处可根据实际业务场景灵活设置初始值，比如从其他地方获取合适的值来替换null\r\n        });\r\n        return dataObj;\r\n      });\r\n      this.testData = this.testData.concat(newDataArr);\r\n      this.socketMap.push(data.name)\r\n    },\r\n    // 更新测试状态\r\n    updateTestStatus(data) {\r\n      const record = this.testData.find(\r\n          item => item.name === data.devices &&\r\n              item.barcode === data.barcode\r\n      );\r\n\r\n      if (record) {\r\n        if (data.type === 'startTest') {\r\n          record.isTesting = true;\r\n          record.testStatus = 'testing';\r\n          this.startTimer(data);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          record.isTesting = false;\r\n          record.testStatus = data.type === 'finishTest'? 'success' : 'fail';\r\n          this.$set(record, 'testStatus', record.testStatus);\r\n          this.stopTimer(data);\r\n\r\n          setTimeout(() => {\r\n            if (data.type === 'finishTest') {\r\n              record.barcode = '';\r\n            }\r\n            this.$set(record, 'barcode', record.barcode);\r\n          }, 2000);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        }\r\n      }\r\n    },\r\n    updateTestItemStatus(data) {\r\n      const targetData  = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (targetData) {\r\n        if (data.type === 'startItem') {\r\n          targetData[data.item]='testing'\r\n          this.testData[targetData.key]=targetData\r\n        } else if (data.type === 'finishItem') {\r\n          targetData[data.item]='success'\r\n          this.testData[targetData.key]=targetData\r\n        } else {\r\n          targetData[data.item]='fail'\r\n          this.testData[targetData.key]=targetData\r\n        }\r\n      }\r\n    },\r\n    startTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && !this.timers[record.key]) {\r\n        let seconds = 0\r\n        this.timers[record.key] = setInterval(() => {\r\n          seconds++\r\n          record.time = this.formatTime(seconds)\r\n          this.testData[record.key].time=record.time\r\n        }, 1000)\r\n      }\r\n    },\r\n\r\n    stopTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && this.timers[record.key]) {\r\n        clearInterval(this.timers[record.key])\r\n        delete this.timers[record.key]\r\n      }\r\n    },\r\n\r\n    formatTime(seconds) {\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = seconds % 60\r\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.initWebSocket();\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    Object.keys(this.timers).forEach(key => {\r\n      clearInterval(this.timers[key]);\r\n    });\r\n    this.websockets.forEach((socket) => {\r\n      if (socket) {\r\n        socket.close();\r\n      }\r\n    });\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.testing-interface {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.device-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.device-id {\r\n  font-weight: 500;\r\n}\r\n\r\n.device-version {\r\n  display: flex;\r\n  color: #666;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #1890ff;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #52c41a;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-error {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.input-success {\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 12px 8px;\r\n}\r\n\r\n:deep(.ant-input) {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.ant-tag) {\r\n  margin: 0;\r\n  padding: 4px 8px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.input-success {\r\n  background-color: #f6ffed;\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  background-color: #fff1f0;\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n.time-default {\r\n  background-color: white;\r\n  color: black;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.time-error {\r\n  background-color: #fff1f0;\r\n  color: #ff4d4f;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./BoardLevelTest.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./BoardLevelTest.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./BoardLevelTest.vue?vue&type=template&id=368db563&scoped=true\"\nimport script from \"./BoardLevelTest.vue?vue&type=script&lang=js\"\nexport * from \"./BoardLevelTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./BoardLevelTest.vue?vue&type=style&index=0&id=368db563&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"368db563\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"data-management\"},[_c('a-card',{staticClass:\"query-card\",attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"inline\"},on:{\"submit\":_vm.handleQuery}},[_c('a-form-item',{attrs:{\"label\":\"测试方案类型\"}},[_c('a-select',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['planType']),expression:\"['planType']\"}],staticStyle:{\"width\":\"140px\"},attrs:{\"placeholder\":\"请选择\"},on:{\"change\":_vm.handlePlanTypeChange}},[_c('a-select-option',{attrs:{\"value\":\"power\"}},[_vm._v(\"电源板条码\")]),_c('a-select-option',{attrs:{\"value\":\"board\"}},[_vm._v(\"主板条码\")]),_c('a-select-option',{attrs:{\"value\":\"whole\"}},[_vm._v(\"整机资产条码\")])],1)],1),_c('a-form-item',{attrs:{\"label\":\"条码\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['barcode', { rules: [{ validator: _vm.validateBarcode }] }]),expression:\"['barcode', { rules: [{ validator: validateBarcode }] }]\"}],attrs:{\"placeholder\":\"请输入条码\",\"maxLength\":22}})],1),_c('a-form-item',{attrs:{\"label\":\"时间范围\"}},[_c('a-range-picker',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['dateRange']),expression:\"['dateRange']\"}],staticClass:\"custom-range-picker\",attrs:{\"placeholder\":['开始日期', '结束日期'],\"locale\":_vm.locale},on:{\"change\":_vm.handleDateRangeChange}})],1),_c('a-form-item',{attrs:{\"label\":\"测试状态\"}},[_c('a-select',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['status', { initialValue: 0 }]),expression:\"['status', { initialValue: 0 }]\"}],staticStyle:{\"width\":\"180px\"}},[_c('a-select-option',{attrs:{\"value\":0}},[_vm._v(\"全部状态\")]),_c('a-select-option',{attrs:{\"value\":1}},[_vm._v(\"全部测完且全部合格\")]),_c('a-select-option',{attrs:{\"value\":2}},[_vm._v(\"全部测完存在不合格\")]),_c('a-select-option',{attrs:{\"value\":3}},[_vm._v(\"有未测试项\")])],1)],1),_c('a-form-item',[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\"},on:{\"click\":_vm.handleQuery}},[_vm._v(\"查询\")]),_c('a-button',{staticStyle:{\"margin-left\":\"8px\"},on:{\"click\":_vm.handleReset}},[_vm._v(\"重置\")])],1)],1)],1),_c('a-card',{staticClass:\"result-card\",attrs:{\"bordered\":false}},[_c('template',{slot:\"extra\"},[_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.exportToExcel}},[_c('a-icon',{attrs:{\"type\":\"download\"}}),_vm._v(\"导出数据 \")],1)],1),_c('a-table',{attrs:{\"columns\":_vm.columns,\"dataSource\":_vm.data,\"loading\":_vm.loading,\"pagination\":_vm.pagination},on:{\"change\":_vm.handleTableChange},scopedSlots:_vm._u([{key:\"powerItem\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.powerItem)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.powerItem))+\" \")])]}},{key:\"boardItem\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.boardItem)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.boardItem))+\" \")])]}},{key:\"wholeItem\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.wholeItem)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.wholeItem))+\" \")])]}},{key:\"rewholeItem\",fn:function(text, record){return [_c('a-tag',{attrs:{\"color\":_vm.getStatusColor(record.rewholeItem)}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record.rewholeItem))+\" \")])]}}])})],2)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"data-management\">\r\n    <a-card class=\"query-card\" :bordered=\"false\">\r\n      <a-form :form=\"form\" layout=\"inline\" @submit=\"handleQuery\">\r\n        <a-form-item label=\"测试方案类型\">\r\n          <a-select v-decorator=\"['planType']\" style=\"width: 140px\" @change=\"handlePlanTypeChange\" placeholder=\"请选择\">\r\n            <a-select-option value=\"power\">电源板条码</a-select-option>\r\n            <a-select-option value=\"board\">主板条码</a-select-option>\r\n            <a-select-option value=\"whole\">整机资产条码</a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n        <a-form-item label=\"条码\">\r\n          <a-input v-decorator=\"['barcode', { rules: [{ validator: validateBarcode }] }]\" placeholder=\"请输入条码\" :maxLength=\"22\"/>\r\n        </a-form-item>\r\n        <a-form-item label=\"时间范围\">\r\n          <a-range-picker\r\n              :placeholder=\"['开始日期', '结束日期']\"\r\n              :locale=\"locale\"\r\n              v-decorator=\"['dateRange']\"\r\n              class=\"custom-range-picker\"\r\n              @change=\"handleDateRangeChange\"\r\n          />\r\n        </a-form-item>\r\n        <a-form-item label=\"测试状态\">\r\n          <a-select v-decorator=\"['status', { initialValue: 0 }]\" style=\"width: 180px\">\r\n            <a-select-option :value=\"0\">全部状态</a-select-option>\r\n            <a-select-option :value=\"1\">全部测完且全部合格</a-select-option>\r\n            <a-select-option :value=\"2\">全部测完存在不合格</a-select-option>\r\n            <a-select-option :value=\"3\">有未测试项</a-select-option>\r\n          </a-select>\r\n        </a-form-item>\r\n        <a-form-item>\r\n          <a-button type=\"primary\" html-type=\"submit\" @click=\"handleQuery\">查询</a-button>\r\n          <a-button style=\"margin-left: 8px\" @click=\"handleReset\">重置</a-button>\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-card>\r\n\r\n    <a-card class=\"result-card\" :bordered=\"false\">\r\n      <template slot=\"extra\">\r\n        <a-button type=\"primary\" @click=\"exportToExcel\">\r\n          <a-icon type=\"download\" />导出数据\r\n        </a-button>\r\n      </template>\r\n      <a-table\r\n          :columns=\"columns\"\r\n          :dataSource=\"data\"\r\n          :loading=\"loading\"\r\n          :pagination=\"pagination\"\r\n          @change=\"handleTableChange\"\r\n      >\r\n        <template slot=\"powerItem\" slot-scope=\"text, record\">\r\n          <a-tag :color=\"getStatusColor(record.powerItem)\">\r\n            {{ getStatusText(record.powerItem) }}\r\n          </a-tag>\r\n        </template>\r\n        <template slot=\"boardItem\" slot-scope=\"text, record\">\r\n          <a-tag :color=\"getStatusColor(record.boardItem)\">\r\n            {{ getStatusText(record.boardItem) }}\r\n          </a-tag>\r\n        </template>\r\n        <template slot=\"wholeItem\" slot-scope=\"text, record\">\r\n          <a-tag :color=\"getStatusColor(record.wholeItem)\">\r\n            {{ getStatusText(record.wholeItem) }}\r\n          </a-tag>\r\n        </template>\r\n        <template slot=\"rewholeItem\" slot-scope=\"text, record\">\r\n          <a-tag :color=\"getStatusColor(record.rewholeItem)\">\r\n            {{ getStatusText(record.rewholeItem) }}\r\n          </a-tag>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { dataManage, exportData } from \"@/api/api\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      locale: {\r\n        lang: {\r\n          placeholder: '请选择日期',\r\n          yearPlaceholder: '年',\r\n          monthPlaceholder: '月',\r\n          dayPlaceholder: '日',\r\n          weekPlaceholder: '周',\r\n          rangePlaceholder: ['开始日期', '结束日期'],\r\n          today: '今天',\r\n          now: '此刻',\r\n          ok: '确定',\r\n          clear: '清除',\r\n          prevYear: '去年',\r\n          nextYear: '明年',\r\n          prevMonth: '上月',\r\n          nextMonth: '下月',\r\n          monthSelect: '选择月份',\r\n          yearSelect: '选择年份',\r\n          decadeSelect: '选择年代',\r\n          yearFormat: 'YYYY年',\r\n          monthFormat: 'MM月',\r\n          dateFormat: 'YYYY-MM-DD',\r\n          dayFormat: 'D日',\r\n          dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',\r\n          timeFormat: 'HH:mm:ss',\r\n          secondFormat: 'ss秒',\r\n          meridiem: '上午/下午',\r\n          am: '上午',\r\n          pm: '下午',\r\n        },\r\n        timePickerLocale: {\r\n          placeholder: '请选择时间',\r\n        },\r\n      },\r\n      form: this.$form.createForm(this),\r\n      loading: false,\r\n      data: [],\r\n      pagination: {\r\n        current: 1,\r\n        pageSize: 10,\r\n        pageSizeOptions: ['10', '20', '50', '100'],\r\n        showTotal: (total, range) => {\r\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\r\n        },\r\n        showQuickJumper: true,\r\n        showSizeChanger: true,\r\n        total: 0\r\n      },\r\n      columns: [\r\n        {title: '电源板条码', dataIndex: 'powerBarcode', key: 'powerBarcode'},\r\n        {title: '主板条码', dataIndex: 'boardBarcode', key: 'boardBarcode'},\r\n        {title: '资产条码', dataIndex: 'wholeBarcode', key: 'wholeBarcode'},\r\n        {title: '电源板测试结果', dataIndex: 'powerItem', key: 'powerItem', scopedSlots: {customRender: 'powerItem'}},\r\n        {title: '主板测试结果', dataIndex: 'boardItem', key: 'boardItem', scopedSlots: {customRender: 'boardItem'}},\r\n        {title: '整机测试结果', dataIndex: 'wholeItem', key: 'wholeItem', scopedSlots: {customRender: 'wholeItem'}},\r\n        {title: '整机复测结果', dataIndex: 'rewholeItem', key: 'rewholeItem', scopedSlots: {customRender: 'rewholeItem'}},\r\n      ],\r\n      startTime: 0,\r\n      endTime: 0,\r\n      excelData: [[\"\", \"电源板条码\", \"电源板测试结果\", \"主板条码\", \"主板测试结果\", \"资产条码\", \"整机测试结果\", \"整机复测结果\"]],\r\n      currentPlanType: '',\r\n    }\r\n  },\r\n  methods: {\r\n    handlePlanTypeChange(value) {\r\n      this.currentPlanType = value;\r\n      this.form.setFieldsValue({ barcode: '' });\r\n      this.form.validateFields(['barcode'], { force: true });\r\n    },\r\n    validateBarcode(rule, value, callback) {\r\n      if(this.currentPlanType){\r\n        if (!value) {\r\n          callback('请输入条码');\r\n          return;\r\n        }\r\n      }\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n      const numberRegex = /^\\d+$/;\r\n      if (!numberRegex.test(value)) {\r\n        callback('请输入纯数字');\r\n        return;\r\n      }\r\n      switch (this.currentPlanType) {\r\n        case 'power':\r\n        case 'board':\r\n          if (value.length < 4 || value.length > 20) {\r\n            callback('请输入4-20位纯数字');\r\n          } else {\r\n            callback();\r\n          }\r\n          break;\r\n        case 'whole':\r\n          if (value.length !== 22) {\r\n            callback('请输入22位纯数字');\r\n          } else {\r\n            callback();\r\n          }\r\n          break;\r\n        default:\r\n          callback();\r\n      }\r\n    },\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.startTime = dates[0].startOf('day').unix();\r\n        this.endTime = dates[1].endOf('day').unix();\r\n      } else {\r\n        this.startTime = 0;\r\n        this.endTime = 0;\r\n      }\r\n    },\r\n    handleQuery(e) {\r\n      e.preventDefault();\r\n      this.form.validateFields((err, values) => {\r\n        if (!err) {\r\n          this.fetchData(values);\r\n        }\r\n      });\r\n    },\r\n    handleReset() {\r\n      this.form.resetFields();\r\n      this.currentPlanType = '';\r\n      this.startTime = 0;\r\n      this.endTime = 0;\r\n    },\r\n    exportToExcel() {\r\n      this.form.validateFields((err, values) => {\r\n        if (!err) {\r\n          const planType = values.planType || '';\r\n          const barcode = values.barcode || '';\r\n          exportData(planType, barcode, this.startTime, this.endTime, values.status, this.pagination.current, this.pagination.pageSize).catch((error) => {\r\n            console.log(error);\r\n          });\r\n        } else {\r\n          this.$message.error('请修正输入错误后再导出');\r\n        }\r\n      });\r\n    },\r\n    handleTableChange(pagination) {\r\n      this.pagination = pagination;\r\n      this.form.validateFields((err, values) => {\r\n        if (!err) {\r\n          this.fetchData(values);\r\n        }\r\n      });\r\n    },\r\n    fetchData(values) {\r\n      this.loading = true;\r\n      const planType = values.planType || '';\r\n      const barcode = values.barcode || '';\r\n      console.log(this.startTime)\r\n      console.log(this.endTime)\r\n\r\n      dataManage(planType, barcode, this.startTime, this.endTime, values.status, false, this.pagination.current, this.pagination.pageSize).then(res => {\r\n        this.pagination.total = res.page_count * this.pagination.pageSize;\r\n        this.data = res.records.map((item, index) => ({\r\n          key: index,\r\n          powerBarcode: item.powerBarcode,\r\n          powerItem: item.powerItem,\r\n          boardBarcode: item.boardBarcode,\r\n          boardItem: item.boardItem,\r\n          wholeBarcode: item.wholeBarcode,\r\n          wholeItem: item.wholeItem,\r\n          rewholeItem: item.rewholeItem,\r\n        }));\r\n      }).catch((error) => {\r\n        console.log(error);\r\n        this.$message.error('获取数据失败');\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getStatusColor(status) {\r\n      const colors = ['orange', 'green', 'red'];\r\n      if (Object.keys(status).length === 0) {\r\n        return colors[0];\r\n      }\r\n      let hasEmpty = false;\r\n      let allQualified = true;\r\n      for (const key in status) {\r\n        const value = status[key];\r\n        if (value === \"\") {\r\n          hasEmpty = true;\r\n          break;\r\n        } else if (value === \"不合格\") {\r\n          allQualified = false;\r\n        }\r\n      }\r\n      if (hasEmpty) {\r\n        return colors[0];\r\n      } else if (allQualified) {\r\n        return colors[1];\r\n      }\r\n      return colors[2];\r\n    },\r\n    getStatusText(status) {\r\n      const texts = ['有未测试项', '全部测完且全部合格', '全部测完存在不合格'];\r\n      if (Object.keys(status).length === 0) {\r\n        return texts[0];\r\n      }\r\n      let hasEmpty = false;\r\n      let allQualified = true;\r\n      for (const key in status) {\r\n        const value = status[key];\r\n        if (value === \"\") {\r\n          hasEmpty = true;\r\n          break;\r\n        } else if (value === \"不合格\") {\r\n          allQualified = false;\r\n        }\r\n      }\r\n      if (hasEmpty) {\r\n        return texts[0];\r\n      } else if (allQualified) {\r\n        return texts[1];\r\n      }\r\n      return texts[2];\r\n    },\r\n  },\r\n  mounted() {\r\n    this.fetchData(this.form.getFieldsValue());\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-management {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.query-card {\r\n  margin-bottom: 24px;\r\n  background: #FFFFE0;\r\n}\r\n\r\n.result-card {\r\n  background: #fff;\r\n}\r\n\r\n.ant-form-item {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ant-form {\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .ant-form-item {\r\n    margin-right: 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.custom-range-picker {\r\n  width: 200px;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./DataManage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./DataManage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./DataManage.vue?vue&type=template&id=b50d7ad8&scoped=true\"\nimport script from \"./DataManage.vue?vue&type=script&lang=js\"\nexport * from \"./DataManage.vue?vue&type=script&lang=js\"\nimport style0 from \"./DataManage.vue?vue&type=style&index=0&id=b50d7ad8&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b50d7ad8\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('a-layout',{staticClass:\"layout\"},[_c('div',{style:({ background: '#fff', padding: '24px', minHeight: '280px' })},[_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":16}},[_c('a-card',{attrs:{\"title\":\"测试工装状态\",\"bordered\":false}},[_c('a-table',{attrs:{\"columns\":_vm.fixtureColumns,\"data-source\":_vm.fixtureData,\"pagination\":false},scopedSlots:_vm._u([{key:\"status\",fn:function(text){return [_c('a-tag',{attrs:{\"color\":text === '运行中' ? 'green' : text === '空闲' ? 'blue' : 'red'}},[_vm._v(\" \"+_vm._s(text)+\" \")])]}}])})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-card',{attrs:{\"title\":\"系统概览\",\"bordered\":false}},[_c('a-statistic-card',[_c('a-statistic',{staticStyle:{\"margin-top\":\"15%\",\"display\":\"block\"},attrs:{\"title\":\"在线测试工装\",\"value\":_vm.onlineFixtures,\"precision\":0},scopedSlots:_vm._u([{key:\"prefix\",fn:function(){return [_c('a-icon',{attrs:{\"type\":\"cluster\"}})]},proxy:true}])}),_c('a-statistic',{staticStyle:{\"margin-top\":\"30px\",\"display\":\"block\"},attrs:{\"title\":\"正在测试的模块\",\"value\":_vm.testingModules,\"precision\":0},scopedSlots:_vm._u([{key:\"prefix\",fn:function(){return [_c('a-icon',{attrs:{\"type\":\"experiment\"}})]},proxy:true}])})],1)],1)],1)],1),_c('a-card',{staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":\"当前测试\"}},[_c('a-table',{attrs:{\"columns\":_vm.testColumns,\"data-source\":_vm.testData},scopedSlots:_vm._u([{key:\"progress\",fn:function(text, record){return [_c('a-progress',{attrs:{\"percent\":record.progress,\"size\":\"small\"}})]}},{key:\"status\",fn:function(text){return [_c('a-tag',{attrs:{\"color\":text === '进行中' ? 'blue' : text === '完成' ? 'green' : 'orange'}},[_vm._v(\" \"+_vm._s(text)+\" \")])]}}])})],1),_c('a-card',{staticStyle:{\"margin-top\":\"16px\"},attrs:{\"title\":\"最近测试结果\"}},[_c('a-list',{attrs:{\"itemLayout\":\"horizontal\",\"dataSource\":_vm.recentResults},scopedSlots:_vm._u([{key:\"renderItem\",fn:function(item){return _c('a-list-item',{},[_c('a-list-item-meta',{attrs:{\"description\":item.description}},[_c('template',{slot:\"title\"},[_c('a',{attrs:{\"href\":\"javascript:;\"}},[_vm._v(_vm._s(item.title))])]),_c('a-avatar',{style:({ backgroundColor: item.passed ? '#52c41a' : '#f5222d' }),attrs:{\"slot\":\"avatar\",\"icon\":item.passed ? 'check' : 'close'},slot:\"avatar\"})],2),_c('div',[_vm._v(_vm._s(item.time))])],1)}}])})],1)],1),_c('a-layout-footer',{staticStyle:{\"text-align\":\"center\"}},[_vm._v(\" II型光伏分布式电源测试系统 ©2023 Created by Your Company \")])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <a-layout class=\"layout\">\r\n      <div :style=\"{ background: '#fff', padding: '24px', minHeight: '280px' }\">\r\n        <a-row :gutter=\"16\">\r\n          <a-col :span=\"16\">\r\n            <a-card title=\"测试工装状态\" :bordered=\"false\">\r\n              <a-table :columns=\"fixtureColumns\" :data-source=\"fixtureData\" :pagination=\"false\">\r\n                <template slot=\"status\" slot-scope=\"text\">\r\n                  <a-tag :color=\"text === '运行中' ? 'green' : text === '空闲' ? 'blue' : 'red'\">\r\n                    {{ text }}\r\n                  </a-tag>\r\n                </template>\r\n              </a-table>\r\n            </a-card>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-card title=\"系统概览\" :bordered=\"false\">\r\n              <a-statistic-card>\r\n                <a-statistic\r\n                    title=\"在线测试工装\"\r\n                    :value=\"onlineFixtures\"\r\n                    :precision=\"0\"\r\n                    style=\"margin-top: 15%;display: block\"\r\n                >\r\n                  <template #prefix>\r\n                    <a-icon type=\"cluster\" />\r\n                  </template>\r\n                </a-statistic>\r\n                <a-statistic\r\n                    title=\"正在测试的模块\"\r\n                    :value=\"testingModules\"\r\n                    :precision=\"0\"\r\n                    style=\"margin-top: 30px;display: block\"\r\n                >\r\n                  <template #prefix>\r\n                    <a-icon type=\"experiment\" />\r\n                  </template>\r\n                </a-statistic>\r\n              </a-statistic-card>\r\n            </a-card>\r\n          </a-col>\r\n        </a-row>\r\n\r\n        <a-card title=\"当前测试\" style=\"margin-top: 16px\">\r\n          <a-table :columns=\"testColumns\" :data-source=\"testData\">\r\n            <template slot=\"progress\" slot-scope=\"text, record\">\r\n              <a-progress :percent=\"record.progress\" size=\"small\" />\r\n            </template>\r\n            <template slot=\"status\" slot-scope=\"text\">\r\n              <a-tag :color=\"text === '进行中' ? 'blue' : text === '完成' ? 'green' : 'orange'\">\r\n                {{ text }}\r\n              </a-tag>\r\n            </template>\r\n          </a-table>\r\n        </a-card>\r\n\r\n        <a-card title=\"最近测试结果\" style=\"margin-top: 16px\">\r\n          <a-list\r\n              itemLayout=\"horizontal\"\r\n              :dataSource=\"recentResults\"\r\n          >\r\n            <a-list-item slot=\"renderItem\" slot-scope=\"item\">\r\n              <a-list-item-meta\r\n                  :description=\"item.description\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <a href=\"javascript:;\">{{ item.title }}</a>\r\n                </template>\r\n                <a-avatar\r\n                    slot=\"avatar\"\r\n                    :style=\"{ backgroundColor: item.passed ? '#52c41a' : '#f5222d' }\"\r\n                    :icon=\"item.passed ? 'check' : 'close'\"\r\n                />\r\n              </a-list-item-meta>\r\n              <div>{{ item.time }}</div>\r\n            </a-list-item>\r\n          </a-list>\r\n        </a-card>\r\n      </div>\r\n\r\n    <a-layout-footer style=\"text-align: center\">\r\n      II型光伏分布式电源测试系统 ©2023 Created by Your Company\r\n    </a-layout-footer>\r\n  </a-layout>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      fixtureColumns: [\r\n        { title: '工装ID', dataIndex: 'id', key: 'id' },\r\n        { title: '状态', dataIndex: 'status', key: 'status', scopedSlots: { customRender: 'status' } },\r\n        { title: '当前测试模块', dataIndex: 'currentModule', key: 'currentModule' },\r\n        { title: '已完成测试项', dataIndex: 'completedTests', key: 'completedTests' },\r\n      ],\r\n      fixtureData: [\r\n        { id: 'F001', status: '运行中', currentModule: 'PV-M-001', completedTests: 3 },\r\n        { id: 'F002', status: '空闲', currentModule: '-', completedTests: 0 },\r\n        { id: 'F003', status: '运行中', currentModule: 'PV-M-002', completedTests: 1 },\r\n        { id: 'F004', status: '故障', currentModule: '-', completedTests: 0 },\r\n      ],\r\n      onlineFixtures: 3,\r\n      testingModules: 2,\r\n      testColumns: [\r\n        { title: '模块ID', dataIndex: 'moduleId', key: 'moduleId' },\r\n        { title: '测试项目', dataIndex: 'testItem', key: 'testItem' },\r\n        { title: '进度', dataIndex: 'progress', key: 'progress', scopedSlots: { customRender: 'progress' } },\r\n        { title: '状态', dataIndex: 'status', key: 'status', scopedSlots: { customRender: 'status' } },\r\n      ],\r\n      testData: [\r\n        { moduleId: 'PV-M-001', testItem: '电源板测试', progress: 75, status: '进行中' },\r\n        { moduleId: 'PV-M-002', testItem: '板级测试', progress: 30, status: '进行中' },\r\n        { moduleId: 'PV-M-003', testItem: '整机测试', progress: 100, status: '完成' },\r\n      ],\r\n      recentResults: [\r\n        { title: 'PV-M-004 测试完成', description: '电源板测试项目通过', passed: true, time: '2023-05-20 14:30' },\r\n        { title: 'PV-M-005 测试完成', description: '板级测试未通过', passed: false, time: '2023-05-20 13:45' },\r\n        { title: 'PV-M-006 测试完成', description: '整机测试项目通过', passed: true, time: '2023-05-20 12:15' },\r\n      ],\r\n    };\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.layout {\r\n  min-height: 100vh;\r\n}\r\n.logo {\r\n  float: left;\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HomePage.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./HomePage.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./HomePage.vue?vue&type=template&id=7f775ba0&scoped=true\"\nimport script from \"./HomePage.vue?vue&type=script&lang=js\"\nexport * from \"./HomePage.vue?vue&type=script&lang=js\"\nimport style0 from \"./HomePage.vue?vue&type=style&index=0&id=7f775ba0&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7f775ba0\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"power-supply-test\"},[_c('a-card',{staticClass:\"query-card\",attrs:{\"bordered\":false}},[_c('a-col',{attrs:{\"span\":24}},[_c('a-form',{attrs:{\"form\":_vm.form},on:{\"submit\":_vm.handleSubmit}},[_c('a-col',{attrs:{\"span\":7}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"电源板条码\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['startCode', { rules: [{ required: true, message: '请输入电源板条码' }, { validator: _vm.validateNumber }] }]),expression:\"['startCode', { rules: [{ required: true, message: '请输入电源板条码' }, { validator: validateNumber }] }]\"}],attrs:{\"placeholder\":\"请输入电源板条码\",\"maxLength\":20}})],1)],1),_c('a-col',{attrs:{\"span\":6}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"连续的条码数量\"}},[_c('a-input-group',{attrs:{\"compact\":\"\"}},[_c('a-input-number',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['quantity', { rules: [{ required: true, message: '请输入数量' }, { validator: _vm.validateCount }] }]),expression:\"['quantity', { rules: [{ required: true, message: '请输入数量' }, { validator: validateCount }] }]\"}],staticStyle:{\"width\":\"70%\"},attrs:{\"min\":1,\"max\":10000}})],1)],1)],1),_c('a-col',{attrs:{\"span\":3}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"状态\"}},[_c('a-switch',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['status', { valuePropName: 'checked', initialValue: true }]),expression:\"['status', { valuePropName: 'checked', initialValue: true }]\"}],attrs:{\"checked-children\":\"合格\",\"un-checked-children\":\"不合格\"}})],1)],1),_c('a-col',{attrs:{\"span\":3}},[_c('a-form-item',[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"save\"},on:{\"click\":_vm.batchSave}},[_vm._v(\"添加\")])],1)],1)],1)],1)],1),_c('a-card',{staticClass:\"query-card_\",attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"form\":_vm.queryForm,\"layout\":\"inline\"},on:{\"submit\":_vm.handleQuery}},[_c('a-row',{staticStyle:{\"height\":\"80px\",\"background-color\":\"#f0f9eb\",\"display\":\"flex\",\"align-items\":\"center\"},attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"条码\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['queryCode', { rules: [{ validator: _vm.validateNumber }]}]),expression:\"['queryCode', { rules: [{ validator: validateNumber }]}]\"}],attrs:{\"placeholder\":\"请输入条码\",\"maxLength\":20}})],1)],1),_c('a-col',{attrs:{\"span\":4}},[_c('a-form-item',{attrs:{\"label\":\"状态\"}},[_c('a-select',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['queryStatus']),expression:\"['queryStatus']\"}],staticStyle:{\"width\":\"120px\"},attrs:{\"placeholder\":\"请选择状态\"}},[_c('a-select-option',{attrs:{\"value\":\"\"}},[_vm._v(\"全部\")]),_c('a-select-option',{attrs:{\"value\":\"true\"}},[_vm._v(\"合格\")]),_c('a-select-option',{attrs:{\"value\":\"false\"}},[_vm._v(\"不合格\")])],1)],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"search\"},on:{\"click\":_vm.handleQuery}},[_vm._v(\"查询\")])],1)],1)],1)],1),_c('a-table',{staticStyle:{\"margin-top\":\"20px\"},attrs:{\"rowKey\":\"key\",\"columns\":_vm.columns,\"dataSource\":_vm.queryResults,\"pagination\":_vm.detailIpagination},on:{\"change\":_vm.detailhandleTableChange},scopedSlots:_vm._u([{key:\"status\",fn:function(text){return [_c('a-tag',{attrs:{\"color\":text ? 'green' : 'red'}},[_vm._v(\" \"+_vm._s(text ? '合格' : '不合格')+\" \")])]}},{key:\"action\",fn:function(text, record){return [(!record.code)?_c('span',{staticClass:\"no-code\"},[_vm._v(\"无条码\")]):_vm._e(),_c('a-button',{staticClass:\"update-button\",attrs:{\"type\":\"primary\",\"icon\":\"edit\"},on:{\"click\":function($event){return _vm.updateData(record)}}},[_vm._v(\" 更新 \")])]}}])})],1),_c('UpdatePowerBoardTest',{ref:\"UpdatePowerBoardTest\",attrs:{\"initialCode\":_vm.barcode,\"initialStatus\":_vm.isStatus},on:{\"child-message\":_vm.handleChildMessage}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-modal',{attrs:{\"title\":\"更新电源板状态\",\"visible\":_vm.visible,\"confirm-loading\":_vm.confirmLoading,\"footer\":null},on:{\"ok\":_vm.handleOk,\"cancel\":_vm.handleCancel}},[_c('a-spin',{attrs:{\"spinning\":_vm.loading}},[_c('a-form',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"16px\"},attrs:{\"form\":_vm.updateForm},on:{\"submit\":_vm.handleUpdate}},[_c('a-form-item',{staticStyle:{\"margin-left\":\"25%\",\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"状态\"}},[_c('a-switch',{attrs:{\"checked-children\":\"合格\",\"un-checked-children\":\"不合格\",\"disabled\":_vm.confirmLoading},model:{value:(_vm.localStatus),callback:function ($$v) {_vm.localStatus=$$v},expression:\"localStatus\"}})],1),_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"}},[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"sync\",\"loading\":_vm.confirmLoading},on:{\"click\":_vm.handleUpdate}},[_vm._v(\" 更新 \")])],1)],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-modal\r\n        title=\"更新电源板状态\"\r\n        :visible=\"visible\"\r\n        :confirm-loading=\"confirmLoading\"\r\n        @ok=\"handleOk\"\r\n        @cancel=\"handleCancel\"\r\n        :footer=\"null\"\r\n    >\r\n      <a-spin :spinning=\"loading\">\r\n        <a-form :form=\"updateForm\" @submit=\"handleUpdate\" style=\"display: flex;align-items: center;gap: 16px;\">\r\n          <a-form-item label=\"状态\" style=\"margin-left: 25%;display: flex;align-items: center;gap: 8px;\">\r\n            <a-switch\r\n                v-model=\"localStatus\"\r\n                checked-children=\"合格\"\r\n                un-checked-children=\"不合格\"\r\n                :disabled=\"confirmLoading\"\r\n            />\r\n          </a-form-item>\r\n          <a-form-item style=\"display: flex;align-items: center;gap: 8px;\">\r\n            <a-button\r\n                type=\"primary\"\r\n                html-type=\"submit\"\r\n                icon=\"sync\"\r\n                :loading=\"confirmLoading\"\r\n                @click=\"handleUpdate\"\r\n            >\r\n              更新\r\n            </a-button>\r\n          </a-form-item>\r\n        </a-form>\r\n      </a-spin>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {updateTest} from \"@/api/api\";\r\nimport {notification} from \"ant-design-vue\";\r\n\r\nexport default {\r\n  name: 'UpdatePowerBoardTest',\r\n  props: {\r\n    initialCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    initialStatus: {\r\n      type: Boolean,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      localBarcode:'',\r\n      localStatus: this.initialStatus,\r\n      updateForm: this.$form.createForm(this),\r\n      visible: false,\r\n      confirmLoading: false,\r\n      loading: false,\r\n      barcodeError: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.$parent.$on('initial-status-updated', (newStatus) => {\r\n      this.localStatus = newStatus; // 更新本地状态\r\n    });\r\n    this.$parent.$on('initial-barcode-updated', (newStatus) => {\r\n      this.localBarcode = newStatus; // 更新本地状态\r\n    });\r\n  },\r\n  methods: {\r\n    showModal() {\r\n      this.visible = true;\r\n    },\r\n    handleCancel() {\r\n      this.visible = false;\r\n      this.updateForm.resetFields();\r\n      this.barcodeError = '';\r\n    },\r\n    handleUpdate(e) {\r\n      e.preventDefault();\r\n      this.updateForm.validateFields(async (err, values) => {\r\n        console.log(values)\r\n        if (!err) {\r\n          this.confirmLoading = true;\r\n          try {\r\n            updateTest(this.localBarcode,this.localStatus).then(res=>{\r\n              console.log(res)\r\n              this.$emit('child-message', res.error_code);\r\n              if(res.error_code===0){\r\n                this.$message.success({\r\n                  content: '电源板状态修改成功',\r\n                  duration: 3,\r\n                });\r\n                this.handleCancel();\r\n              }else{\r\n                notification.error({\r\n                  message: '系统提示',\r\n                  description: res.message,\r\n                  duration: 4\r\n                });\r\n              }\r\n            })\r\n          } catch (error) {\r\n            notification.error({\r\n              message: '系统提示',\r\n              description: error.response.data.message,\r\n              duration: 4\r\n            });\r\n          } finally {\r\n            this.confirmLoading = false;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleOk() {\r\n      this.updateForm.submit();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ant-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdatePowerBoardTest.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdatePowerBoardTest.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UpdatePowerBoardTest.vue?vue&type=template&id=0226015e&scoped=true\"\nimport script from \"./UpdatePowerBoardTest.vue?vue&type=script&lang=js\"\nexport * from \"./UpdatePowerBoardTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./UpdatePowerBoardTest.vue?vue&type=style&index=0&id=0226015e&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0226015e\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"power-supply-test\">\r\n    <a-card :bordered=\"false\" class=\"query-card\">\r\n      <a-col :span=\"24\">\r\n          <a-form :form=\"form\" @submit=\"handleSubmit\">\r\n            <a-col :span=\"7\">\r\n              <a-form-item label=\"电源板条码\" style=\"display: flex;align-items: center;gap: 8px;\">\r\n                <a-input\r\n                    v-decorator=\"['startCode', { rules: [{ required: true, message: '请输入电源板条码' }, { validator: validateNumber }] }]\"\r\n                    placeholder=\"请输入电源板条码\"\r\n                    :maxLength=\"20\"\r\n                />\r\n              </a-form-item>\r\n            </a-col>\r\n            <a-col :span=\"6\">\r\n              <a-form-item label=\"连续的条码数量\" style=\"display: flex;align-items: center;gap: 8px;\">\r\n                <a-input-group compact>\r\n                  <a-input-number\r\n                      style=\"width: 70%\"\r\n                      :min=\"1\"\r\n                      :max=\"10000\"\r\n                      v-decorator=\"['quantity', { rules: [{ required: true, message: '请输入数量' }, { validator: validateCount }] }]\"\r\n                  />\r\n                </a-input-group>\r\n              </a-form-item>\r\n            </a-col>\r\n            <a-col :span=\"3\">\r\n              <a-form-item label=\"状态\" style=\"display: flex;align-items: center;gap: 8px;\">\r\n                <a-switch\r\n                    v-decorator=\"['status', { valuePropName: 'checked', initialValue: true }]\"\r\n                    checked-children=\"合格\"\r\n                    un-checked-children=\"不合格\"\r\n                />\r\n              </a-form-item>\r\n            </a-col>\r\n            <a-col :span=\"3\">\r\n              <a-form-item>\r\n                <a-button type=\"primary\" html-type=\"submit\" icon=\"save\" @click=\"batchSave\">添加</a-button>\r\n              </a-form-item>\r\n            </a-col>\r\n          </a-form>\r\n      </a-col>\r\n    </a-card>\r\n    <a-card :bordered=\"false\" class=\"query-card_\">\r\n      <a-form :form=\"queryForm\" layout=\"inline\" @submit=\"handleQuery\">\r\n        <a-row :gutter=\"16\" style=\"height: 80px;background-color: #f0f9eb;display: flex;align-items: center\">\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"条码\">\r\n              <a-input v-decorator=\"['queryCode', { rules: [{ validator: validateNumber }]}]\" placeholder=\"请输入条码\" :maxLength=\"20\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"4\">\r\n            <a-form-item label=\"状态\">\r\n              <a-select v-decorator=\"['queryStatus']\" style=\"width: 120px\" placeholder=\"请选择状态\">\r\n                <a-select-option value=\"\">全部</a-select-option>\r\n                <a-select-option value=\"true\">合格</a-select-option>\r\n                <a-select-option value=\"false\">不合格</a-select-option>\r\n              </a-select>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item>\r\n              <a-button type=\"primary\" html-type=\"submit\" icon=\"search\" @click=\"handleQuery\">查询</a-button>\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n      <a-table\r\n          style=\"margin-top: 20px\"\r\n          rowKey=\"key\"\r\n          :columns=\"columns\"\r\n          :dataSource=\"queryResults\"\r\n          :pagination=\"detailIpagination\"\r\n          @change='detailhandleTableChange'\r\n      >\r\n        <template slot=\"status\" slot-scope=\"text\">\r\n          <a-tag :color=\"text ? 'green' : 'red'\">\r\n            {{ text ? '合格' : '不合格' }}\r\n          </a-tag>\r\n        </template>\r\n        <template slot=\"action\" slot-scope=\"text, record\">\r\n          <span v-if=\"!record.code\" class=\"no-code\">无条码</span>\r\n          <a-button\r\n              class=\"update-button\"\r\n              type=\"primary\"\r\n              icon=\"edit\"\r\n              @click=\"updateData(record)\"\r\n          >\r\n            更新\r\n          </a-button>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n    <UpdatePowerBoardTest @child-message=\"handleChildMessage\" :initialCode=\"barcode\" :initialStatus=\"isStatus\" ref=\"UpdatePowerBoardTest\"></UpdatePowerBoardTest>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UpdatePowerBoardTest from '@/components/UpdatePowerBoardTest'\r\nimport {addTest, checkTest} from \"@/api/api\";\r\n\r\nexport default {\r\n  components: {\r\n    UpdatePowerBoardTest\r\n  },\r\n  data() {\r\n    return {\r\n      detailIpagination:{\r\n        current: 1,\r\n        pageSize: 10,\r\n        pageSizeOptions: ['10','20','50','100'],\r\n        showTotal: (total, range) => {\r\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\r\n        },\r\n        showQuickJumper: true,\r\n        showSizeChanger: true,\r\n        total: 0\r\n      },\r\n      barcode: '',\r\n      isStatus: false,\r\n      form: this.$form.createForm(this),\r\n      queryForm: this.$form.createForm(this),\r\n      queryResults: [],\r\n      columns: [\r\n        { title: '条码', dataIndex: 'code', key: 'code' },\r\n        { title: '状态', dataIndex: 'status', key: 'status', scopedSlots: { customRender: 'status' } },\r\n        { title: '操作', key: 'action', scopedSlots: { customRender: 'action' } },\r\n      ],\r\n      selectedRowKeys: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    handleChildMessage(message){\r\n      if(message===0){\r\n        this.fetchData()\r\n      }\r\n    },\r\n    fetchData(){\r\n      this.queryForm.validateFields((err, values) => {\r\n        if (!err) {\r\n          checkTest(values.queryCode, values.queryStatus, this.detailIpagination.pageSize,this.detailIpagination.current).then(res => {\r\n            console.log(res)\r\n            this.detailIpagination.total=res.page_count*this.detailIpagination.pageSize\r\n            this.queryResults = res.records.map((item, index) => ({\r\n              key: index,\r\n              code: item.barcode,\r\n              status: item.result,\r\n            }));\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 校验是否为数字的校验函数\r\n    validateNumber(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n      const decimalValue = Number(value);\r\n      console.log(decimalValue)\r\n      if (Number.isNaN(decimalValue)) {\r\n        callback('请输入数字');\r\n      } else if (value.toString().length < 4||value.toString().length > 20) {\r\n        callback('输入4 ~ 20位数字');\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateCount(rule, value, callback) {\r\n      if (value === undefined || value === null || value === '') {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const numValue = Number(value);\r\n      if (isNaN(numValue)) {\r\n        callback(new Error('请输入数字'));\r\n        return;\r\n      }\r\n\r\n      if (numValue % 1!== 0) {\r\n        callback(new Error('请输入整数，不能输入小数'));\r\n        return;\r\n      }\r\n\r\n      if (!/^\\d+$/.test(numValue.toString())) {\r\n        callback(new Error('请输入纯数字，不能包含其他字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    detailhandleTableChange(pagination) {\r\n      this.detailIpagination = pagination\r\n      this.queryForm.validateFields((err, values) => {\r\n        if (!err) {\r\n          checkTest(values.queryCode, values.queryStatus, this.detailIpagination.pageSize, this.detailIpagination.current).then(res => {\r\n            this.detailIpagination.total=res.page_count*this.detailIpagination.pageSize\r\n            console.log(res)\r\n            this.queryResults = res.records.map((item, index) => ({\r\n              key: index,\r\n              code: item.barcode,\r\n              status: item.result,\r\n            }));\r\n          });\r\n        }\r\n      });\r\n    },\r\n    async batchSave() {\r\n      this.form.validateFields((err, values) => {\r\n        if (!err) {\r\n          addTest(values.startCode, values.status, values.quantity).then(res => {\r\n            console.log(res)\r\n            this.queryForm.validateFields((err, values) => {\r\n              if (!err) {\r\n                checkTest(values.queryCode, values.queryStatus, this.detailIpagination.pageSize,this.detailIpagination.current).then(res => {\r\n                  console.log(res)\r\n                  if(res.error_code===0){\r\n                    this.detailIpagination.total=res.page_count*this.detailIpagination.pageSize\r\n                    this.queryResults = res.records.map((item, index) => ({\r\n                      key: index,\r\n                      code: item.barcode,\r\n                      status: item.result,\r\n                    }));\r\n                  }else {\r\n                    this.$message.error( res.message)\r\n                  }\r\n                });\r\n                this.form.resetFields()\r\n              }\r\n            });\r\n          })\r\n        }\r\n      });\r\n    },\r\n    handleSubmit(e) {\r\n      e.preventDefault();\r\n      this.form.validateFields((err, values) => {\r\n        if (!err) {\r\n          this.$message.success('批量录入成功');\r\n          console.log('批量录入表单提交:', values);\r\n          // 在这里调用后端 API 保存批量录入数据\r\n        }\r\n      });\r\n    },\r\n    handleQuery(e) {\r\n      e.preventDefault();\r\n      this.queryForm.validateFields((err, values) => {\r\n        if (!err) {\r\n          checkTest(values.queryCode, values.queryStatus, this.detailIpagination.pageSize,this.detailIpagination.current).then(res => {\r\n            console.log(res)\r\n            this.detailIpagination.total=res.page_count*this.detailIpagination.pageSize\r\n            this.queryResults = res.records.map((item, index) => ({\r\n              key: index,\r\n              code: item.barcode,\r\n              status: item.result,\r\n            }));\r\n          });\r\n        }\r\n      });\r\n    },\r\n    updateData(text){\r\n      try {\r\n        this.barcode=text.code\r\n        this.isStatus=text.status\r\n        this.$emit('initial-status-updated', this.isStatus); // 触发事件\r\n        this.$emit('initial-barcode-updated', this.barcode); // 触发事件\r\n        this.$refs.UpdatePowerBoardTest.title = \"更新写号信息\"\r\n        this.$refs.UpdatePowerBoardTest.visible = true;\r\n      } catch (error) {\r\n        console.error('Error fetching initial status:', error);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.power-supply-test {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.query-card {\r\n  margin-bottom: 16px;\r\n  background-color: #FFFFE0;\r\n}\r\n\r\n.query-card_ {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.table-operations {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.table-operations > * {\r\n  margin-right: 8px;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background-color: #fafafa;\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.ant-card-head) {\r\n  border-bottom: 1px solid #e8e8e8;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./PowerBoardTest.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./PowerBoardTest.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./PowerBoardTest.vue?vue&type=template&id=3967c1d9&scoped=true\"\nimport script from \"./PowerBoardTest.vue?vue&type=script&lang=js\"\nexport * from \"./PowerBoardTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./PowerBoardTest.vue?vue&type=style&index=0&id=3967c1d9&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3967c1d9\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"test-interface\"},[_c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-card',{staticClass:\"main-card\"},[_c('a-form-item',{staticClass:\"backend-address\",attrs:{\"label\":\"后端地址:\"}},[_c('div',{staticClass:\"backend-address-input\"},[_c('a-input',{attrs:{\"placeholder\":\"请输入后端地址\"},model:{value:(_vm.backendAddress),callback:function ($$v) {_vm.backendAddress=$$v},expression:\"backendAddress\"}}),_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleSyncAddress}},[_vm._v(\" 同步后端地址 \")])],1)]),_c('a-card',{staticClass:\"section-card\",attrs:{\"title\":\"板级测试\"}},[_c('div',{staticClass:\"ip-list\"},[_c('div',{staticClass:\"ip-input-group\"},[_c('a-input',{attrs:{\"placeholder\":\"请输入工装地址\"},model:{value:(_vm.board),callback:function ($$v) {_vm.board=$$v},expression:\"board\"}}),_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.addIP('board', _vm.board)}}},[_vm._v(\" 添加地址 \")])],1),_c('h4',{staticClass:\"ip-list-title\"},[_vm._v(\"工装地址列表\")]),_c('a-list',{staticClass:\"ip-list-items\",attrs:{\"bordered\":\"\",\"dataSource\":_vm.boardIPs},scopedSlots:_vm._u([{key:\"renderItem\",fn:function(item, index){return _c('a-list-item',{},[_c('a-input',{model:{value:(_vm.boardIPs[index]),callback:function ($$v) {_vm.$set(_vm.boardIPs, index, $$v)},expression:\"boardIPs[index]\"}}),_c('a-button',{attrs:{\"type\":\"link\",\"icon\":\"delete\"},on:{\"click\":function($event){return _vm.removeIP('board', index)}}},[_vm._v(\" 删除 \")])],1)}}])})],1),_c('div',{staticClass:\"test-items-section\"},[_c('h4',{staticClass:\"test-items-title\"},[_vm._v(\"测试项选择\")]),_c('a-checkbox',{attrs:{\"indeterminate\":_vm.indeterminateBoard,\"checked\":_vm.checkAllBoard},on:{\"change\":_vm.onCheckAllBoardChange}},[_vm._v(\" 全选 \")]),_c('a-divider',{staticClass:\"divider\"}),_c('a-checkbox-group',{on:{\"change\":_vm.onBoardChange},model:{value:(_vm.selectedBoard),callback:function ($$v) {_vm.selectedBoard=$$v},expression:\"selectedBoard\"}},[_c('a-row',{attrs:{\"gutter\":[16, 16]}},_vm._l((_vm.boardTestItems),function(item){return _c('a-col',{key:item.key,attrs:{\"span\":8}},[_c('a-checkbox',{attrs:{\"value\":item.key}},[_vm._v(\" \"+_vm._s(item.title)+\" \")])],1)}),1)],1)],1)]),_c('a-card',{staticClass:\"section-card\",attrs:{\"title\":\"整机测试\"}},[_c('div',{staticClass:\"ip-list\"},[_c('div',{staticClass:\"ip-input-group\"},[_c('a-input',{attrs:{\"placeholder\":\"请输入工装地址\"},model:{value:(_vm.system),callback:function ($$v) {_vm.system=$$v},expression:\"system\"}}),_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.addIP('system', _vm.system)}}},[_vm._v(\" 添加地址 \")])],1),_c('h4',{staticClass:\"ip-list-title\"},[_vm._v(\"工装地址列表\")]),_c('a-list',{staticClass:\"ip-list-items\",attrs:{\"bordered\":\"\",\"dataSource\":_vm.systemIPs},scopedSlots:_vm._u([{key:\"renderItem\",fn:function(item, index){return _c('a-list-item',{},[_c('a-input',{model:{value:(_vm.systemIPs[index]),callback:function ($$v) {_vm.$set(_vm.systemIPs, index, $$v)},expression:\"systemIPs[index]\"}}),_c('a-button',{attrs:{\"type\":\"link\",\"icon\":\"delete\"},on:{\"click\":function($event){return _vm.removeIP('system', index)}}},[_vm._v(\" 删除 \")])],1)}}])})],1),_c('div',{staticClass:\"test-items-section\"},[_c('h4',{staticClass:\"test-items-title\"},[_vm._v(\"测试项选择\")]),_c('a-checkbox',{attrs:{\"indeterminate\":_vm.indeterminateSystem,\"checked\":_vm.checkAllSystem},on:{\"change\":_vm.onCheckAllSystemChange}},[_vm._v(\" 全选 \")]),_c('a-divider',{staticClass:\"divider\"}),_c('a-checkbox-group',{on:{\"change\":_vm.onSystemChange},model:{value:(_vm.selectedSystem),callback:function ($$v) {_vm.selectedSystem=$$v},expression:\"selectedSystem\"}},[_c('a-row',{attrs:{\"gutter\":[16, 16]}},_vm._l((_vm.systemTestItems),function(item){return _c('a-col',{key:item.key,attrs:{\"span\":8}},[_c('a-checkbox',{attrs:{\"value\":item.key}},[_vm._v(\" \"+_vm._s(item.title)+\" \")])],1)}),1)],1)],1),_c('a-card',{staticClass:\"sub-section-card\",attrs:{\"title\":\"光伏模块检验参数\"}},[_c('a-form-item',{attrs:{\"label\":\"软件版本:\"}},[_c('a-input',{attrs:{\"placeholder\":\"请输入软件版本\"},model:{value:(_vm.softwareVersion),callback:function ($$v) {_vm.softwareVersion=$$v},expression:\"softwareVersion\"}})],1)],1)],1),_c('a-card',{staticClass:\"section-card\",attrs:{\"title\":\"整机复试\"}},[_c('div',{staticClass:\"ip-list\"},[_c('div',{staticClass:\"ip-input-group\"},[_c('a-input',{attrs:{\"placeholder\":\"请输入工装地址\"},model:{value:(_vm.measurement),callback:function ($$v) {_vm.measurement=$$v},expression:\"measurement\"}}),_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.addIP('measurement', _vm.measurement)}}},[_vm._v(\" 添加地址 \")])],1),_c('h4',{staticClass:\"ip-list-title\"},[_vm._v(\"工装地址列表\")]),_c('a-list',{staticClass:\"ip-list-items\",attrs:{\"bordered\":\"\",\"dataSource\":_vm.measurementIPs},scopedSlots:_vm._u([{key:\"renderItem\",fn:function(item, index){return _c('a-list-item',{},[_c('a-input',{model:{value:(_vm.measurementIPs[index]),callback:function ($$v) {_vm.$set(_vm.measurementIPs, index, $$v)},expression:\"measurementIPs[index]\"}}),_c('a-button',{attrs:{\"type\":\"link\",\"icon\":\"delete\"},on:{\"click\":function($event){return _vm.removeIP('measurement', index)}}},[_vm._v(\" 删除 \")])],1)}}])})],1),_c('div',{staticClass:\"test-items-section\"},[_c('h4',{staticClass:\"test-items-title\"},[_vm._v(\"测试项选择\")]),_c('a-checkbox',{attrs:{\"indeterminate\":_vm.indeterminateMeasurement,\"checked\":_vm.checkAllMeasurement},on:{\"change\":_vm.onCheckAllMeasurementChange}},[_vm._v(\" 全选 \")]),_c('a-divider',{staticClass:\"divider\"}),_c('a-checkbox-group',{on:{\"change\":_vm.onMeasurementChange},model:{value:(_vm.selectedMeasurement),callback:function ($$v) {_vm.selectedMeasurement=$$v},expression:\"selectedMeasurement\"}},[_c('a-row',{attrs:{\"gutter\":[16, 16]}},_vm._l((_vm.measurementTestItems),function(item){return _c('a-col',{key:item.key,attrs:{\"span\":8}},[_c('a-checkbox',{attrs:{\"value\":item.key}},[_vm._v(\" \"+_vm._s(item.title)+\" \")])],1)}),1)],1)],1)]),_c('div',{staticClass:\"footer\"},[_c('a-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleSync}},[_vm._v(\"同步\")])],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"test-interface\">\r\n    <a-form :form=\"form\" layout=\"vertical\">\r\n      <a-card class=\"main-card\">\r\n        <a-form-item label=\"后端地址:\" class=\"backend-address\">\r\n          <div class=\"backend-address-input\">\r\n            <a-input v-model=\"backendAddress\" placeholder=\"请输入后端地址\" />\r\n            <a-button type=\"primary\" @click=\"handleSyncAddress\">\r\n              同步后端地址\r\n            </a-button>\r\n          </div>\r\n        </a-form-item>\r\n\r\n        <!-- Board Level Test -->\r\n        <a-card title=\"板级测试\" class=\"section-card\">\r\n          <div class=\"ip-list\">\r\n            <div class=\"ip-input-group\">\r\n              <a-input\r\n                  v-model=\"board\"\r\n                  placeholder=\"请输入工装地址\"\r\n              />\r\n              <a-button type=\"primary\" @click=\"addIP('board', board)\">\r\n                添加地址\r\n              </a-button>\r\n            </div>\r\n            <h4 class=\"ip-list-title\">工装地址列表</h4>\r\n            <a-list\r\n                bordered\r\n                :dataSource=\"boardIPs\"\r\n                class=\"ip-list-items\"\r\n            >\r\n              <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\r\n                <a-input v-model=\"boardIPs[index]\" />\r\n                <a-button type=\"link\" @click=\"removeIP('board', index)\" icon=\"delete\">\r\n                  删除\r\n                </a-button>\r\n              </a-list-item>\r\n            </a-list>\r\n          </div>\r\n\r\n          <div class=\"test-items-section\">\r\n            <h4 class=\"test-items-title\">测试项选择</h4>\r\n            <a-checkbox\r\n                :indeterminate=\"indeterminateBoard\"\r\n                :checked=\"checkAllBoard\"\r\n                @change=\"onCheckAllBoardChange\"\r\n            >\r\n              全选\r\n            </a-checkbox>\r\n            <a-divider class=\"divider\" />\r\n            <a-checkbox-group v-model=\"selectedBoard\" @change=\"onBoardChange\">\r\n              <a-row :gutter=\"[16, 16]\">\r\n                <a-col :span=\"8\" v-for=\"item in boardTestItems\" :key=\"item.key\">\r\n                  <a-checkbox :value=\"item.key\">\r\n                    {{ item.title }}\r\n                  </a-checkbox>\r\n                </a-col>\r\n              </a-row>\r\n            </a-checkbox-group>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- System Test -->\r\n        <a-card title=\"整机测试\" class=\"section-card\">\r\n          <div class=\"ip-list\">\r\n            <div class=\"ip-input-group\">\r\n              <a-input\r\n                  v-model=\"system\"\r\n                  placeholder=\"请输入工装地址\"\r\n              />\r\n              <a-button type=\"primary\" @click=\"addIP('system', system)\">\r\n                添加地址\r\n              </a-button>\r\n            </div>\r\n            <h4 class=\"ip-list-title\">工装地址列表</h4>\r\n            <a-list\r\n                bordered\r\n                :dataSource=\"systemIPs\"\r\n                class=\"ip-list-items\"\r\n            >\r\n              <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\r\n                <a-input v-model=\"systemIPs[index]\" />\r\n                <a-button type=\"link\" @click=\"removeIP('system', index)\" icon=\"delete\">\r\n                  删除\r\n                </a-button>\r\n              </a-list-item>\r\n            </a-list>\r\n          </div>\r\n\r\n          <div class=\"test-items-section\">\r\n            <h4 class=\"test-items-title\">测试项选择</h4>\r\n            <a-checkbox\r\n                :indeterminate=\"indeterminateSystem\"\r\n                :checked=\"checkAllSystem\"\r\n                @change=\"onCheckAllSystemChange\"\r\n            >\r\n              全选\r\n            </a-checkbox>\r\n            <a-divider class=\"divider\" />\r\n            <a-checkbox-group v-model=\"selectedSystem\" @change=\"onSystemChange\">\r\n              <a-row :gutter=\"[16, 16]\">\r\n                <a-col :span=\"8\" v-for=\"item in systemTestItems\" :key=\"item.key\">\r\n                  <a-checkbox :value=\"item.key\">\r\n                    {{ item.title }}\r\n                  </a-checkbox>\r\n                </a-col>\r\n              </a-row>\r\n            </a-checkbox-group>\r\n          </div>\r\n\r\n          <a-card title=\"光伏模块检验参数\" class=\"sub-section-card\">\r\n            <a-form-item label=\"软件版本:\">\r\n              <a-input v-model=\"softwareVersion\" placeholder=\"请输入软件版本\" />\r\n            </a-form-item>\r\n          </a-card>\r\n        </a-card>\r\n\r\n        <!-- System Measurement -->\r\n        <a-card title=\"整机复试\" class=\"section-card\">\r\n          <div class=\"ip-list\">\r\n            <div class=\"ip-input-group\">\r\n              <a-input\r\n                  v-model=\"measurement\"\r\n                  placeholder=\"请输入工装地址\"\r\n              />\r\n              <a-button type=\"primary\" @click=\"addIP('measurement', measurement)\">\r\n                添加地址\r\n              </a-button>\r\n            </div>\r\n            <h4 class=\"ip-list-title\">工装地址列表</h4>\r\n            <a-list\r\n                bordered\r\n                :dataSource=\"measurementIPs\"\r\n                class=\"ip-list-items\"\r\n            >\r\n              <a-list-item slot=\"renderItem\" slot-scope=\"item, index\">\r\n                <a-input v-model=\"measurementIPs[index]\" />\r\n                <a-button type=\"link\" @click=\"removeIP('measurement', index)\" icon=\"delete\">\r\n                  删除\r\n                </a-button>\r\n              </a-list-item>\r\n            </a-list>\r\n          </div>\r\n\r\n          <div class=\"test-items-section\">\r\n            <h4 class=\"test-items-title\">测试项选择</h4>\r\n            <a-checkbox\r\n                :indeterminate=\"indeterminateMeasurement\"\r\n                :checked=\"checkAllMeasurement\"\r\n                @change=\"onCheckAllMeasurementChange\"\r\n            >\r\n              全选\r\n            </a-checkbox>\r\n            <a-divider class=\"divider\" />\r\n            <a-checkbox-group v-model=\"selectedMeasurement\" @change=\"onMeasurementChange\">\r\n              <a-row :gutter=\"[16, 16]\">\r\n                <a-col :span=\"8\" v-for=\"item in measurementTestItems\" :key=\"item.key\">\r\n                  <a-checkbox :value=\"item.key\">\r\n                    {{ item.title }}\r\n                  </a-checkbox>\r\n                </a-col>\r\n              </a-row>\r\n            </a-checkbox-group>\r\n          </div>\r\n        </a-card>\r\n\r\n        <!-- Sync Button -->\r\n        <div class=\"footer\">\r\n          <a-button type=\"primary\" @click=\"handleSync\">同步</a-button>\r\n        </div>\r\n      </a-card>\r\n    </a-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getInfo, getTestItems, readParameter, setParameter } from \"@/api/api\";\r\nimport { EventBus } from '/src/main'\r\nimport { message } from \"ant-design-vue\";\r\nimport service from \"@/utils/api\";\r\n\r\nexport default {\r\n  name: 'TestInterface',\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      backendAddress: '',\r\n      softwareVersion: '',\r\n      board: '',\r\n      system: '',\r\n      measurement: '',\r\n      boardIPs: [],\r\n      systemIPs: [],\r\n      measurementIPs: [],\r\n      boardTestItems: [],\r\n      systemTestItems: [],\r\n      measurementTestItems: [],\r\n      selectedBoard: [],\r\n      selectedSystem: [],\r\n      selectedMeasurement: [],\r\n      checkAllBoard: false,\r\n      indeterminateBoard: false,\r\n      checkAllSystem: false,\r\n      indeterminateSystem: false,\r\n      checkAllMeasurement: false,\r\n      indeterminateMeasurement: false,\r\n    }\r\n  },\r\n  created() {\r\n    this.loadSettings();\r\n  },\r\n  mounted() {\r\n    this.fetchTestItems();\r\n    this.fetchSoftwareVersion();\r\n  },\r\n  methods: {\r\n    handleSyncAddress() {\r\n      const baseurl = `http://${this.backendAddress}`;\r\n      localStorage.setItem('backendAddress', JSON.stringify(baseurl));\r\n      service.defaults.baseURL = baseurl;\r\n\r\n      setTimeout(() => {\r\n        this.loadSettings();\r\n        this.fetchTestItems();\r\n        this.fetchSoftwareVersion();\r\n        getInfo().then(res => {\r\n          const newInfo = {\r\n            name: res.name,\r\n            version: res.version,\r\n          }\r\n          EventBus.$emit('updateBackendInfo', newInfo)\r\n        })\r\n        message.success('已同步完成');\r\n      }, 2000);\r\n    },\r\n    loadSettings() {\r\n      const currentHost = window.location.host;\r\n      const [ip, port] = currentHost.split(':');\r\n      const defaultBackendAddress = `${ip || 'localhost'}:${port || '8080'}`;\r\n      const localAddress = JSON.parse(localStorage.getItem('backendAddress')) || '';\r\n\r\n      this.backendAddress = localAddress.replace('http://', '') || defaultBackendAddress;\r\n\r\n      this.boardIPs = JSON.parse(localStorage.getItem('boardIPs')) || [];\r\n      this.systemIPs = JSON.parse(localStorage.getItem('systemIPs')) || [];\r\n      this.measurementIPs = JSON.parse(localStorage.getItem('measurementIPs')) || [];\r\n\r\n      this.selectedBoard = JSON.parse(localStorage.getItem('selectedBoard')) || [];\r\n      this.selectedSystem = JSON.parse(localStorage.getItem('selectedSystem')) || [];\r\n      this.selectedMeasurement = JSON.parse(localStorage.getItem('selectedMeasurement')) || [];\r\n    },\r\n    fetchTestItems() {\r\n      getTestItems().then(res => {\r\n        this.boardTestItems = res.plans[0].items.map((item) => ({\r\n          key: item.item,\r\n          title: item.name,\r\n        }));\r\n        this.systemTestItems = res.plans[1].items.map((item) => ({\r\n          key: item.item,\r\n          title: item.name,\r\n        }));\r\n        this.measurementTestItems = res.plans[2].items.map((item) => ({\r\n          key: item.item,\r\n          title: item.name,\r\n        }));\r\n\r\n        if (this.selectedBoard.length === 0 && localStorage.getItem('remainingBoardItems') === null) {\r\n          this.selectedBoard = this.boardTestItems.map(item => item.key);\r\n        }\r\n        if (this.selectedSystem.length === 0 && localStorage.getItem('remainingSystemItems') === null) {\r\n          this.selectedSystem = this.systemTestItems.map(item => item.key);\r\n        }\r\n        if (this.selectedMeasurement.length === 0) {\r\n          this.selectedMeasurement = [];\r\n        }\r\n        this.updateCheckAllStatus();\r\n      }).catch((error) => {\r\n        console.log(error)\r\n      });\r\n    },\r\n    fetchSoftwareVersion() {\r\n      readParameter().then(res => {\r\n        if (res.error_code === 0) {\r\n          this.softwareVersion = res.soft_version;\r\n        }\r\n      })\r\n    },\r\n    addIP(section, ip) {\r\n      if (!ip) {\r\n        message.error(\"请输入内容后再添加地址\");\r\n      } else {\r\n        this[`${section}IPs`].push(ip);\r\n        this[section] = '';\r\n      }\r\n    },\r\n    removeIP(section, index) {\r\n      this[`${section}IPs`].splice(index, 1);\r\n    },\r\n    onCheckAllBoardChange(e) {\r\n      Object.assign(this, {\r\n        selectedBoard: e.target.checked ? this.boardTestItems.map(item => item.key) : [],\r\n        indeterminateBoard: false,\r\n        checkAllBoard: e.target.checked,\r\n      });\r\n    },\r\n    onBoardChange(checkedValues) {\r\n      const checkedCount = checkedValues.length;\r\n      this.checkAllBoard = checkedCount === this.boardTestItems.length;\r\n      this.indeterminateBoard = checkedCount > 0 && checkedCount < this.boardTestItems.length;\r\n    },\r\n    onCheckAllSystemChange(e) {\r\n      Object.assign(this, {\r\n        selectedSystem: e.target.checked ? this.systemTestItems.map(item => item.key) : [],\r\n        indeterminateSystem: false,\r\n        checkAllSystem: e.target.checked,\r\n      });\r\n    },\r\n    onSystemChange(checkedValues) {\r\n      const checkedCount = checkedValues.length;\r\n      this.checkAllSystem = checkedCount === this.systemTestItems.length;\r\n      this.indeterminateSystem = checkedCount > 0 && checkedCount < this.systemTestItems.length;\r\n    },\r\n    onCheckAllMeasurementChange(e) {\r\n      Object.assign(this, {\r\n        selectedMeasurement: e.target.checked ? this.measurementTestItems.map(item => item.key) : [],\r\n        indeterminateMeasurement: false,\r\n        checkAllMeasurement: e.target.checked,\r\n      });\r\n    },\r\n    onMeasurementChange(checkedValues) {\r\n      const checkedCount = checkedValues.length;\r\n      this.checkAllMeasurement = checkedCount === this.measurementTestItems.length;\r\n      this.indeterminateMeasurement = checkedCount > 0 && checkedCount < this.measurementTestItems.length;\r\n    },\r\n    updateCheckAllStatus() {\r\n      this.onBoardChange(this.selectedBoard);\r\n      this.onSystemChange(this.selectedSystem);\r\n      this.onMeasurementChange(this.selectedMeasurement);\r\n    },\r\n    handleSync() {\r\n      localStorage.setItem('selectedBoard', JSON.stringify(this.selectedBoard));\r\n      localStorage.setItem('selectedSystem', JSON.stringify(this.selectedSystem));\r\n      localStorage.setItem('selectedMeasurement', JSON.stringify(this.selectedMeasurement));\r\n\r\n      this.selectedBoardItems = this.selectedBoard.map((item) => ({\r\n        key: item,\r\n        title: this.boardTestItems.find(key => key.key === item).title,\r\n      }));\r\n      this.selectedSystemItems = this.selectedSystem.map((item) => ({\r\n        key: item,\r\n        title: this.systemTestItems.find(key => key.key === item).title,\r\n      }));\r\n      this.selectedMeasurementItems = this.selectedMeasurement.map((item) => ({\r\n        key: item,\r\n        title: this.measurementTestItems.find(key => key.key === item).title,\r\n      }));\r\n\r\n      const remainingBoardItems = this.boardTestItems.filter(item => {\r\n        return this.selectedBoard.indexOf(item.key) === -1;\r\n      });\r\n      const remainingSystemItems = this.systemTestItems.filter(item => {\r\n        return this.selectedSystem.indexOf(item.key) === -1;\r\n      });\r\n\r\n      localStorage.setItem('remainingBoardItems', JSON.stringify(remainingBoardItems));\r\n      localStorage.setItem('remainingSystemItems', JSON.stringify(remainingSystemItems));\r\n\r\n      localStorage.setItem('selectedBoardItems', JSON.stringify(this.selectedBoardItems));\r\n      localStorage.setItem('selectedSystemItems', JSON.stringify(this.selectedSystemItems));\r\n      localStorage.setItem('selectedMeasurementItems', JSON.stringify(this.selectedMeasurementItems));\r\n\r\n      localStorage.setItem('boardIPs', JSON.stringify(this.boardIPs));\r\n      localStorage.setItem('systemIPs', JSON.stringify(this.systemIPs));\r\n      localStorage.setItem('measurementIPs', JSON.stringify(this.measurementIPs));\r\n\r\n      setTimeout(() => {\r\n        this.loadSettings();\r\n        this.fetchTestItems();\r\n        this.fetchSoftwareVersion();\r\n        setParameter(this.systemIPs,this.softwareVersion).then(res => {\r\n          console.log(res)\r\n          // if (res[0].data.error_code === 0) {\r\n          //   message.success('已同步完成');\r\n          // }\r\n        });\r\n        message.success('已同步完成');\r\n      }, 2000);\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.test-interface {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.main-card {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #1890ff;\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.backend-address {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.backend-address-input {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.backend-address-input .ant-input {\r\n  flex: 1;\r\n}\r\n\r\n.backend-address-input .ant-btn {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.section-card {\r\n  margin-bottom: 24px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.sub-section-card {\r\n  background: #fafafa;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 8px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.ip-list {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.ip-input-group {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.ip-list-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin: 16px 0;\r\n  color: #1890ff;\r\n}\r\n\r\n.ip-list-items {\r\n  background: #fff;\r\n  border-radius: 4px;\r\n}\r\n\r\n.test-items-section {\r\n  margin-top: 24px;\r\n  background: #fafafa;\r\n  border: 1px solid #f0f0f0;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n}\r\n\r\n.test-items-title {\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n  margin-bottom: 16px;\r\n  color: #1890ff;\r\n}\r\n\r\n.divider {\r\n  margin: 16px 0;\r\n}\r\n\r\n.footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  margin-top: 24px;\r\n}\r\n\r\n:deep(.ant-card-head-title) {\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #1890ff;\r\n}\r\n\r\n:deep(.ant-form-item-label > label) {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n:deep(.ant-checkbox-wrapper) {\r\n  margin-left: 0;\r\n  margin-right: 8px;\r\n}\r\n\r\n:deep(.ant-list-item) {\r\n  padding: 12px 16px;\r\n}\r\n\r\n:deep(.ant-btn-primary) {\r\n  height: 40px;\r\n  font-size: 16px;\r\n  padding: 0 24px;\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n:deep(.ant-btn-primary:hover) {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n:deep(.ant-input) {\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.ant-checkbox-group) {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SystemSetting.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./SystemSetting.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./SystemSetting.vue?vue&type=template&id=90feee94&scoped=true\"\nimport script from \"./SystemSetting.vue?vue&type=script&lang=js\"\nexport * from \"./SystemSetting.vue?vue&type=script&lang=js\"\nimport style0 from \"./SystemSetting.vue?vue&type=style&index=0&id=90feee94&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"90feee94\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"testing-interface\"},[_c('a-card',{attrs:{\"bordered\":false}},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.testData,\"pagination\":false,\"rowKey\":record => record.key,\"scroll\":{ x: 800 }},scopedSlots:_vm._u([{key:\"barcode\",fn:function(text, record){return [_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],class:_vm.getBarcodeInputClass(record),attrs:{\"maxLength\":22,\"placeholder\":\"请输入条码\",\"disabled\":record.isTesting},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleBarcodeSubmit(record)}},model:{value:(record.barcode),callback:function ($$v) {_vm.$set(record, \"barcode\", $$v)},expression:\"record.barcode\"}})]}},{key:\"device\",fn:function(text, record){return [_c('div',{staticClass:\"device-info\"},[_c('div',{staticClass:\"device-id\"},[_vm._v(_vm._s(record.device))]),_c('div',{staticClass:\"device-version\"},[_c('span',{staticClass:\"position\"},[_vm._v(_vm._s(record.name)+\"/\")]),_c('span',{staticClass:\"version\"},[_vm._v(\"v\"+_vm._s(record.version))])])])]}},{key:\"time\",fn:function(text, record){return [_c('span',{class:{\n          'time-testing': record.isTesting,\n          'time-success': record.testStatus === 'success',\n          'time-error': record.testStatus === 'fail'\n        }},[_vm._v(_vm._s(record.time))])]}},_vm._l((_vm.testItems),function(item){return {key:item.key,fn:function(text, record){return [_c('a-tag',{key:item.key,staticStyle:{\"width\":\"50px\"},attrs:{\"color\":_vm.getStatusColor(record[item.key])}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record[item.key]))+\" \")])]}}})],null,true)})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"testing-interface\">\r\n    <a-card :bordered=\"false\">\r\n      <a-table\r\n          :columns=\"columns\"\r\n          :data-source=\"testData\"\r\n          :pagination=\"false\"\r\n          :rowKey=\"record => record.key\"\r\n          :scroll=\"{ x: 800 }\"\r\n      >\r\n        <!-- 条码输入框插槽 -->\r\n        <template slot=\"barcode\" slot-scope=\"text, record\">\r\n          <a-input\r\n              v-enter-next-input\r\n              :maxLength=\"22\"\r\n              v-model=\"record.barcode\"\r\n              placeholder=\"请输入条码\"\r\n              :disabled=\"record.isTesting\"\r\n              @keyup.enter=\"handleBarcodeSubmit(record)\"\r\n              :class=\"getBarcodeInputClass(record)\"\r\n          />\r\n        </template>\r\n\r\n        <!-- 工装信息显示插槽 -->\r\n        <template slot=\"device\" slot-scope=\"text, record\">\r\n          <div class=\"device-info\">\r\n            <div class=\"device-id\">{{ record.device }}</div>\r\n            <div class=\"device-version\">\r\n              <span class=\"position\">{{ record.name }}/</span>\r\n              <span class=\"version\">v{{ record.version }}</span>\r\n            </div>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 时间字段插槽 -->\r\n        <template slot=\"time\" slot-scope=\"text, record\">\r\n          <span :class=\"{\r\n            'time-testing': record.isTesting,\r\n            'time-success': record.testStatus === 'success',\r\n            'time-error': record.testStatus === 'fail'\r\n          }\">{{ record.time }}</span>\r\n        </template>\r\n\r\n        <!-- 动态测试状态插槽 -->\r\n        <template v-for=\"item in testItems\" :slot=\"item.key\" slot-scope=\"text, record\">\r\n          <a-tag :key=\"item.key\" :color=\"getStatusColor(record[item.key])\" style=\"width: 50px\">\r\n            {{ getStatusText(record[item.key]) }}\r\n          </a-tag>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {message} from \"ant-design-vue\";\r\n\r\nexport default {\r\n  name: 'TestingInterface',\r\n  data() {\r\n    return {\r\n      testItems: [],\r\n      columns: [\r\n        {\r\n          title: '工装',\r\n          dataIndex: 'device',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'device' }\r\n        },\r\n        {\r\n          title: '时间',\r\n          dataIndex: 'time',\r\n          width: 80,\r\n          scopedSlots: { customRender: 'time' }\r\n        },\r\n        {\r\n          title: '条码',\r\n          dataIndex: 'barcode',\r\n          width: 180,\r\n          scopedSlots: { customRender: 'barcode' }\r\n        }\r\n      ],\r\n      testData: [],\r\n      websockets: [],\r\n      timers: {},\r\n      workbenchAddresses: [],\r\n      socketMap: [],\r\n      submissionStatus: {},\r\n    }\r\n  },\r\n  created() {\r\n    //board\r\n    const storedBackendUrl = localStorage.getItem('selectedMeasurementItems');\r\n    if (storedBackendUrl) {\r\n      this.testItems = JSON.parse(storedBackendUrl).map(item => ({\r\n        key: item.key,\r\n        title: item.title,\r\n      }));\r\n\r\n      // 动态添加测试项列\r\n      this.testItems.forEach(item => {\r\n        this.columns.push({\r\n          title: item.title,\r\n          dataIndex: item.key,\r\n          width: 150,\r\n          scopedSlots: { customRender: item.key }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // 验证条码\r\n    validateBarcode(barcode) {\r\n      const regex = /^\\d{22}$/\r\n      return regex.test(barcode)\r\n    },\r\n\r\n    updateSubmissionStatus(key, status) {\r\n      this.$set(this.submissionStatus, key, status)\r\n      // Reset the status after 2 seconds\r\n      setTimeout(() => {\r\n        this.$set(this.submissionStatus, key, null)\r\n      }, 2000)\r\n    },\r\n\r\n    // New method to get the appropriate class for the barcode input\r\n    getBarcodeInputClass(record) {\r\n      return {\r\n        'input-success': this.submissionStatus[record.key] === 'success',\r\n        'input-error': this.submissionStatus[record.key] === 'error'\r\n      }\r\n    },\r\n\r\n\r\n    handleBarcodeSubmit(record) {\r\n      if (window.isDirectiveEnter) {\r\n        window.isDirectiveEnter = false; // 重置标志位，方便下次判断\r\n        return;\r\n      }\r\n      if (!this.validateBarcode(record.barcode)) {\r\n        message.error('条码必须为22位纯数字')\r\n        this.updateSubmissionStatus(record.key, 'error')\r\n        return\r\n      }\r\n\r\n      if (record.isTesting) {\r\n        message.warning('测试进行中，请等待测试完成')\r\n        return\r\n      }\r\n\r\n      this.websockets.forEach((socket, index) => {\r\n        const indexFind = this.socketMap.findIndex(socketObj => socketObj === record.device);\r\n        if (socket.readyState === WebSocket.OPEN && index === indexFind) {\r\n          socket.send(JSON.stringify({\r\n            type: 'boardCode',\r\n            planType: 'rewhole',\r\n            device: record.name,\r\n            barcode: record.barcode\r\n          }))\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          // record.testStatus = null;\r\n          // record.isTesting = false; // 添加这行代码来重置测试中状态\r\n          // record.time = 0;\r\n        }\r\n      })\r\n    },\r\n    // 处理条码提交\r\n    // handleBarcodeSubmit(record) {\r\n    //   if (!this.validateBarcode(record.barcode)) {\r\n    //     message.error('条码必须为22位纯数字')\r\n    //     return\r\n    //   }\r\n    //\r\n    //   if (record.isTesting) {\r\n    //     message.warning('测试进行中，请等待测试完成')\r\n    //     return\r\n    //   }\r\n    //\r\n    //   this.websockets.forEach((socket,index) => {\r\n    //     const indexFind = this.socketMap.findIndex(socketObj => socketObj === record.device);\r\n    //     if (socket.readyState === WebSocket.OPEN&&index===indexFind) {\r\n    //       socket.send(JSON.stringify({\r\n    //         type: 'boardCode',\r\n    //         planType: 'rewhole',\r\n    //         device: record.name,\r\n    //         barcode: record.barcode\r\n    //       }))\r\n    //     }\r\n    //   })\r\n    // },\r\n    // 获取状态对应的颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        success: '#00FF99',\r\n        fail: '#FF3333',\r\n        testing: '#66CCFF',\r\n        waiting: '#999999'\r\n      };\r\n      return colorMap[status] || colorMap.waiting\r\n    },\r\n\r\n    // 获取状态对应的文本\r\n    getStatusText(status) {\r\n      const textMap = {\r\n        success: '成功',\r\n        fail: '失败',\r\n        testing: '测试中',\r\n        waiting: '未测试'\r\n      }\r\n      return textMap[status] || textMap.waiting\r\n    },\r\n\r\n    initWebSocket() {\r\n      const storedBackendUrl = localStorage.getItem('measurementIPs');\r\n      this.workbenchAddresses = JSON.parse(storedBackendUrl).map(ip => `ws://${ip}/echo`);\r\n      this.workbenchAddresses.forEach((address) => {\r\n        let socket = new WebSocket(address);\r\n        let currentTimeout = 2000; // 初始超时时间设为2秒\r\n        let timeoutId;\r\n        const increaseTimeoutFactor = 1.5; // 超时时间增长系数，每次增加50%，可调整\r\n        let hasSentMessage = false; // 新增标识变量，用于记录是否已发送过消息\r\n\r\n        const checkConnection = () => {\r\n          if (socket.readyState!== WebSocket.OPEN) {\r\n            currentTimeout *= increaseTimeoutFactor; // 增加下次超时时间\r\n            if (currentTimeout > 10000) { // 最大超时时间限制为10秒，可调整\r\n              console.error(`WebSocket连接超时（地址：${address}）`);\r\n              message.error(`与工装（地址：${address}）的连接超时，请检查网络`);\r\n              socket.close();\r\n              return;\r\n            }\r\n            console.warn(`WebSocket连接未建立，正在延长超时等待时间至${currentTimeout}毫秒（地址：${address}）`);\r\n            message.warning(`与工装（地址：${address}）的连接较慢，正在尝试继续等待...`);\r\n            timeoutId = setTimeout(checkConnection, currentTimeout);\r\n          } else {\r\n            if (!hasSentMessage) { // 只有在未发送过消息时才发送\r\n              socket.send(JSON.stringify({\r\n                type: 'devicePlan',\r\n                planType: 'rewhole',\r\n                items: this.testItems.map(item => item.key)\r\n              }));\r\n              hasSentMessage = true; // 标记已发送过消息\r\n            }\r\n            clearTimeout(timeoutId);\r\n          }\r\n        };\r\n        socket.onopen = () => {\r\n          console.log(`WebSocket连接已建立，地址：${address}`);\r\n          message.success(`WebSocket连接已建立，地址：${address}`);\r\n          if (!hasSentMessage) { // 同样判断，只有未发送过消息时才发送\r\n            socket.send(JSON.stringify({\r\n              type: 'devicePlan',\r\n              planType: 'rewhole',\r\n              items: this.testItems.map(item => item.key)\r\n            }));\r\n            hasSentMessage = true; // 标记已发送过消息\r\n          }\r\n        };\r\n        socket.onerror = (error) => {\r\n          console.error(`WebSocket错误（地址：${address}）:`, error);\r\n          message.error(`与工装（地址：${address}）的连接出现错误，请检查网络`);\r\n        };\r\n        socket.onclose = () => {\r\n          console.log(`WebSocket连接已断开，地址：${address}`);\r\n          message.warning(`WebSocket连接已断开，地址：${address}`);\r\n        };\r\n        socket.onmessage = this.handleWebSocketMessage;\r\n\r\n        timeoutId = setTimeout(checkConnection, currentTimeout);\r\n        this.websockets.push(socket);\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(event) {\r\n      const data = JSON.parse(event.data)\r\n      console.log(data)\r\n\r\n      if (data.type === 'startTest') {\r\n        this.updateTestStatus(data)\r\n        return\r\n      }\r\n\r\n      switch (data.type) {\r\n        case 'devicePlan':\r\n          if(data.error_code === 0){\r\n            message.success('测试方案已配置成功')\r\n          } else {\r\n            message.warning(`${data.message}`)\r\n          }\r\n          break\r\n        case 'deviceInfo':\r\n          this.updateTestDataInfo(data)\r\n          break\r\n        case 'finishTest':\r\n        case 'failTest':\r\n          this.updateTestStatus(data)\r\n          break\r\n        case 'startItem':\r\n        case 'finishItem':\r\n        case 'failItem':\r\n          this.updateTestItemStatus(data)\r\n          break\r\n        case 'boardCode':\r\n          this.handleBarcodeResponse(data)\r\n          break\r\n        default:\r\n          console.warn('未知的消息类型:', data.type)\r\n      }\r\n    },\r\n\r\n    handleBarcodeResponse(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record) {\r\n        if (data.error_code === 0) {\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          record.testStatus = null\r\n          record.isTesting = false\r\n          record.time = 0\r\n        } else {\r\n          this.updateSubmissionStatus(record.key, 'error')\r\n          message.error(`条码提交失败: ${data.message}`)\r\n        }\r\n        this.$forceUpdate()\r\n      }\r\n    },\r\n    updateTestDataInfo(data) {\r\n      const newDataArr = data.devices.map((item, index) => {\r\n        const dataObj = {\r\n          key: `${data.name}-${item}-${index}`,\r\n          name: item,\r\n          device: data.name,\r\n          time: 0,\r\n          barcode: '',\r\n          version : data.version,\r\n        };\r\n        this.testItems.forEach(testItem => {\r\n          dataObj[testItem.key] = null; // 此处可根据实际业务场景灵活设置初始值，比如从其他地方获取合适的值来替换null\r\n        });\r\n        return dataObj;\r\n      });\r\n      this.testData = this.testData.concat(newDataArr);\r\n      this.socketMap.push(data.name)\r\n    },\r\n    // 更新测试状态\r\n    updateTestStatus(data) {\r\n      const record = this.testData.find(\r\n          item => item.name === data.devices &&\r\n              item.barcode === data.barcode\r\n      );\r\n\r\n      if (record) {\r\n        if (data.type === 'startTest') {\r\n          record.isTesting = true;\r\n          record.testStatus = 'testing';\r\n          this.startTimer(data);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          record.isTesting = false;\r\n          record.testStatus = data.type === 'finishTest'? 'success' : 'fail';\r\n          this.$set(record, 'testStatus', record.testStatus);\r\n          this.stopTimer(data);\r\n\r\n          setTimeout(() => {\r\n            if (data.type === 'finishTest') {\r\n              record.barcode = '';\r\n            }\r\n            this.$set(record, 'barcode', record.barcode);\r\n          }, 2000);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        }\r\n      }\r\n    },\r\n    updateTestItemStatus(data) {\r\n      const targetData  = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (targetData) {\r\n        if (data.type === 'startItem') {\r\n          targetData[data.item]='testing'\r\n          this.testData[targetData.key]=targetData\r\n        } else if (data.type === 'finishItem') {\r\n          targetData[data.item]='success'\r\n          this.testData[targetData.key]=targetData\r\n        } else {\r\n          targetData[data.item]='fail'\r\n          this.testData[targetData.key]=targetData\r\n        }\r\n      }\r\n    },\r\n    startTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && !this.timers[record.key]) {\r\n        let seconds = 0\r\n        this.timers[record.key] = setInterval(() => {\r\n          seconds++\r\n          record.time = this.formatTime(seconds)\r\n          this.testData[record.key].time=record.time\r\n        }, 1000)\r\n      }\r\n    },\r\n\r\n    stopTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && this.timers[record.key]) {\r\n        clearInterval(this.timers[record.key])\r\n        delete this.timers[record.key]\r\n      }\r\n    },\r\n\r\n    formatTime(seconds) {\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = seconds % 60\r\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.initWebSocket();\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    Object.keys(this.timers).forEach(key => {\r\n      clearInterval(this.timers[key]);\r\n    });\r\n    this.websockets.forEach((socket) => {\r\n      if (socket) {\r\n        socket.close();\r\n      }\r\n    });\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.testing-interface {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.device-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.device-id {\r\n  font-weight: 500;\r\n}\r\n\r\n.device-version {\r\n  display: flex;\r\n  color: #666;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #1890ff;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #52c41a;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-error {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.input-success {\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 12px 8px;\r\n}\r\n\r\n:deep(.ant-input) {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.ant-tag) {\r\n  margin: 0;\r\n  padding: 4px 8px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.input-success {\r\n  background-color: #f6ffed;\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  background-color: #fff1f0;\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n.time-default {\r\n  background-color: white;\r\n  color: black;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.time-error {\r\n  background-color: #fff1f0;\r\n  color: #ff4d4f;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WholeMachineReTest.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WholeMachineReTest.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./WholeMachineReTest.vue?vue&type=template&id=19f0c960&scoped=true\"\nimport script from \"./WholeMachineReTest.vue?vue&type=script&lang=js\"\nexport * from \"./WholeMachineReTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./WholeMachineReTest.vue?vue&type=style&index=0&id=19f0c960&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"19f0c960\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"testing-interface\"},[_c('a-card',{attrs:{\"bordered\":false}},[_c('a-table',{attrs:{\"columns\":_vm.columns,\"data-source\":_vm.testData,\"pagination\":false,\"rowKey\":record => record.key,\"scroll\":{ x: 800 }},scopedSlots:_vm._u([{key:\"barcode\",fn:function(text, record){return [_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],class:_vm.getBarcodeInputClass(record),attrs:{\"maxLength\":22,\"placeholder\":\"请输入条码\",\"disabled\":record.isTesting},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleBarcodeSubmit(record)}},model:{value:(record.barcode),callback:function ($$v) {_vm.$set(record, \"barcode\", $$v)},expression:\"record.barcode\"}})]}},{key:\"device\",fn:function(text, record){return [_c('div',{staticClass:\"device-info\"},[_c('div',{staticClass:\"device-id\"},[_vm._v(_vm._s(record.device))]),_c('div',{staticClass:\"device-version\"},[_c('span',{staticClass:\"position\"},[_vm._v(_vm._s(record.name)+\"/\")]),_c('span',{staticClass:\"version\"},[_vm._v(\"v\"+_vm._s(record.version))])])])]}},{key:\"time\",fn:function(text, record){return [_c('span',{class:{\n          'time-testing': record.isTesting,\n          'time-success': record.testStatus === 'success',\n          'time-error': record.testStatus === 'fail'\n        }},[_vm._v(_vm._s(record.time))])]}},_vm._l((_vm.testItems),function(item){return {key:item.key,fn:function(text, record){return [_c('a-tag',{key:item.key,staticStyle:{\"width\":\"50px\"},attrs:{\"color\":_vm.getStatusColor(record[item.key])}},[_vm._v(\" \"+_vm._s(_vm.getStatusText(record[item.key]))+\" \")])]}}})],null,true)})],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"testing-interface\">\r\n    <a-card :bordered=\"false\">\r\n      <a-table\r\n          :columns=\"columns\"\r\n          :data-source=\"testData\"\r\n          :pagination=\"false\"\r\n          :rowKey=\"record => record.key\"\r\n          :scroll=\"{ x: 800 }\"\r\n      >\r\n        <!-- 条码输入框插槽 -->\r\n        <template slot=\"barcode\" slot-scope=\"text, record\">\r\n          <a-input\r\n              v-enter-next-input\r\n              :maxLength=\"22\"\r\n              v-model=\"record.barcode\"\r\n              placeholder=\"请输入条码\"\r\n              :disabled=\"record.isTesting\"\r\n              @keyup.enter=\"handleBarcodeSubmit(record)\"\r\n              :class=\"getBarcodeInputClass(record)\"\r\n          />\r\n        </template>\r\n\r\n        <!-- 工装信息显示插槽 -->\r\n        <template slot=\"device\" slot-scope=\"text, record\">\r\n          <div class=\"device-info\">\r\n            <div class=\"device-id\">{{ record.device }}</div>\r\n            <div class=\"device-version\">\r\n              <span class=\"position\">{{ record.name }}/</span>\r\n              <span class=\"version\">v{{ record.version }}</span>\r\n            </div>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 时间字段插槽 -->\r\n        <template slot=\"time\" slot-scope=\"text, record\">\r\n          <span :class=\"{\r\n            'time-testing': record.isTesting,\r\n            'time-success': record.testStatus === 'success',\r\n            'time-error': record.testStatus === 'fail'\r\n          }\">{{ record.time }}</span>\r\n        </template>\r\n\r\n        <!-- 动态测试状态插槽 -->\r\n        <template v-for=\"item in testItems\" :slot=\"item.key\" slot-scope=\"text, record\">\r\n          <a-tag :key=\"item.key\" :color=\"getStatusColor(record[item.key])\" style=\"width: 50px\">\r\n            {{ getStatusText(record[item.key]) }}\r\n          </a-tag>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {message} from \"ant-design-vue\";\r\n\r\nexport default {\r\n  name: 'TestingInterface',\r\n  data() {\r\n    return {\r\n      testItems: [],\r\n      columns: [\r\n        {\r\n          title: '工装',\r\n          dataIndex: 'device',\r\n          width: 150,\r\n          scopedSlots: { customRender: 'device' }\r\n        },\r\n        {\r\n          title: '时间',\r\n          dataIndex: 'time',\r\n          width: 80,\r\n          scopedSlots: { customRender: 'time' }\r\n        },\r\n        {\r\n          title: '条码',\r\n          dataIndex: 'barcode',\r\n          width: 180,\r\n          scopedSlots: { customRender: 'barcode' }\r\n        }\r\n      ],\r\n      testData: [],\r\n      websockets: [],\r\n      timers: {},\r\n      workbenchAddresses: [],\r\n      socketMap: [],\r\n      submissionStatus: {},\r\n    }\r\n  },\r\n  created() {\r\n    //board\r\n    const storedBackendUrl = localStorage.getItem('selectedSystemItems');\r\n    if (storedBackendUrl) {\r\n      this.testItems = JSON.parse(storedBackendUrl).map(item => ({\r\n        key: item.key,\r\n        title: item.title,\r\n      }));\r\n\r\n      // 动态添加测试项列\r\n      this.testItems.forEach(item => {\r\n        this.columns.push({\r\n          title: item.title,\r\n          dataIndex: item.key,\r\n          width: 150,\r\n          scopedSlots: { customRender: item.key }\r\n        })\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // 验证条码\r\n    validateBarcode(barcode) {\r\n      const regex = /^\\d{22}$/\r\n      return regex.test(barcode)\r\n    },\r\n    updateSubmissionStatus(key, status) {\r\n      this.$set(this.submissionStatus, key, status)\r\n      setTimeout(() => {\r\n        this.$set(this.submissionStatus, key, null)\r\n      }, 2000)\r\n    },\r\n\r\n    getBarcodeInputClass(record) {\r\n      return {\r\n        'input-success': this.submissionStatus[record.key] === 'success',\r\n        'input-error': this.submissionStatus[record.key] === 'error'\r\n      }\r\n    },\r\n\r\n    handleBarcodeSubmit(record) {\r\n      if (window.isDirectiveEnter) {\r\n        window.isDirectiveEnter = false; // 重置标志位，方便下次判断\r\n        return;\r\n      }\r\n      if (!this.validateBarcode(record.barcode)) {\r\n        message.error('条码必须为22位纯数字')\r\n        this.updateSubmissionStatus(record.key, 'error')\r\n        return\r\n      }\r\n\r\n      if (record.isTesting) {\r\n        message.warning('测试进行中，请等待测试完成')\r\n        return\r\n      }\r\n\r\n      this.websockets.forEach((socket, index) => {\r\n        const indexFind = this.socketMap.findIndex(socketObj => socketObj === record.device);\r\n        if (socket.readyState === WebSocket.OPEN && index === indexFind) {\r\n          socket.send(JSON.stringify({\r\n            type: 'boardCode',\r\n            planType: 'whole',\r\n            device: record.name,\r\n            barcode: record.barcode\r\n          }))\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          // record.testStatus = null;\r\n          // record.isTesting = false; // 添加这行代码来重置测试中状态\r\n          // record.time = 0;\r\n        }\r\n      })\r\n    },\r\n\r\n    // // 处理条码提交\r\n    // handleBarcodeSubmit(record) {\r\n    //   if (!this.validateBarcode(record.barcode)) {\r\n    //     message.error('条码必须为22位纯数字')\r\n    //     return\r\n    //   }\r\n    //\r\n    //   if (record.isTesting) {\r\n    //     message.warning('测试进行中，请等待测试完成')\r\n    //     return\r\n    //   }\r\n    //\r\n    //   this.websockets.forEach((socket,index) => {\r\n    //     const indexFind = this.socketMap.findIndex(socketObj => socketObj === record.device);\r\n    //     if (socket.readyState === WebSocket.OPEN&&index===indexFind) {\r\n    //       socket.send(JSON.stringify({\r\n    //         type: 'boardCode',\r\n    //         planType: 'whole',\r\n    //         device: record.name,\r\n    //         barcode: record.barcode\r\n    //       }))\r\n    //     }\r\n    //   })\r\n    // },\r\n    // 获取状态对应的颜色\r\n    getStatusColor(status) {\r\n      const colorMap = {\r\n        success: '#00FF99',\r\n        fail: '#FF3333',\r\n        testing: '#66CCFF',\r\n        waiting: '#999999'\r\n      };\r\n      return colorMap[status] || colorMap.waiting\r\n    },\r\n\r\n    // 获取状态对应的文本\r\n    getStatusText(status) {\r\n      const textMap = {\r\n        success: '成功',\r\n        fail: '失败',\r\n        testing: '测试中',\r\n        waiting: '未测试'\r\n      }\r\n      return textMap[status] || textMap.waiting\r\n    },\r\n\r\n    initWebSocket() {\r\n      const storedBackendUrl = localStorage.getItem('systemIPs');\r\n      this.workbenchAddresses = JSON.parse(storedBackendUrl).map(ip => `ws://${ip}/echo`);\r\n      this.workbenchAddresses.forEach((address) => {\r\n        let socket = new WebSocket(address);\r\n        let currentTimeout = 2000; // 初始超时时间设为2秒\r\n        let timeoutId;\r\n        const increaseTimeoutFactor = 1.5; // 超时时间增长系数，每次增加50%，可调整\r\n        let hasSentMessage = false; // 新增标识变量，用于记录是否已发送过消息\r\n\r\n        const checkConnection = () => {\r\n          if (socket.readyState!== WebSocket.OPEN) {\r\n            currentTimeout *= increaseTimeoutFactor; // 增加下次超时时间\r\n            if (currentTimeout > 10000) { // 最大超时时间限制为10秒，可调整\r\n              console.error(`WebSocket连接超时（地址：${address}）`);\r\n              message.error(`与工装（地址：${address}）的连接超时，请检查网络`);\r\n              socket.close();\r\n              return;\r\n            }\r\n            console.warn(`WebSocket连接未建立，正在延长超时等待时间至${currentTimeout}毫秒（地址：${address}）`);\r\n            message.warning(`与工装（地址：${address}）的连接较慢，正在尝试继续等待...`);\r\n            timeoutId = setTimeout(checkConnection, currentTimeout);\r\n          } else {\r\n            if (!hasSentMessage) { // 只有在未发送过消息时才发送\r\n              socket.send(JSON.stringify({\r\n                type: 'devicePlan',\r\n                planType: 'whole',\r\n                items: this.testItems.map(item => item.key)\r\n              }));\r\n              hasSentMessage = true; // 标记已发送过消息\r\n            }\r\n            clearTimeout(timeoutId);\r\n          }\r\n        };\r\n        socket.onopen = () => {\r\n          console.log(`WebSocket连接已建立，地址：${address}`);\r\n          message.success(`WebSocket连接已建立，地址：${address}`);\r\n          if (!hasSentMessage) { // 同样判断，只有未发送过消息时才发送\r\n            socket.send(JSON.stringify({\r\n              type: 'devicePlan',\r\n              planType: 'whole',\r\n              items: this.testItems.map(item => item.key)\r\n            }));\r\n            hasSentMessage = true; // 标记已发送过消息\r\n          }\r\n        };\r\n        socket.onerror = (error) => {\r\n          console.error(`WebSocket错误（地址：${address}）:`, error);\r\n          message.error(`与工装（地址：${address}）的连接出现错误，请检查网络`);\r\n        };\r\n        socket.onclose = () => {\r\n          console.log(`WebSocket连接已断开，地址：${address}`);\r\n          message.warning(`WebSocket连接已断开，地址：${address}`);\r\n        };\r\n        socket.onmessage = this.handleWebSocketMessage;\r\n\r\n        timeoutId = setTimeout(checkConnection, currentTimeout);\r\n        this.websockets.push(socket);\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(event) {\r\n      const data = JSON.parse(event.data)\r\n      console.log(data)\r\n\r\n      if (data.type === 'startTest') {\r\n        this.updateTestStatus(data)\r\n        return\r\n      }\r\n\r\n      switch (data.type) {\r\n        case 'devicePlan':\r\n          if(data.error_code === 0){\r\n            message.success('测试方案已配置成功')\r\n          } else {\r\n            message.warning(`${data.message}`)\r\n          }\r\n          break\r\n        case 'deviceInfo':\r\n          this.updateTestDataInfo(data)\r\n          break\r\n        case 'finishTest':\r\n        case 'failTest':\r\n          this.updateTestStatus(data)\r\n          break\r\n        case 'startItem':\r\n        case 'finishItem':\r\n        case 'failItem':\r\n          this.updateTestItemStatus(data)\r\n          break\r\n        case 'boardCode':\r\n          this.handleBarcodeResponse(data)\r\n          break\r\n        default:\r\n          console.warn('未知的消息类型:', data.type)\r\n      }\r\n    },\r\n    handleBarcodeResponse(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record) {\r\n        if (data.error_code === 0) {\r\n          this.updateSubmissionStatus(record.key, 'success')\r\n          record.testStatus = null\r\n          record.isTesting = false\r\n          record.time = 0\r\n        } else {\r\n          this.updateSubmissionStatus(record.key, 'error')\r\n          message.error(`条码提交失败: ${data.message}`)\r\n        }\r\n        this.$forceUpdate()\r\n      }\r\n    },\r\n    updateTestDataInfo(data) {\r\n      const newDataArr = data.devices.map((item, index) => {\r\n        const dataObj = {\r\n          key: `${data.name}-${item}-${index}`,\r\n          name: item,\r\n          device: data.name,\r\n          time: 0,\r\n          barcode: '',\r\n          version : data.version,\r\n        };\r\n        this.testItems.forEach(testItem => {\r\n          dataObj[testItem.key] = null; // 此处可根据实际业务场景灵活设置初始值，比如从其他地方获取合适的值来替换null\r\n        });\r\n        return dataObj;\r\n      });\r\n      this.testData = this.testData.concat(newDataArr);\r\n      this.socketMap.push(data.name)\r\n    },\r\n    // 更新测试状态\r\n    updateTestStatus(data) {\r\n      const record = this.testData.find(\r\n          item => item.name === data.devices &&\r\n              item.barcode === data.barcode\r\n      );\r\n\r\n      if (record) {\r\n        if (data.type === 'startTest') {\r\n          record.isTesting = true;\r\n          record.testStatus = 'testing';\r\n          this.startTimer(data);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        } else {\r\n          record.isTesting = false;\r\n          record.testStatus = data.type === 'finishTest'? 'success' : 'fail';\r\n          this.$set(record, 'testStatus', record.testStatus);\r\n          this.stopTimer(data);\r\n\r\n          setTimeout(() => {\r\n            if (data.type === 'finishTest') {\r\n              record.barcode = '';\r\n            }\r\n            this.$set(record, 'barcode', record.barcode);\r\n          }, 2000);\r\n          // 强制触发视图更新\r\n          this.$forceUpdate();\r\n        }\r\n      }\r\n    },\r\n    updateTestItemStatus(data) {\r\n      const targetData  = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (targetData) {\r\n        if (data.type === 'startItem') {\r\n          targetData[data.item]='testing'\r\n          this.testData[targetData.key]=targetData\r\n        } else if (data.type === 'finishItem') {\r\n          targetData[data.item]='success'\r\n          this.testData[targetData.key]=targetData\r\n        } else {\r\n          targetData[data.item]='fail'\r\n          this.testData[targetData.key]=targetData\r\n        }\r\n      }\r\n    },\r\n    startTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && !this.timers[record.key]) {\r\n        let seconds = 0\r\n        this.timers[record.key] = setInterval(() => {\r\n          seconds++\r\n          record.time = this.formatTime(seconds)\r\n          this.testData[record.key].time=record.time\r\n        }, 1000)\r\n      }\r\n    },\r\n\r\n    stopTimer(data) {\r\n      const record = this.testData.find(item => item.name === data.devices && item.barcode === data.barcode)\r\n      if (record && this.timers[record.key]) {\r\n        clearInterval(this.timers[record.key])\r\n        delete this.timers[record.key]\r\n      }\r\n    },\r\n\r\n    formatTime(seconds) {\r\n      const minutes = Math.floor(seconds / 60)\r\n      const remainingSeconds = seconds % 60\r\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`\r\n    },\r\n  },\r\n  mounted() {\r\n    setTimeout(() => {\r\n      this.initWebSocket();\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    Object.keys(this.timers).forEach(key => {\r\n      clearInterval(this.timers[key]);\r\n    });\r\n    this.websockets.forEach((socket) => {\r\n      if (socket) {\r\n        socket.close();\r\n      }\r\n    });\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.testing-interface {\r\n  padding: 24px;\r\n  background: #f0f2f5;\r\n}\r\n\r\n.device-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.device-id {\r\n  font-weight: 500;\r\n}\r\n\r\n.device-version {\r\n  display: flex;\r\n  color: #666;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #1890ff;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #52c41a;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-error {\r\n  background-color: #ff4d4f;\r\n  color: white;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.input-success {\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n:deep(.ant-table-thead > tr > th) {\r\n  background: #fafafa;\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.ant-table-tbody > tr > td) {\r\n  padding: 12px 8px;\r\n}\r\n\r\n:deep(.ant-input) {\r\n  width: 100%;\r\n}\r\n\r\n:deep(.ant-tag) {\r\n  margin: 0;\r\n  padding: 4px 8px;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.input-success {\r\n  background-color: #f6ffed;\r\n  border-color: #52c41a;\r\n}\r\n\r\n.input-error {\r\n  background-color: #fff1f0;\r\n  border-color: #ff4d4f;\r\n}\r\n\r\n.time-default {\r\n  background-color: white;\r\n  color: black;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-testing {\r\n  background-color: #e6f7ff;\r\n  color: #1890ff;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.time-success {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.time-error {\r\n  background-color: #fff1f0;\r\n  color: #ff4d4f;\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WholeMachineTest.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WholeMachineTest.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./WholeMachineTest.vue?vue&type=template&id=b7a44aa0&scoped=true\"\nimport script from \"./WholeMachineTest.vue?vue&type=script&lang=js\"\nexport * from \"./WholeMachineTest.vue?vue&type=script&lang=js\"\nimport style0 from \"./WholeMachineTest.vue?vue&type=style&index=0&id=b7a44aa0&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b7a44aa0\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"form-container\"},[_c('div',{staticClass:\"info-section add-section\"},[_c('a-form',{attrs:{\"form\":_vm.addForm,\"layout\":\"horizontal\"}},[_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"起始资产编码:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['barcode',{ rules: [{ required: true, message: '请输入资产编码' }, { validator: _vm.validateAssetCode }]}]),expression:\"['barcode',{ rules: [{ required: true, message: '请输入资产编码' }, { validator: validateAssetCode }]}]\"}],attrs:{\"placeholder\":\"只允许输入21或22位10进制值\",\"maxLength\":22}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"添加数量:\"}},[_c('a-input-group',{attrs:{\"compact\":\"\"}},[_c('a-input-number',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['count', { rules: [{ required: true, message: '请输入数量' },{ validator: _vm.validateCount }] }]),expression:\"['count', { rules: [{ required: true, message: '请输入数量' },{ validator: validateCount }] }]\"}],attrs:{\"min\":1,\"max\":10000}})],1)],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"起始通讯地址:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['address',{ rules: [{ required: true, message: '请输入通讯地址' },{ validator: _vm.validateNumber }]}]),expression:\"['address',{ rules: [{ required: true, message: '请输入通讯地址' },{ validator: validateNumber }]}]\"}],attrs:{\"placeholder\":\"只允许输入12位10进制值\",\"maxLength\":12}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"生产日期:\"}},[_c('a-date-picker',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['produceDate', { initialValue: _vm.initialDate }]),expression:\"['produceDate', { initialValue: initialDate }]\"}],attrs:{\"placeholder\":\"格式: YYYY-MM-DD\",\"locale\":_vm.locale,\"format\":'YYYY-MM-DD'}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-checkbox',{model:{value:(_vm.mode),callback:function ($$v) {_vm.mode=$$v},expression:\"mode\"}},[_vm._v(\"上电启用安全模式\")])],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"起始表号:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['meter',{ rules: [{ required: true, message: '请输入表号' },{ validator: _vm.validateNumber }]}]),expression:\"['meter',{ rules: [{ required: true, message: '请输入表号' },{ validator: validateNumber }]}]\"}],attrs:{\"placeholder\":\"只允许输入12位10进制值\",\"maxLength\":12}})],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"硬件版本:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['hardVersion']),expression:\"['hardVersion']\"}],attrs:{\"placeholder\":\"最大长度4\",\"maxLength\":4}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"厂商代码:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['vendorCode']),expression:\"['vendorCode']\"}],attrs:{\"placeholder\":\"最大长度4\",\"maxLength\":4}})],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"硬件日期:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['hardDate', { rules: [{ validator: _vm.validateNumber }] }]),expression:\"['hardDate', { rules: [{ validator: validateNumber }] }]\"}],attrs:{\"placeholder\":\"最大长度6\",\"maxLength\":6}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"厂商扩展:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['vendorExt']),expression:\"['vendorExt']\"}],attrs:{\"placeholder\":\"最大长度8\",\"maxLength\":8}})],1)],1),_c('a-col',{staticStyle:{\"text-align\":\"right\"},attrs:{\"span\":8}},[_c('a-button',{style:({ width: '120px', height: '40px', fontSize: '16px' }),attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleAddSave}},[_vm._v(\"保存\")])],1)],1)],1)],1),_c('a-card',{staticClass:\"query-card_\",attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"form\":_vm.queryForm,\"layout\":\"inline\"},on:{\"submit\":_vm.handleQuery}},[_c('a-row',{staticStyle:{\"height\":\"100px\",\"background-color\":\"#f0f9eb\",\"display\":\"flex\",\"align-items\":\"center\"}},[_c('a-col',{attrs:{\"span\":4}},[_c('a-form-item',{attrs:{\"label\":\"资产编码:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['barcode',{ rules: [{ validator: _vm.validateNumber }]}]),expression:\"['barcode',{ rules: [{ validator: validateNumber }]}]\"}],attrs:{\"maxLength\":22}})],1)],1),_c('a-col',{attrs:{\"span\":4}},[_c('a-form-item',{attrs:{\"label\":\"通讯地址:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['address',{ rules: [{ validator: _vm.validateNumber }]}]),expression:\"['address',{ rules: [{ validator: validateNumber }]}]\"}],attrs:{\"maxLength\":12,\"inputProps\":{ style: { width: '500px' } }}})],1)],1),_c('a-col',{attrs:{\"span\":3}},[_c('a-form-item',{attrs:{\"label\":\"表号:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['meter',{ rules: [{ validator: _vm.validateNumber }]}]),expression:\"['meter',{ rules: [{ validator: validateNumber }]}]\"}],attrs:{\"maxLength\":12}})],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',{attrs:{\"label\":\"硬件版本:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['hardVersion']),expression:\"['hardVersion']\"}],attrs:{\"maxLength\":4}})],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',{attrs:{\"label\":\"硬件日期:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['hardDate', { rules: [{ validator: _vm.validateNumber }] }]),expression:\"['hardDate', { rules: [{ validator: validateNumber }] }]\"}],attrs:{\"maxLength\":6}})],1)],1),_c('a-col',{attrs:{\"span\":3}},[_c('a-form-item',{attrs:{\"label\":\"生产日期:\"}},[_c('a-date-picker',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['produceDate']),expression:\"['produceDate']\"}],attrs:{\"placeholder\":\"选择日期\",\"locale\":_vm.locale,\"format\":'YYYY-MM-DD'}})],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',{attrs:{\"label\":\"厂商代码:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['vendorCode']),expression:\"['vendorCode']\"}],attrs:{\"maxLength\":4}})],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',{attrs:{\"label\":\"厂商扩展:\"}},[_c('a-input',{directives:[{name:\"decorator\",rawName:\"v-decorator\",value:(['vendorExt']),expression:\"['vendorExt']\"}],attrs:{\"maxLength\":8}})],1)],1),_c('a-col',{attrs:{\"span\":2}},[_c('a-form-item',{attrs:{\"label\":\"操作:\"}},[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"search\"},on:{\"click\":_vm.handleQuery}},[_vm._v(\"查询\")])],1)],1)],1)],1),_c('a-table',{staticStyle:{\"margin-top\":\"20px\"},attrs:{\"rowKey\":\"key\",\"columns\":_vm.columns,\"dataSource\":_vm.tableData,\"pagination\":_vm.pagination},on:{\"change\":_vm.handleTableChange},scopedSlots:_vm._u([{key:\"mode\",fn:function(text){return [_c('a-tag',{attrs:{\"color\":text ? 'green' : 'red'}},[_vm._v(\" \"+_vm._s(text ? '已启用' : '未启用')+\" \")])]}},{key:\"operation\",fn:function(text, record){return [_c('a-button',{staticClass:\"update-button\",attrs:{\"type\":\"primary\",\"icon\":\"edit\"},on:{\"click\":function($event){return _vm.handleEdit(record)}}},[_vm._v(\"更新 \")])]}}])})],1),_c('UpdateNumberInfo',{ref:\"UpdateNumberInfo\",attrs:{\"initialbarcode\":_vm.barcode,\"initialaddress\":_vm.address,\"initialmeter\":_vm.meter,\"initialhardVersion\":_vm.hardVersion,\"initialhardDate\":_vm.hardDate,\"initialproduceDate\":_vm.produceDate,\"initialvendorCode\":_vm.vendorCode,\"initialvendorExt\":_vm.vendorExt,\"initialmode\":_vm.isMode}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-modal',{attrs:{\"title\":\"更新写号信息\",\"visible\":_vm.visible,\"confirm-loading\":_vm.confirmLoading,\"footer\":null,\"width\":\"1000px\",\"height\":\"1000px\"},on:{\"ok\":_vm.handleOk,\"cancel\":_vm.handleCancel}},[_c('a-spin',{attrs:{\"spinning\":_vm.loading}},[_c('div',{staticClass:\"info-section update-section\"},[_c('a-form',{attrs:{\"form\":_vm.updateForm,\"layout\":\"horizontal\"}},[_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"资产编码:\",\"validate-status\":_vm.errors.barcode ? 'error' : '',\"help\":_vm.errors.barcode}},[_c('a-input',{attrs:{\"placeholder\":\"只允许输入21或22位10进制值\",\"required\":\"\",\"maxLength\":22,\"disabled\":true},on:{\"blur\":function($event){return _vm.validateInput('barcode', [21, 22])}},model:{value:(_vm.localbarcode),callback:function ($$v) {_vm.localbarcode=$$v},expression:\"localbarcode\"}})],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"通讯地址:\",\"validate-status\":_vm.errors.address ? 'error' : '',\"help\":_vm.errors.address}},[_c('a-input',{attrs:{\"placeholder\":\"只允许输入12位10进制值\",\"maxLength\":12},on:{\"blur\":function($event){return _vm.validateInput('address', 12)}},model:{value:(_vm.localaddress),callback:function ($$v) {_vm.localaddress=$$v},expression:\"localaddress\"}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"生产日期:\",\"validate-status\":_vm.errors.produceDate ? 'error' : '',\"help\":_vm.errors.produceDate}},[_c('a-date-picker',{attrs:{\"placeholder\":\"格式: YYYY-MM-DD\"},on:{\"change\":_vm.validateDate},model:{value:(_vm.localproduceDate),callback:function ($$v) {_vm.localproduceDate=$$v},expression:\"localproduceDate\"}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-checkbox',{model:{value:(_vm.localmode),callback:function ($$v) {_vm.localmode=$$v},expression:\"localmode\"}},[_vm._v(\"上电启用安全模式\")])],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"表号:\",\"validate-status\":_vm.errors.meter ? 'error' : '',\"help\":_vm.errors.meter}},[_c('a-input',{attrs:{\"placeholder\":\"只允许输入12位10进制值\",\"maxLength\":12},on:{\"blur\":function($event){return _vm.validateInput('meter', 12)}},model:{value:(_vm.localmeter),callback:function ($$v) {_vm.localmeter=$$v},expression:\"localmeter\"}})],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"硬件版本:\",\"validate-status\":_vm.errors.hardVersion ? 'error' : '',\"help\":_vm.errors.hardVersion}},[_c('a-input',{attrs:{\"placeholder\":\"最大长度4\",\"maxLength\":4},on:{\"blur\":function($event){return _vm.validateInput('hardVersion', 4, false)}},model:{value:(_vm.localhardVersion),callback:function ($$v) {_vm.localhardVersion=$$v},expression:\"localhardVersion\"}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"厂商代码:\",\"validate-status\":_vm.errors.vendorCode ? 'error' : '',\"help\":_vm.errors.vendorCode}},[_c('a-input',{attrs:{\"placeholder\":\"最大长度4\",\"maxLength\":4},on:{\"blur\":function($event){return _vm.validateInput('vendorCode', 4, false)}},model:{value:(_vm.localvendorCode),callback:function ($$v) {_vm.localvendorCode=$$v},expression:\"localvendorCode\"}})],1)],1)],1),_c('a-row',{attrs:{\"gutter\":24}},[_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"硬件日期:\",\"validate-status\":_vm.errors.hardDate ? 'error' : '',\"help\":_vm.errors.hardDate}},[_c('a-input',{attrs:{\"placeholder\":\"最大长度6\",\"maxLength\":6},on:{\"blur\":function($event){return _vm.validateInput('hardDate', 6, false)}},model:{value:(_vm.localhardDate),callback:function ($$v) {_vm.localhardDate=$$v},expression:\"localhardDate\"}})],1)],1),_c('a-col',{attrs:{\"span\":8}},[_c('a-form-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"gap\":\"8px\"},attrs:{\"label\":\"厂商扩展:\",\"validate-status\":_vm.errors.vendorExt ? 'error' : '',\"help\":_vm.errors.vendorExt}},[_c('a-input',{attrs:{\"placeholder\":\"最大长度8\",\"maxLength\":8},on:{\"blur\":function($event){return _vm.validateInput('vendorExt', 8, false)}},model:{value:(_vm.localvendorExt),callback:function ($$v) {_vm.localvendorExt=$$v},expression:\"localvendorExt\"}})],1)],1),_c('a-col',{staticStyle:{\"text-align\":\"right\"},attrs:{\"span\":8}},[_c('a-button',{style:({ width: '120px', height: '40px', fontSize: '16px' }),attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleUpdateSave}},[_vm._v(\" 保存 \")])],1)],1)],1)],1)])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-modal\r\n        title=\"更新写号信息\"\r\n        :visible=\"visible\"\r\n        :confirm-loading=\"confirmLoading\"\r\n        @ok=\"handleOk\"\r\n        @cancel=\"handleCancel\"\r\n        :footer=\"null\"\r\n        width=\"1000px\"\r\n        height=\"1000px\"\r\n    >\r\n      <a-spin :spinning=\"loading\">\r\n        <div class=\"info-section update-section\">\r\n          <a-form :form=\"updateForm\" layout=\"horizontal\">\r\n            <a-row :gutter=\"24\">\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"资产编码:\"\r\n                    :validate-status=\"errors.barcode ? 'error' : ''\"\r\n                    :help=\"errors.barcode\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"只允许输入21或22位10进制值\"\r\n                      v-model=\"localbarcode\"\r\n                      @blur=\"validateInput('barcode', [21, 22])\"\r\n                      required\r\n                      :maxLength=\"22\"\r\n                      :disabled=\"true\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n            </a-row>\r\n\r\n            <a-row :gutter=\"24\">\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"通讯地址:\"\r\n                    :validate-status=\"errors.address ? 'error' : ''\"\r\n                    :help=\"errors.address\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"只允许输入12位10进制值\"\r\n                      v-model=\"localaddress\"\r\n                      @blur=\"validateInput('address', 12)\"\r\n                      :maxLength=\"12\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"生产日期:\"\r\n                    :validate-status=\"errors.produceDate ? 'error' : ''\"\r\n                    :help=\"errors.produceDate\"\r\n                >\r\n                  <a-date-picker\r\n                      placeholder=\"格式: YYYY-MM-DD\"\r\n                      v-model=\"localproduceDate\"\r\n                      @change=\"validateDate\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n              <a-col :span=\"8\">\r\n                <a-checkbox v-model=\"localmode\">上电启用安全模式</a-checkbox>\r\n              </a-col>\r\n            </a-row>\r\n\r\n            <a-row :gutter=\"24\">\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"表号:\"\r\n                    :validate-status=\"errors.meter ? 'error' : ''\"\r\n                    :help=\"errors.meter\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"只允许输入12位10进制值\"\r\n                      v-model=\"localmeter\"\r\n                      @blur=\"validateInput('meter', 12)\"\r\n                      :maxLength=\"12\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n            </a-row>\r\n\r\n            <a-row :gutter=\"24\">\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"硬件版本:\"\r\n                    :validate-status=\"errors.hardVersion ? 'error' : ''\"\r\n                    :help=\"errors.hardVersion\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"最大长度4\"\r\n                      v-model=\"localhardVersion\"\r\n                      @blur=\"validateInput('hardVersion', 4, false)\"\r\n                      :maxLength=\"4\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"厂商代码:\"\r\n                    :validate-status=\"errors.vendorCode ? 'error' : ''\"\r\n                    :help=\"errors.vendorCode\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"最大长度4\"\r\n                      v-model=\"localvendorCode\"\r\n                      @blur=\"validateInput('vendorCode', 4, false)\"\r\n                      :maxLength=\"4\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n            </a-row>\r\n\r\n            <a-row :gutter=\"24\">\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"硬件日期:\"\r\n                    :validate-status=\"errors.hardDate ? 'error' : ''\"\r\n                    :help=\"errors.hardDate\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"最大长度6\"\r\n                      v-model=\"localhardDate\"\r\n                      @blur=\"validateInput('hardDate', 6, false)\"\r\n                      :maxLength=\"6\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n              <a-col :span=\"8\">\r\n                <a-form-item\r\n                    style=\"display: flex;align-items: center;gap: 8px;\"\r\n                    label=\"厂商扩展:\"\r\n                    :validate-status=\"errors.vendorExt ? 'error' : ''\"\r\n                    :help=\"errors.vendorExt\"\r\n                >\r\n                  <a-input\r\n                      placeholder=\"最大长度8\"\r\n                      v-model=\"localvendorExt\"\r\n                      @blur=\"validateInput('vendorExt', 8, false)\"\r\n                      :maxLength=\"8\"\r\n                  />\r\n                </a-form-item>\r\n              </a-col>\r\n              <a-col :span=\"8\" style=\"text-align: right\">\r\n                <a-button\r\n                    type=\"primary\"\r\n                    :style=\"{ width: '120px', height: '40px', fontSize: '16px' }\"\r\n                    @click=\"handleUpdateSave\"\r\n                >\r\n                  保存\r\n                </a-button>\r\n              </a-col>\r\n            </a-row>\r\n          </a-form>\r\n        </div>\r\n      </a-spin>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { updateNumber } from \"@/api/api\";\r\nimport moment from \"moment/moment\";\r\nimport { notification } from 'ant-design-vue'\r\n\r\nexport default {\r\n  name: 'UpdateDeviceInfo',\r\n  props: {\r\n    initialbarcode: { type: String, required: true },\r\n    initialaddress: { type: String, required: true },\r\n    initialmeter: { type: String, required: true },\r\n    initialhardVersion: { type: String, required: true },\r\n    initialhardDate: { type: String, required: true },\r\n    initialproduceDate: { type: String, required: true },\r\n    initialvendorCode: { type: String, required: true },\r\n    initialvendorExt: { type: String, required: true },\r\n    initialmode: { type: Boolean, required: true },\r\n  },\r\n  data() {\r\n    return {\r\n      localbarcode: this.initialbarcode,\r\n      localaddress: this.initialaddress,\r\n      localmeter: this.initialmeter,\r\n      localhardVersion: this.initialhardVersion,\r\n      localhardDate: this.initialhardDate,\r\n      localproduceDate: moment(this.initialproduceDate),\r\n      localvendorCode: this.initialvendorCode,\r\n      localvendorExt: this.initialvendorExt,\r\n      localmode: this.initialmode,\r\n      updateForm: this.$form.createForm(this),\r\n      visible: false,\r\n      confirmLoading: false,\r\n      loading: false,\r\n      errors: {\r\n        barcode: '',\r\n        address: '',\r\n        meter: '',\r\n        hardVersion: '',\r\n        hardDate: '',\r\n        produceDate: '',\r\n        vendorCode: '',\r\n        vendorExt: '',\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    const updateFields = [\r\n      'barcode', 'address', 'meter', 'hardVersion', 'hardDate',\r\n      'produceDate', 'vendorCode', 'vendorExt', 'isMode'\r\n    ];\r\n\r\n    updateFields.forEach(field => {\r\n      this.$parent.$on(`initial-${field}-updated`, (newValue) => {\r\n        const localField = field === 'isMode' ? 'mode' : field;\r\n        if (field === 'produceDate') {\r\n          this[`local${localField}`] = moment(newValue);\r\n        } else {\r\n          this[`local${localField}`] = newValue;\r\n        }\r\n      });\r\n    });\r\n  },\r\n  methods: {\r\n    validateInput(field, lengths, onlyDigits = true) {\r\n      const value = this[`local${field}`];\r\n      if (!value) {\r\n        if(field === 'barcode'){\r\n          this.errors[field] = '此字段不能为空';\r\n        }\r\n      } else if (onlyDigits && !/^\\d+$/.test(value)) {\r\n        this.errors[field] = '只允许输入数字';\r\n      } else if (Array.isArray(lengths)) {\r\n        if (!lengths.includes(value.length)) {\r\n          this.errors[field] = `请输入${lengths.join('或')}位数字`;\r\n        } else {\r\n          this.errors[field] = '';\r\n        }\r\n      } else if (value.length !== lengths && onlyDigits) {\r\n        this.errors[field] = `请输入${lengths}位数字`;\r\n      } else if (value.length > lengths && !onlyDigits) {\r\n        this.errors[field] = `最大长度为${lengths}`;\r\n      } else {\r\n        this.errors[field] = '';\r\n      }\r\n    },\r\n    validateDate() {\r\n      if (!this.localproduceDate) {\r\n        this.errors.produceDate = '请选择日期';\r\n      } else {\r\n        this.errors.produceDate = '';\r\n      }\r\n    },\r\n    validateAllFields() {\r\n      this.validateInput('barcode', [21, 22]);\r\n      this.validateInput('address', 12);\r\n      this.validateInput('meter', 12);\r\n      this.validateInput('hardVersion', 4, false);\r\n      this.validateInput('hardDate', 6, false);\r\n      this.validateInput('vendorCode', 4, false);\r\n      this.validateInput('vendorExt', 8, false);\r\n      this.validateDate();\r\n    },\r\n    handleUpdateSave() {\r\n      this.validateAllFields();\r\n\r\n      if (Object.values(this.errors).some(error => error !== '')) {\r\n        this.$message.error('请修正输入错误后再保存');\r\n        return;\r\n      }\r\n\r\n      if (!this.localbarcode) {\r\n        this.$message.error('资产编码为必填项');\r\n        return;\r\n      }\r\n\r\n      const timeString = this.localproduceDate.format('YYYY-MM-DD');\r\n      updateNumber(\r\n          this.localbarcode,\r\n          this.localaddress,\r\n          this.localmeter,\r\n          this.localhardVersion,\r\n          this.localhardDate,\r\n          timeString,\r\n          this.localvendorCode,\r\n          this.localvendorExt,\r\n          this.localmode\r\n      ).then(res => {\r\n        console.log(res);\r\n        if (res.error_code === 0) {\r\n          this.$message.success({\r\n            content: '更新成功',\r\n            duration: 3,\r\n          });\r\n          this.$emit('child-message', res.error_code);\r\n          this.handleCancel();\r\n        }\r\n      }).catch(error => {\r\n        notification.error({\r\n          message: '系统提示',\r\n          description: error.response.data.message,\r\n          duration: 4\r\n        });\r\n      })\r\n    },\r\n    showModal() {\r\n      this.visible = true;\r\n    },\r\n    handleCancel() {\r\n      this.visible = false;\r\n      this.updateForm.resetFields();\r\n      Object.keys(this.errors).forEach(key => this.errors[key] = '');\r\n    },\r\n    handleOk() {\r\n      this.updateForm.submit();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.info-section {\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.update-section {\r\n  background-color: #fdf6ec;\r\n}\r\n\r\n:deep(.ant-form-item-label) {\r\n  text-align: right;\r\n}\r\n\r\n:deep(.ant-table-pagination) {\r\n  margin: 16px 0;\r\n}\r\n</style>\r\n\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdateNumberInfo.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdateNumberInfo.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UpdateNumberInfo.vue?vue&type=template&id=1faae1f8&scoped=true\"\nimport script from \"./UpdateNumberInfo.vue?vue&type=script&lang=js\"\nexport * from \"./UpdateNumberInfo.vue?vue&type=script&lang=js\"\nimport style0 from \"./UpdateNumberInfo.vue?vue&type=style&index=0&id=1faae1f8&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1faae1f8\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"form-container\">\r\n    <div class=\"info-section add-section\">\r\n      <a-form :form=\"addForm\" layout=\"horizontal\">\r\n        <a-row :gutter=\"24\">\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"起始资产编码:\" >\r\n              <a-input placeholder=\"只允许输入21或22位10进制值\" :maxLength=\"22\" v-decorator=\"['barcode',{ rules: [{ required: true, message: '请输入资产编码' }, { validator: validateAssetCode }]}]\" />\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"添加数量:\">\r\n              <a-input-group compact>\r\n                <a-input-number\r\n                    :min=\"1\"\r\n                    :max=\"10000\"\r\n                    v-decorator=\"['count', { rules: [{ required: true, message: '请输入数量' },{ validator: validateCount }] }]\"\r\n                />\r\n              </a-input-group>\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n\r\n        <a-row :gutter=\"24\">\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"起始通讯地址:\">\r\n              <a-input placeholder=\"只允许输入12位10进制值\" :maxLength=\"12\"  v-decorator=\"['address',{ rules: [{ required: true, message: '请输入通讯地址' },{ validator: validateNumber }]}]\" />\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"生产日期:\">\r\n              <a-date-picker placeholder=\"格式: YYYY-MM-DD\" :locale=\"locale\" v-decorator=\"['produceDate', { initialValue: initialDate }]\" :format=\"'YYYY-MM-DD'\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-checkbox v-model=\"mode\">上电启用安全模式</a-checkbox>\r\n          </a-col>\r\n        </a-row>\r\n\r\n        <a-row :gutter=\"24\">\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"起始表号:\">\r\n              <a-input placeholder=\"只允许输入12位10进制值\" :maxLength=\"12\" v-decorator=\"['meter',{ rules: [{ required: true, message: '请输入表号' },{ validator: validateNumber }]}]\" />\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n\r\n        <a-row :gutter=\"24\">\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"硬件版本:\">\r\n              <a-input placeholder=\"最大长度4\" :maxLength=\"4\" v-decorator=\"['hardVersion']\" />\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"厂商代码:\">\r\n              <a-input placeholder=\"最大长度4\" :maxLength=\"4\"  v-decorator=\"['vendorCode']\" />\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n\r\n        <a-row :gutter=\"24\">\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"硬件日期:\">\r\n              <a-input placeholder=\"最大长度6\" :maxLength=\"6\" v-decorator=\"['hardDate', { rules: [{ validator: validateNumber }] }]\" />\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\">\r\n            <a-form-item style=\"display: flex;align-items: center;gap: 8px;\" label=\"厂商扩展:\">\r\n              <a-input placeholder=\"最大长度8\" :maxLength=\"8\" v-decorator=\"['vendorExt']\" />\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"8\" style=\"text-align: right\">\r\n            <a-button type=\"primary\" :style=\"{ width: '120px', height: '40px', fontSize: '16px' }\" @click=\"handleAddSave\">保存</a-button>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n    </div>\r\n\r\n    <a-card :bordered=\"false\" class=\"query-card_\">\r\n      <a-form :form=\"queryForm\" layout=\"inline\" @submit=\"handleQuery\">\r\n        <a-row style=\"height: 100px;background-color: #f0f9eb;display: flex;align-items: center\">\r\n          <a-col :span=\"4\">\r\n            <a-form-item label=\"资产编码:\" >\r\n              <a-input :maxLength=\"22\"  v-decorator=\"['barcode',{ rules: [{ validator: validateNumber }]}]\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"4\">\r\n            <a-form-item label=\"通讯地址:\" >\r\n              <a-input :maxLength=\"12\" :inputProps=\"{ style: { width: '500px' } }\"  v-decorator=\"['address',{ rules: [{ validator: validateNumber }]}]\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"3\">\r\n            <a-form-item label=\"表号:\" >\r\n              <a-input :maxLength=\"12\" v-decorator=\"['meter',{ rules: [{ validator: validateNumber }]}]\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item label=\"硬件版本:\" >\r\n              <a-input :maxLength=\"4\"  v-decorator=\"['hardVersion']\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item label=\"硬件日期:\" >\r\n              <a-input :maxLength=\"6\"  v-decorator=\"['hardDate', { rules: [{ validator: validateNumber }] }]\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"3\">\r\n            <a-form-item label=\"生产日期:\" >\r\n              <a-date-picker placeholder=\"选择日期\" :locale=\"locale\"  v-decorator=\"['produceDate']\" :format=\"'YYYY-MM-DD'\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item label=\"厂商代码:\" >\r\n              <a-input :maxLength=\"4\"  v-decorator=\"['vendorCode']\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item label=\"厂商扩展:\" >\r\n              <a-input :maxLength=\"8\"  v-decorator=\"['vendorExt']\"/>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"2\">\r\n            <a-form-item label=\"操作:\" >\r\n              <a-button type=\"primary\" html-type=\"submit\" icon=\"search\" @click=\"handleQuery\">查询</a-button>\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n      <a-table\r\n          style=\"margin-top: 20px\"\r\n          rowKey=\"key\"\r\n          :columns=\"columns\"\r\n          :dataSource=\"tableData\"\r\n          :pagination=\"pagination\"\r\n          @change='handleTableChange'\r\n      >\r\n        <template slot=\"mode\" slot-scope=\"text\">\r\n          <a-tag :color=\"text ? 'green' : 'red'\">\r\n            {{ text ? '已启用' : '未启用' }}\r\n          </a-tag>\r\n        </template>\r\n        <template slot=\"operation\" slot-scope=\"text, record\">\r\n          <a-button class=\"update-button\"\r\n                    type=\"primary\"\r\n                    icon=\"edit\"\r\n                    @click=\"handleEdit(record)\">更新\r\n          </a-button>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n    <UpdateNumberInfo :initialbarcode=\"barcode\" :initialaddress=\"address\" :initialmeter=\"meter\" :initialhardVersion=\"hardVersion\" :initialhardDate=\"hardDate\" :initialproduceDate=\"produceDate\" :initialvendorCode=\"vendorCode\" :initialvendorExt=\"vendorExt\" :initialmode=\"isMode\" ref=\"UpdateNumberInfo\"></UpdateNumberInfo>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UpdateNumberInfo from \"@/components/UpdateNumberInfo.vue\";\r\nimport {addNumber, checkNumber} from \"@/api/api\";\r\nimport moment from \"moment/moment\";\r\n\r\nexport default {\r\n  components: {\r\n    UpdateNumberInfo\r\n  },\r\n  data() {\r\n    return {\r\n      initialDate:null,\r\n      barcode:'',\r\n      address:'',\r\n      meter:'',\r\n      hardVersion:'',\r\n      hardDate:'',\r\n      produceDate:'',\r\n      vendorCode:'',\r\n      vendorExt:'',\r\n      isMode:false,\r\n      locale: {\r\n        lang: {\r\n          placeholder: '请选择日期',\r\n          yearPlaceholder: '年',\r\n          monthPlaceholder: '月',\r\n          dayPlaceholder: '日',\r\n          weekPlaceholder: '周',\r\n          rangePlaceholder: ['开始日期', '结束日期'],\r\n          today: '今天',\r\n          now: '此刻',\r\n          ok: '确定',\r\n          clear: '清除',\r\n          prevYear: '去年',\r\n          nextYear: '明年',\r\n          prevMonth: '上月',\r\n          nextMonth: '下月',\r\n          monthSelect: '选择月份',\r\n          yearSelect: '选择年份',\r\n          decadeSelect: '选择年代',\r\n          yearFormat: 'YYYY年',\r\n          monthFormat: 'MM月',\r\n          dateFormat: 'YYYY-MM-DD',\r\n          dayFormat: 'D日',\r\n          dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',\r\n          timeFormat: 'HH:mm:ss',\r\n          secondFormat: 'ss秒',\r\n          meridiem: '上午/下午',\r\n          am: '上午',\r\n          pm: '下午',\r\n        },\r\n        timePickerLocale: {\r\n          placeholder: '请选择时间',\r\n        },\r\n      },\r\n      power:'',\r\n      board:'',\r\n      whole:'',\r\n      queryForm: this.$form.createForm(this),\r\n      addForm: this.$form.createForm(this),\r\n      mode: false,\r\n      columns: [\r\n        {title: '资产条码', dataIndex: 'assetCode', width: 180},\r\n        {title: '通讯地址', dataIndex: 'address', width: 180},\r\n        {title: '表号', dataIndex: 'meterNo', width: 120},\r\n        {title: '硬件版本', dataIndex: 'hardwareVersion', width: 100},\r\n        {title: '硬件日期', dataIndex: 'hardwareDate', width: 100},\r\n        {title: '生产日期', dataIndex: 'productionDate', width: 120},\r\n        {title: '厂商代码', dataIndex: 'manufacturerCode', width: 100},\r\n        {title: '厂商扩展', dataIndex: 'manufacturerExt', width: 100},\r\n        {title: '安全模式', dataIndex: 'mode',scopedSlots: { customRender: 'mode' }, width: 100},\r\n        {title: '操作', key: 'operation', scopedSlots: { customRender: 'operation' },width: 100},\r\n      ],\r\n      tableData: [],\r\n      pagination: {\r\n        current: 1,\r\n        pageSize: 10,\r\n        pageSizeOptions: ['10','20','50','100'],\r\n        showTotal: (total, range) => {\r\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\r\n        },\r\n        showQuickJumper: true,\r\n        showSizeChanger: true,\r\n        total: 0\r\n      },\r\n      myMoment:'',\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initialDate = this.getValidInitialDate();\r\n    this.fetchData()\r\n  },\r\n  methods: {\r\n    getValidInitialDate() {\r\n      let dateStr = moment().format('YYYY-MM-DD');\r\n      let momentDate = moment(dateStr, 'YYYY-MM-DD');\r\n      if (momentDate.isValid()) {\r\n        return momentDate;\r\n      }\r\n      return null;\r\n    },\r\n    fetchData(){\r\n      this.queryForm.validateFields((err, values) => {\r\n        if (!err) {\r\n          let timeString\r\n          if(values.produceDate!==undefined&&values.produceDate!==null){\r\n            this.myMoment=values.produceDate\r\n            timeString = this.myMoment.format('YYYY-MM-DD');\r\n          }\r\n          checkNumber(values.barcode, values.address,values.meter,values.hardVersion, values.hardDate,timeString,values.vendorCode,values.vendorExt,this.pagination.current,this.pagination.pageSize).then(res => {\r\n            console.log(res)\r\n            this.pagination.total=res.page_count*this.pagination.pageSize\r\n            this.tableData = res.records.map((item, index) => ({\r\n              key: index,\r\n              assetCode: item.barcode,\r\n              address: item.address,\r\n              meterNo: item.meter,\r\n              hardwareVersion: item.hardVersion,\r\n              hardwareDate: item.hardDate,\r\n              productionDate: item.produceDate,\r\n              manufacturerCode: item.vendorCode,\r\n              manufacturerExt: item.vendorExt,\r\n              mode:item.mode\r\n            }));\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleTableChange(pagination){\r\n      this.pagination = pagination\r\n      this.fetchData()\r\n    },\r\n    validateCount(rule, value, callback) {\r\n      if (value === undefined || value === null || value === '') {\r\n        callback();\r\n        return;\r\n      }\r\n\r\n      const numValue = Number(value);\r\n      if (isNaN(numValue)) {\r\n        callback(new Error('请输入数字'));\r\n        return;\r\n      }\r\n\r\n      if (numValue % 1!== 0) {\r\n        callback(new Error('请输入整数，不能输入小数'));\r\n        return;\r\n      }\r\n\r\n      if (!/^\\d+$/.test(numValue.toString())) {\r\n        callback(new Error('请输入纯数字，不能包含其他字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateAssetCode(rule, value, callback) {\r\n      if (!value) {\r\n        callback();\r\n        return;\r\n      }\r\n      const decimalValue = Number(value);\r\n      if (Number.isNaN(decimalValue)) {\r\n        callback('请输入有效的 10 进制数字');\r\n      } else if (value.length !== 21 && value.length !== 22) {\r\n        callback('只能输入 21 或 22 位数字');\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateNumber(rule, value, callback) {\r\n      if(rule.field === 'barcode'){\r\n        if (!value) {\r\n          callback();\r\n          return;\r\n        }\r\n        const decimalValue = Number(value);\r\n        if (Number.isNaN(decimalValue)) {\r\n          callback('请输入有效的 10 进制数字');\r\n        } else if (value.toString().length !== 22) {\r\n          callback('只能输入22 位数字');\r\n        } else {\r\n          callback();\r\n        }\r\n      } else if (rule.field === 'address' || rule.field === 'meter') {\r\n        if (!value) {\r\n          callback();\r\n          return;\r\n        }\r\n        const decimalValue = Number(value);\r\n        if (Number.isNaN(decimalValue)) {\r\n          callback('请输入有效的 10 进制数字');\r\n        } else if (value.toString().length !== 12) {\r\n          callback('只能输入 12 位数字');\r\n        } else {\r\n          callback();\r\n        }\r\n      } else if (rule.field === 'hardDate') {\r\n        if (!value) {\r\n          callback();\r\n          return;\r\n        }\r\n        const decimalValue = Number(value);\r\n        if (Number.isNaN(decimalValue)) {\r\n          callback('请输入有效的 10 进制数字');\r\n        } else if (value.toString().length > 6) {\r\n          callback('最大长度 6 位');\r\n        } else {\r\n          callback();\r\n        }\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    handleQuery(e) {\r\n      e.preventDefault();\r\n      this.fetchData()\r\n    },\r\n    handleAddSave() {\r\n      this.addForm.validateFields((err, values) => {\r\n        this.myMoment=values.produceDate\r\n        const timeString = this.myMoment.format('YYYY-MM-DD');\r\n        console.log(timeString)\r\n        if (!err) {\r\n          addNumber(values.barcode,values.address,values.meter,values.hardVersion,values.hardDate,timeString,values.vendorCode,values.vendorExt,this.mode,values.count).then(res=>{\r\n            console.log(res)\r\n            if(res.error_code===0){\r\n              this.$message.success( '保存成功')\r\n            }else {\r\n              this.$message.error( res.message)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handleEdit(record) {\r\n      try {\r\n        this.barcode=record.assetCode\r\n        this.address=record.address\r\n        this.meter=record.meterNo\r\n        this.hardVersion=record.hardwareVersion\r\n        this.hardDate=record.hardwareDate\r\n        this.produceDate=record.productionDate\r\n        console.log(this.produceDate)\r\n        this.vendorCode=record.manufacturerCode\r\n        this.vendorExt=record.manufacturerExt\r\n        this.isMode=record.mode\r\n        console.log(this.power)\r\n        this.$emit('initial-barcode-updated', this.barcode);\r\n        this.$emit('initial-address-updated', this.address);\r\n        this.$emit('initial-meter-updated', this.meter);\r\n        this.$emit('initial-hardVersion-updated', this.hardVersion);\r\n        this.$emit('initial-hardDate-updated', this.hardDate);\r\n        this.$emit('initial-produceDate-updated', this.produceDate);\r\n        this.$emit('initial-vendorCode-updated', this.vendorCode);\r\n        this.$emit('initial-vendorExt-updated', this.vendorExt);\r\n        this.$emit('initial-isMode-updated', this.isMode);\r\n        this.$refs.UpdateNumberInfo.visible = true;\r\n      } catch (error) {\r\n        console.error('Error fetching initial status:', error);\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.form-container {\r\n  padding: 24px;\r\n}\r\n\r\n.info-section {\r\n  padding: 20px;\r\n  margin-bottom: 24px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.add-section {\r\n  background-color: #FFFFE0;\r\n}\r\n\r\n.update-section {\r\n  background-color: #fdf6ec;\r\n}\r\n\r\n.section-header {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n:deep(.ant-form-item-label) {\r\n  text-align: right;\r\n}\r\n\r\n:deep(.ant-table-pagination) {\r\n  margin: 16px 0;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WriteNumberConfiguration.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./WriteNumberConfiguration.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./WriteNumberConfiguration.vue?vue&type=template&id=4590a4a1&scoped=true\"\nimport script from \"./WriteNumberConfiguration.vue?vue&type=script&lang=js\"\nexport * from \"./WriteNumberConfiguration.vue?vue&type=script&lang=js\"\nimport style0 from \"./WriteNumberConfiguration.vue?vue&type=style&index=0&id=4590a4a1&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4590a4a1\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"binding-container\"},[_c('a-card',{staticClass:\"binding-form\",attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"form\":_vm.form,\"layout\":\"vertical\"}},[_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"电源板条码：\"}},[_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],attrs:{\"placeholder\":\"请输入电源板条码\",\"maxLength\":20},on:{\"blur\":function($event){return _vm.checkStatus('power')}},model:{value:(_vm.formData.powerCode),callback:function ($$v) {_vm.$set(_vm.formData, \"powerCode\", $$v)},expression:\"formData.powerCode\"}}),(_vm.errorMessage.power&&this.formData.powerCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.errorMessage.power))]):_vm._e(),(_vm.status.power)?_c('span',{class:['status-text', _vm.status.power.type]},[_vm._v(\" \"+_vm._s(_vm.status.power.message)+\" \")]):_vm._e()],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"主板条码：\"}},[_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],attrs:{\"placeholder\":\"请输入主板条码\",\"maxLength\":20},on:{\"blur\":function($event){return _vm.checkStatus('board')}},model:{value:(_vm.formData.boardCode),callback:function ($$v) {_vm.$set(_vm.formData, \"boardCode\", $$v)},expression:\"formData.boardCode\"}}),(_vm.errorMessage.board&&this.formData.boardCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.errorMessage.board))]):_vm._e(),(_vm.status.board)?_c('span',{class:['status-text', _vm.status.board.type]},[_vm._v(\" \"+_vm._s(_vm.status.board.message)+\" \")]):_vm._e()],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"资产条码：\"}},[_c('a-input',{directives:[{name:\"enter-next-input\",rawName:\"v-enter-next-input\"}],attrs:{\"placeholder\":\"请输入资产条码\",\"maxLength\":22},on:{\"blur\":function($event){return _vm.checkStatus('whole')}},model:{value:(_vm.formData.wholeCode),callback:function ($$v) {_vm.$set(_vm.formData, \"wholeCode\", $$v)},expression:\"formData.wholeCode\"}}),(_vm.errorMessage.whole&&this.formData.wholeCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.errorMessage.whole))]):_vm._e(),(_vm.status.whole)?_c('span',{class:['status-text', _vm.status.whole.type]},[_vm._v(\" \"+_vm._s(_vm.status.whole.message)+\" \")]):_vm._e()],1)],1),_c('a-button',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"margin-top\":\"29px\"},attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleSave}},[_vm._v(\"添加\")])],1)],1)],1),_c('a-card',{staticClass:\"search-section\",attrs:{\"bordered\":false}},[_c('a-form',{attrs:{\"layout\":\"horizontal\"}},[_c('a-row',{staticStyle:{\"height\":\"100px\",\"background-color\":\"#f0f9eb\",\"display\":\"flex\",\"align-items\":\"center\"},attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"电源板条码\"}},[_c('a-input',{directives:[{name:\"enter-input\",rawName:\"v-enter-input\"}],attrs:{\"placeholder\":\"请输入查询条码\",\"maxLength\":20},on:{\"blur\":function($event){return _vm.validateNumber('power')}},model:{value:(_vm.searchForm.powerCode),callback:function ($$v) {_vm.$set(_vm.searchForm, \"powerCode\", $$v)},expression:\"searchForm.powerCode\"}}),(_vm.error.power&&this.searchForm.powerCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.error.power))]):_vm._e()],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"主板条码\"}},[_c('a-input',{directives:[{name:\"enter-input\",rawName:\"v-enter-input\"}],attrs:{\"placeholder\":\"请输入查询条码\",\"maxLength\":20},on:{\"blur\":function($event){return _vm.validateNumber('board')}},model:{value:(_vm.searchForm.boardCode),callback:function ($$v) {_vm.$set(_vm.searchForm, \"boardCode\", $$v)},expression:\"searchForm.boardCode\"}}),(_vm.error.board&&this.searchForm.boardCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.error.board))]):_vm._e()],1)],1),_c('a-col',{attrs:{\"span\":5}},[_c('a-form-item',{attrs:{\"label\":\"资产条码\"}},[_c('a-input',{directives:[{name:\"enter-input\",rawName:\"v-enter-input\"}],attrs:{\"placeholder\":\"请输入查询条码\",\"maxLength\":22},on:{\"blur\":function($event){return _vm.validateNumber('whole')}},model:{value:(_vm.searchForm.wholeCode),callback:function ($$v) {_vm.$set(_vm.searchForm, \"wholeCode\", $$v)},expression:\"searchForm.wholeCode\"}}),(_vm.error.whole&&this.searchForm.wholeCode!=='')?_c('p',{staticStyle:{\"color\":\"red\"}},[_vm._v(_vm._s(_vm.error.whole))]):_vm._e()],1)],1),_c('a-col',{attrs:{\"span\":1}},[_c('a-form-item',{attrs:{\"label\":\"操作\"}},[_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"search\"},on:{\"click\":_vm.handleSearch}},[_vm._v(\"查询\")])],1)],1)],1)],1),_c('a-table',{staticStyle:{\"margin-top\":\"20px\"},attrs:{\"rowKey\":\"key\",\"columns\":_vm.columns,\"data-source\":_vm.tableData,\"pagination\":_vm.detailIpagination,\"rowClassName\":(record, index) => (index % 2 === 1 ? 'table-row-light' : 'table-row-dark')},on:{\"change\":_vm.detailhandleTableChange},scopedSlots:_vm._u([{key:\"operate\",fn:function(text, record){return [_c('a-button',{staticClass:\"update-button\",attrs:{\"type\":\"primary\",\"icon\":\"edit\"},on:{\"click\":function($event){return _vm.handleUpdate(record)}}},[_vm._v(\" 更新 \")]),_c('a-button',{staticStyle:{\"margin-left\":\"20px\"},attrs:{\"type\":\"primary\",\"icon\":\"delete\"},on:{\"click\":function($event){return _vm.handleDelete(record)}}},[_vm._v(\" 删除 \")])]}}])})],1),_c('UpdateAssemblyRelate',{ref:\"UpdateAssemblyRelate\",attrs:{\"initialPowerCode\":_vm.power,\"initialBoardCode\":_vm.board,\"initialWholeCode\":_vm.whole},on:{\"child-message\":_vm.handleChildMessage}}),_c('a-modal',{attrs:{\"title\":_vm.modal.title,\"okText\":_vm.modal.okText,\"cancelText\":_vm.modal.cancelText},on:{\"ok\":_vm.handleModalConfirm},model:{value:(_vm.modal.visible),callback:function ($$v) {_vm.$set(_vm.modal, \"visible\", $$v)},expression:\"modal.visible\"}},[_c('p',[_vm._v(_vm._s(_vm.modal.content))])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('a-modal',{attrs:{\"title\":\"更新组装关系\",\"visible\":_vm.visible,\"confirm-loading\":_vm.confirmLoading,\"footer\":null},on:{\"ok\":_vm.handleOk,\"cancel\":_vm.handleCancel}},[_c('a-spin',{attrs:{\"spinning\":_vm.loading}},[_c('a-form',{attrs:{\"form\":_vm.updateForm,\"layout\":\"vertical\"},on:{\"submit\":_vm.handleUpdate}},[_c('a-row',{attrs:{\"gutter\":16}},[_c('a-col',{attrs:{\"span\":24}},[_c('a-form-item',{attrs:{\"label\":\"电源板条码\",\"validate-status\":_vm.powerBarcodeError ? 'error' : '',\"help\":_vm.powerBarcodeError || ''}},[_c('a-input',{attrs:{\"placeholder\":\"请输入条码\",\"disabled\":_vm.confirmLoading,\"maxLength\":20},on:{\"blur\":function($event){return _vm.validateBarcode('power')}},model:{value:(_vm.localPower),callback:function ($$v) {_vm.localPower=$$v},expression:\"localPower\"}})],1)],1),_c('a-col',{attrs:{\"span\":24}},[_c('a-form-item',{attrs:{\"label\":\"主板条码\",\"validate-status\":_vm.boardBarcodeError ? 'error' : '',\"help\":_vm.boardBarcodeError || ''}},[_c('a-input',{attrs:{\"placeholder\":\"请输入条码\",\"disabled\":_vm.confirmLoading,\"maxLength\":20},on:{\"blur\":function($event){return _vm.validateBarcode('board')}},model:{value:(_vm.localBoard),callback:function ($$v) {_vm.localBoard=$$v},expression:\"localBoard\"}})],1)],1),_c('a-col',{attrs:{\"span\":24}},[_c('a-form-item',{attrs:{\"label\":\"资产条码\",\"validate-status\":_vm.wholeBarcodeError ? 'error' : '',\"help\":_vm.wholeBarcodeError || ''}},[_c('a-input',{attrs:{\"placeholder\":\"请输入条码\",\"disabled\":true,\"maxLength\":22},model:{value:(_vm.localWhole),callback:function ($$v) {_vm.localWhole=$$v},expression:\"localWhole\"}})],1)],1),_c('a-button',{attrs:{\"type\":\"primary\",\"html-type\":\"submit\",\"icon\":\"sync\",\"loading\":_vm.confirmLoading,\"block\":\"\"},on:{\"click\":_vm.handleUpdate}},[_vm._v(\" 更新 \")])],1)],1)],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <a-modal\r\n        title=\"更新组装关系\"\r\n        :visible=\"visible\"\r\n        :confirm-loading=\"confirmLoading\"\r\n        @ok=\"handleOk\"\r\n        @cancel=\"handleCancel\"\r\n        :footer=\"null\"\r\n    >\r\n      <a-spin :spinning=\"loading\">\r\n        <a-form :form=\"updateForm\" @submit=\"handleUpdate\" layout=\"vertical\">\r\n          <a-row :gutter=\"16\">\r\n            <a-col :span=\"24\">\r\n              <a-form-item\r\n                  label=\"电源板条码\"\r\n                  :validate-status=\"powerBarcodeError ? 'error' : ''\"\r\n                  :help=\"powerBarcodeError || ''\"\r\n              >\r\n                <a-input\r\n                    v-model=\"localPower\"\r\n                    placeholder=\"请输入条码\"\r\n                    :disabled=\"confirmLoading\"\r\n                    @blur=\"validateBarcode('power')\"\r\n                    :maxLength=\"20\"\r\n                >\r\n                </a-input>\r\n              </a-form-item>\r\n            </a-col>\r\n\r\n            <a-col :span=\"24\">\r\n              <a-form-item\r\n                  label=\"主板条码\"\r\n                  :validate-status=\"boardBarcodeError ? 'error' : ''\"\r\n                  :help=\"boardBarcodeError || ''\"\r\n              >\r\n                <a-input\r\n                    v-model=\"localBoard\"\r\n                    placeholder=\"请输入条码\"\r\n                    :disabled=\"confirmLoading\"\r\n                    @blur=\"validateBarcode('board')\"\r\n                    :maxLength=\"20\"\r\n                >\r\n                </a-input>\r\n              </a-form-item>\r\n            </a-col>\r\n\r\n            <a-col :span=\"24\">\r\n              <a-form-item\r\n                  label=\"资产条码\"\r\n                  :validate-status=\"wholeBarcodeError ? 'error' : ''\"\r\n                  :help=\"wholeBarcodeError || ''\"\r\n              >\r\n                <a-input\r\n                    v-model=\"localWhole\"\r\n                    placeholder=\"请输入条码\"\r\n                    :disabled=\"true\"\r\n                    :maxLength=\"22\"\r\n                >\r\n                </a-input>\r\n              </a-form-item>\r\n            </a-col>\r\n            <a-button\r\n                type=\"primary\"\r\n                html-type=\"submit\"\r\n                icon=\"sync\"\r\n                :loading=\"confirmLoading\"\r\n                block\r\n                @click=\"handleUpdate\"\r\n            >\r\n              更新\r\n            </a-button>\r\n          </a-row>\r\n        </a-form>\r\n      </a-spin>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { updateRelate } from \"@/api/api\";\r\nimport {notification} from 'ant-design-vue'\r\n\r\nexport default {\r\n  name: 'UpdateAssemblyRelate',\r\n  props: {\r\n    initialPowerCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    initialBoardCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    initialWholeCode: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      localPower: this.initialPowerCode,\r\n      localBoard: this.initialBoardCode,\r\n      localWhole: this.initialWholeCode,\r\n      updateForm: this.$form.createForm(this),\r\n      visible: false,\r\n      confirmLoading: false,\r\n      loading: false,\r\n      powerBarcodeError: '',\r\n      boardBarcodeError: '',\r\n      wholeBarcodeError: '',\r\n    };\r\n  },\r\n  created() {\r\n    this.$parent.$on('initial-power-updated', (newStatus) => {\r\n      this.localPower = newStatus;\r\n    });\r\n    this.$parent.$on('initial-board-updated', (newStatus) => {\r\n      this.localBoard = newStatus;\r\n    });\r\n    this.$parent.$on('initial-whole-updated', (newStatus) => {\r\n      this.localWhole = newStatus;\r\n    });\r\n  },\r\n  methods: {\r\n    validateBarcode(type) {\r\n      const code = type === 'power' ? this.localPower : this.localBoard;\r\n      const errorKey = `${type}BarcodeError`;\r\n\r\n      if (!code) {\r\n        this[errorKey] = '条码不能为空';\r\n      } else if (!/^\\d{4,20}$/.test(code)) {\r\n        this[errorKey] = '请输入4-20位纯数字';\r\n      } else {\r\n        this[errorKey] = '';\r\n      }\r\n    },\r\n    showModal() {\r\n      this.visible = true;\r\n    },\r\n    handleCancel() {\r\n      this.visible = false;\r\n      this.updateForm.resetFields();\r\n      this.powerBarcodeError = '';\r\n      this.boardBarcodeError = '';\r\n      this.wholeBarcodeError = '';\r\n    },\r\n    handleUpdate(e) {\r\n      e.preventDefault();\r\n      this.validateBarcode('power');\r\n      this.validateBarcode('board');\r\n\r\n      if (this.powerBarcodeError || this.boardBarcodeError) {\r\n        return;\r\n      }\r\n\r\n      this.confirmLoading = true;\r\n      updateRelate(this.localPower, this.localBoard, this.localWhole)\r\n          .then(res => {\r\n            if (res.error_code === 0) {\r\n              this.$message.success({\r\n                content: '更新成功',\r\n                duration: 3,\r\n              });\r\n              this.$emit('child-message', res.error_code);\r\n              this.handleCancel();\r\n            }\r\n          }).catch((error) => {\r\n              notification.error({\r\n                message: '系统提示',\r\n                description: error.response.data.message,\r\n                duration: 4\r\n              });\r\n          })\r\n          .finally(() => {\r\n            this.confirmLoading = false;\r\n          });\r\n    },\r\n    handleOk() {\r\n      this.updateForm.submit();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ant-form-item {\r\n  margin-bottom: 24px;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdateAssemblyRelate.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./UpdateAssemblyRelate.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./UpdateAssemblyRelate.vue?vue&type=template&id=72f86485&scoped=true\"\nimport script from \"./UpdateAssemblyRelate.vue?vue&type=script&lang=js\"\nexport * from \"./UpdateAssemblyRelate.vue?vue&type=script&lang=js\"\nimport style0 from \"./UpdateAssemblyRelate.vue?vue&type=style&index=0&id=72f86485&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72f86485\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"binding-container\">\r\n    <a-card class=\"binding-form\" :bordered=\"false\">\r\n      <a-form :form=\"form\" layout=\"vertical\">\r\n        <a-row :gutter=\"16\">\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"电源板条码：\">\r\n              <a-input\r\n                  v-model=\"formData.powerCode\"\r\n                  v-enter-next-input\r\n                  @blur=\"checkStatus('power')\"\r\n                  placeholder=\"请输入电源板条码\"\r\n                  :maxLength=\"20\"\r\n              />\r\n              <p v-if=\"errorMessage.power&&this.formData.powerCode!==''\" style=\"color: red;\">{{ errorMessage.power }}</p>\r\n              <span v-if=\"status.power\" :class=\"['status-text', status.power.type]\">\r\n                {{ status.power.message }}\r\n              </span>\r\n            </a-form-item>\r\n          </a-col>\r\n\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"主板条码：\">\r\n              <a-input\r\n                  v-model=\"formData.boardCode\"\r\n                  v-enter-next-input\r\n                  @blur=\"checkStatus('board')\"\r\n                  placeholder=\"请输入主板条码\"\r\n                  :maxLength=\"20\"\r\n              />\r\n              <p v-if=\"errorMessage.board&&this.formData.boardCode!==''\" style=\"color: red;\">{{ errorMessage.board }}</p>\r\n              <span v-if=\"status.board\" :class=\"['status-text', status.board.type]\">\r\n                {{ status.board.message }}\r\n              </span>\r\n            </a-form-item>\r\n          </a-col>\r\n\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"资产条码：\">\r\n              <a-input\r\n                  v-model=\"formData.wholeCode\"\r\n                  v-enter-next-input\r\n                  @blur=\"checkStatus('whole')\"\r\n                  placeholder=\"请输入资产条码\"\r\n                  :maxLength=\"22\"\r\n              />\r\n              <p v-if=\"errorMessage.whole&&this.formData.wholeCode!==''\" style=\"color: red;\">{{ errorMessage.whole }}</p>\r\n              <span v-if=\"status.whole\" :class=\"['status-text', status.whole.type]\">\r\n                {{ status.whole.message }}\r\n              </span>\r\n            </a-form-item>\r\n          </a-col>\r\n                <a-button style=\"display: flex;align-items: center;margin-top: 29px\" type=\"primary\" @click=\"handleSave\">添加</a-button>\r\n        </a-row>\r\n\r\n      </a-form>\r\n    </a-card>\r\n\r\n    <a-card class=\"search-section\" :bordered=\"false\">\r\n      <a-form layout=\"horizontal\" >\r\n        <a-row :gutter=\"16\" style=\"height: 100px;background-color: #f0f9eb;display: flex;align-items: center\">\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"电源板条码\">\r\n              <a-input\r\n                  v-enter-input\r\n                  v-model=\"searchForm.powerCode\"\r\n                  @blur=\"validateNumber('power')\"\r\n                  placeholder=\"请输入查询条码\"\r\n                  :maxLength=\"20\"\r\n              />\r\n              <p v-if=\"error.power&&this.searchForm.powerCode!==''\" style=\"color: red;\">{{ error.power }}</p>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"主板条码\">\r\n              <a-input\r\n                  v-enter-input\r\n                  v-model=\"searchForm.boardCode\"\r\n                  @blur=\"validateNumber('board')\"\r\n                  placeholder=\"请输入查询条码\"\r\n                  :maxLength=\"20\"\r\n              />\r\n              <p v-if=\"error.board&&this.searchForm.boardCode!==''\" style=\"color: red;\">{{ error.board }}</p>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"5\">\r\n            <a-form-item label=\"资产条码\">\r\n              <a-input\r\n                  v-enter-input\r\n                  v-model=\"searchForm.wholeCode\"\r\n                  @blur=\"validateNumber('whole')\"\r\n                  placeholder=\"请输入查询条码\"\r\n                  :maxLength=\"22\"\r\n              />\r\n              <p v-if=\"error.whole&&this.searchForm.wholeCode!==''\" style=\"color: red;\">{{ error.whole }}</p>\r\n            </a-form-item>\r\n          </a-col>\r\n          <a-col :span=\"1\">\r\n            <a-form-item label=\"操作\">\r\n              <a-button  type=\"primary\" html-type=\"submit\" icon=\"search\" @click=\"handleSearch\">查询</a-button>\r\n            </a-form-item>\r\n          </a-col>\r\n        </a-row>\r\n      </a-form>\r\n\r\n      <a-table\r\n          style=\"margin-top: 20px\"\r\n          rowKey=\"key\"\r\n          :columns=\"columns\"\r\n          :data-source=\"tableData\"\r\n          :pagination=\"detailIpagination\"\r\n          :rowClassName=\"(record, index) => (index % 2 === 1 ? 'table-row-light' : 'table-row-dark')\"\r\n          @change='detailhandleTableChange'\r\n      >\r\n        <template slot=\"operate\" slot-scope=\"text, record\">\r\n          <a-button\r\n              class=\"update-button\"\r\n              type=\"primary\"\r\n              icon=\"edit\"\r\n              @click=\"handleUpdate(record)\">\r\n            更新\r\n          </a-button>\r\n          <a-button style=\"margin-left: 20px\"\r\n                    type=\"primary\"\r\n                    icon=\"delete\"\r\n                    @click=\"handleDelete(record)\">\r\n            删除\r\n          </a-button>\r\n        </template>\r\n      </a-table>\r\n    </a-card>\r\n    <UpdateAssemblyRelate @child-message=\"handleChildMessage\" :initialPowerCode=\"power\" :initialBoardCode=\"board\" :initialWholeCode=\"whole\" ref=\"UpdateAssemblyRelate\"></UpdateAssemblyRelate>\r\n    <!-- Confirmation Modal -->\r\n    <a-modal\r\n        v-model=\"modal.visible\"\r\n        :title=\"modal.title\"\r\n        :okText=\"modal.okText\"\r\n        :cancelText=\"modal.cancelText\"\r\n        @ok=\"handleModalConfirm\"\r\n    >\r\n      <p>{{ modal.content }}</p>\r\n    </a-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addRelate, checkRelate, checkState, deleteAction} from \"@/api/api\";\r\nimport UpdateAssemblyRelate from \"@/components/UpdateAssemblyRelate.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    UpdateAssemblyRelate\r\n  },\r\n  data() {\r\n    return {\r\n      power: '',\r\n      board: '',\r\n      whole: '',\r\n      error:{\r\n        power: '',\r\n        board: '',\r\n        whole: ''\r\n      },\r\n      errorMessage: {\r\n        power: '',\r\n        board: '',\r\n        whole: '',\r\n      },\r\n      detailIpagination:{\r\n        current: 1,\r\n        pageSize: 10,\r\n        pageSizeOptions: ['10','20','50','100'],\r\n        showTotal: (total, range) => {\r\n          return range[0] + \"-\" + range[1] + \" 共\" + total + \"条\"\r\n        },\r\n        showQuickJumper: true,\r\n        showSizeChanger: true,\r\n        total: 0\r\n      },\r\n      form: this.$form.createForm(this),\r\n      formData: {\r\n        powerCode: '',\r\n        boardCode: '',\r\n        wholeCode: ''\r\n      },\r\n      status: {\r\n        power: null,\r\n        board: null,\r\n        whole: null\r\n      },\r\n      searchForm: {\r\n        powerCode: '',\r\n        boardCode: '',\r\n        wholeCode: ''\r\n      },\r\n      columns: [\r\n        { title: '电源板条码', dataIndex: 'powerCode', key: 'powerCode' },\r\n        { title: '主板条码', dataIndex: 'boardCode', key: 'boardCode' },\r\n        { title: '资产条码', dataIndex: 'wholeCode', key: 'wholeCode' },\r\n        {\r\n          title: '操作',\r\n          key: 'operate',\r\n          scopedSlots: { customRender: 'operate' }\r\n        }\r\n      ],\r\n      tableData: [],\r\n      modal: {\r\n        visible: false,\r\n        title: '',\r\n        content: '',\r\n        okText: '确定',\r\n        cancelText: '取消',\r\n        callback: null\r\n      },\r\n      isEditing: false,\r\n      editingRecord: null,\r\n      result:{\r\n        power: null,\r\n        board: null,\r\n        whole: null\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleSearch()\r\n  },\r\n  methods: {\r\n    detailhandleTableChange(pagination) {\r\n      this.detailIpagination = pagination\r\n      this.handleSearch()\r\n    },\r\n    handleChildMessage(message){\r\n      if(message===0){\r\n        this.fetchData()\r\n      }\r\n    },\r\n    fetchData(){\r\n      this.handleSearch()\r\n    },\r\n    // 校验是否为数字的校验函数\r\n    validateNumber(type) {\r\n      const code = this.searchForm[`${type}Code`];\r\n      if (!code) {\r\n        this.error[type] = '';\r\n        return;\r\n      }\r\n      if (type === 'whole') {\r\n        if (!/^\\d{22}$/.test(code)) {\r\n          this.error[type] = '请输入22位数字';\r\n        } else {\r\n          this.error[type] = '';\r\n        }\r\n      } else {\r\n        if (!/^\\d{4,20}$/.test(code)) {\r\n          this.error[type] = '请输入4 ~ 20位数字';\r\n        } else {\r\n          this.error[type] = '';\r\n        }\r\n      }\r\n    },\r\n    async checkStatus(type) {\r\n      const code = this.formData[`${type}Code`]\r\n      if (!code) {\r\n        this.errorMessage[type] = '请输入内容';\r\n        return;\r\n      }\r\n      if(type==='whole'){\r\n        if (!/^\\d{22}$/.test(code)) {\r\n          this.status[type] =''\r\n          this.errorMessage[type] = '请输入22位数字';\r\n        } else {\r\n          this.errorMessage[type] = '';\r\n          try {\r\n            checkState(code,type).then(res=>{\r\n              console.log(res)\r\n              this.result[type]=res.detail\r\n              this.status[type]={\r\n                type:res.detail==='已组装' ? 'error' : 'success',\r\n                message:res.detail\r\n              }\r\n            })\r\n          } catch (error) {\r\n            this.status[type] = {\r\n              type: 'error',\r\n              message: '查询失败'\r\n            }\r\n          }\r\n        }\r\n      }else {\r\n        if (!/^\\d{4,20}$/.test(code)) {\r\n          this.status[type] =''\r\n          this.errorMessage[type] = '请输入4 ~ 20位数字';\r\n        } else {\r\n          this.errorMessage[type] = '';\r\n          try {\r\n            checkState(code,type).then(res=>{\r\n              console.log(res)\r\n              this.result[type]=res.detail\r\n              this.status[type]={\r\n                type:res.result===true ? 'success' : 'error',\r\n                message:res.detail\r\n              }\r\n            })\r\n          } catch (error) {\r\n            this.status[type] = {\r\n              type: 'error',\r\n              message: '查询失败'\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    async handleSave() {\r\n      // Validate all inputs before saving\r\n      this.checkStatus('power');\r\n      this.checkStatus('board');\r\n      this.checkStatus('whole');\r\n\r\n      // Check if there are any errors\r\n      const hasErrors = Object.values(this.errorMessage).some(error => error !== '');\r\n      if (hasErrors) {\r\n        this.$message.error('请输入条码并且修改输入错误后再保存');\r\n        return;\r\n      }\r\n\r\n      // Check if there are any abnormal statuses\r\n      const hasAbnormalStatus = Object.values(this.status).some(\r\n          status => status && status.type === 'error'\r\n      );\r\n      if (hasAbnormalStatus) {\r\n        if (this.result.power === '已组装' || this.result.whole === '已组装' || this.result.board === '已组装') {\r\n          this.$message.error('存在已组装的设备，无法保存');\r\n        } else {\r\n          // this.showModal({\r\n          //   title: '警告',\r\n          //   content: '存在异常状态，是否继续保存？',\r\n          //   callback: this.saveData\r\n          // });\r\n          this.saveData();\r\n        }\r\n      } else {\r\n        this.saveData();\r\n      }\r\n    },\r\n\r\n    async saveData() {\r\n      try {\r\n        addRelate(this.formData.powerCode,this.formData.boardCode,this.formData.wholeCode).then(res=>{\r\n          console.log(res)\r\n          if(res.error_code===0){\r\n            this.$message.success( '保存成功')\r\n            this.resetForm()\r\n            this.tableData = []\r\n            checkRelate(this.formData.powerCode,this.formData.boardCode,this.formData.wholeCode,this.detailIpagination.current,this.detailIpagination.pageSize).then(res=>{\r\n              this.detailIpagination.total=res.page_count*this.detailIpagination.pageSize\r\n              console.log(res)\r\n              let i=0\r\n              res.records.forEach(item=>{\r\n                this.tableData.push({\r\n                  key:i++,\r\n                  powerCode: item.powerBarcode,\r\n                  boardCode: item.boardBarcode,\r\n                  wholeCode: item.wholeBarcode\r\n                })\r\n              })\r\n            })\r\n          }\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    async handleSearch() {\r\n      // Validate all inputs before searching\r\n      this.validateNumber('power');\r\n      this.validateNumber('board');\r\n      this.validateNumber('whole');\r\n\r\n      // Check if there are any errors\r\n      const hasErrors = Object.values(this.error).some(error => error !== '');\r\n      if (hasErrors) {\r\n        // this.$message.error('请修正输入错误后再查询');\r\n        return;\r\n      }\r\n\r\n      this.tableData = [];\r\n      try {\r\n        const res = await checkRelate(\r\n            this.searchForm.powerCode,\r\n            this.searchForm.boardCode,\r\n            this.searchForm.wholeCode,\r\n            this.detailIpagination.current,\r\n            this.detailIpagination.pageSize\r\n        );\r\n        this.detailIpagination.total = res.page_count * this.detailIpagination.pageSize;\r\n        let i = 0;\r\n        res.records.forEach(item => {\r\n          this.tableData.push({\r\n            key: i++,\r\n            powerCode: item.powerBarcode,\r\n            boardCode: item.boardBarcode,\r\n            wholeCode: item.wholeBarcode\r\n          });\r\n        });\r\n      } catch (error) {\r\n        this.$message.error('查询失败');\r\n      }\r\n    },\r\n    handleUpdate(record) {\r\n      try {\r\n        this.power=record.powerCode\r\n        this.board=record.boardCode\r\n        this.whole=record.wholeCode\r\n        console.log(this.power)\r\n        this.$emit('initial-power-updated', this.power); // 触发事件\r\n        this.$emit('initial-board-updated', this.board); // 触发事件\r\n        this.$emit('initial-whole-updated', this.whole); // 触发事件\r\n        this.$refs.UpdateAssemblyRelate.title = \"更新组装关系\"\r\n        this.$refs.UpdateAssemblyRelate.visible = true;\r\n\r\n      } catch (error) {\r\n        this.$message.error('更新失败')\r\n      }\r\n    },\r\n\r\n    handleDelete(record) {\r\n      this.showModal({\r\n        title: '确认删除',\r\n        content: '是否确认删除该记录？',\r\n        callback: () => this.deleteRecord(record)\r\n      })\r\n    },\r\n\r\n    async deleteRecord(record) {\r\n      try {\r\n        const data = {\r\n          \"Barcode\": record.wholeCode\r\n        };\r\n\r\n        deleteAction(data).then(res=>{\r\n          console.log(res)\r\n          this.tableData = this.tableData.filter(item => item.key !== record.key)\r\n          this.$message.success('删除成功')\r\n          this.handleSearch()\r\n\r\n        })\r\n      } catch (error) {\r\n        this.$message.error('删除失败')\r\n      }\r\n    },\r\n\r\n    showModal({ title, content, callback }) {\r\n      this.modal = {\r\n        ...this.modal,\r\n        visible: true,\r\n        title,\r\n        content,\r\n        callback\r\n      }\r\n    },\r\n\r\n    handleModalConfirm() {\r\n      this.modal.visible = false\r\n      if (this.modal.callback) {\r\n        this.modal.callback()\r\n      }\r\n    },\r\n\r\n    resetForm() {\r\n      this.formData = {\r\n        powerCode: '',\r\n        boardCode: '',\r\n        wholeCode: ''\r\n      }\r\n      this.status = {\r\n        power: null,\r\n        board: null,\r\n        whole: null\r\n      }\r\n      this.isEditing = false\r\n      this.editingRecord = null\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.binding-container {\r\n  padding: 24px;\r\n}\r\n\r\n.binding-form {\r\n  background: #FFFFE0;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.search-section {\r\n  background: #fff;\r\n}\r\n\r\n.form-header {\r\n  font-size: 14px;\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n}\r\n\r\n.arrow {\r\n  color: #666;\r\n  margin-right: 5px;\r\n}\r\n\r\n.status-text {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.status-text.success {\r\n  color: #52c41a;\r\n}\r\n\r\n.status-text.error {\r\n  color: #f5222d;\r\n}\r\n\r\n.table-row-light {\r\n  background-color: #f5f0ff;\r\n}\r\n\r\n.table-row-dark {\r\n  background-color: #fff;\r\n}\r\n\r\n.delete-button {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.delete-button:hover {\r\n  color: #666;\r\n}\r\n\r\n:deep(.ant-card-body) {\r\n  padding: 24px;\r\n}\r\n\r\n:deep(.ant-form-item) {\r\n  margin-bottom: 16px;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./CompleteMachineAssembly.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./CompleteMachineAssembly.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./CompleteMachineAssembly.vue?vue&type=template&id=1d6a1794&scoped=true\"\nimport script from \"./CompleteMachineAssembly.vue?vue&type=script&lang=js\"\nexport * from \"./CompleteMachineAssembly.vue?vue&type=script&lang=js\"\nimport style0 from \"./CompleteMachineAssembly.vue?vue&type=style&index=0&id=1d6a1794&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d6a1794\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"code-replacement-container\"},[_c('a-form',{attrs:{\"form\":_vm.form},on:{\"submit\":_vm.handleSubmit}},[_c('div',{staticClass:\"form-header\"},[_c('div',{staticClass:\"input-group\"},[_c('span',[_vm._v(\"原资产编码：\")]),_c('a-input',{ref:\"oldCodeInput\",attrs:{\"maxLength\":22,\"placeholder\":\"请扫描原资产编码\"},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleOldCodeChange.apply(null, arguments)}},model:{value:(_vm.oldCode),callback:function ($$v) {_vm.oldCode=$$v},expression:\"oldCode\"}})],1),_c('div',{staticClass:\"input-group\"},[_c('span',[_vm._v(\"更换资产编码：\")]),_c('a-input',{ref:\"newCodeInput\",attrs:{\"maxLength\":22,\"placeholder\":\"请扫描新资产编码\",\"disabled\":!_vm.oldCodeValid},on:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.handleNewCodeChange.apply(null, arguments)}},model:{value:(_vm.newCode),callback:function ($$v) {_vm.newCode=$$v},expression:\"newCode\"}})],1),_c('a-button',{attrs:{\"type\":\"primary\",\"disabled\":!_vm.isSubmitEnabled},on:{\"click\":_vm.handleSubmit}},[_vm._v(\" 确定 \")])],1),_c('div',{staticClass:\"info-display\"},[_c('div',{staticClass:\"info-box\"},[_c('h3',[_vm._v(\"待更换资产\")]),(_vm.oldAssetInfo)?_c('div',[_c('p',[_vm._v(\"资产编码: \"+_vm._s(_vm.oldAssetInfo.barcode))]),_c('p',[_vm._v(\"通讯地址: \"+_vm._s(_vm.oldAssetInfo.address))]),_c('p',[_vm._v(\"表号: \"+_vm._s(_vm.oldAssetInfo.meter))]),_c('p',[_vm._v(\"硬件版本: \"+_vm._s(_vm.oldAssetInfo.hardVersion))]),_c('p',[_vm._v(\"硬件日期: \"+_vm._s(_vm.oldAssetInfo.hardDate))]),_c('p',[_vm._v(\"生产日期: \"+_vm._s(_vm.oldAssetInfo.produceDate))]),_c('p',[_vm._v(\"厂商编码: \"+_vm._s(_vm.oldAssetInfo.vendorCode))]),_c('p',[_vm._v(\"厂商扩展: \"+_vm._s(_vm.oldAssetInfo.vendorExt))]),_c('p',[_vm._v(\"安全模式: \"+_vm._s(_vm.oldAssetInfo.mode))])]):_vm._e()]),_c('div',{staticClass:\"info-box\"},[_c('h3',[_vm._v(\"更换资产信息\")]),(_vm.newAssetInfo)?_c('div',[_c('p',[_vm._v(\"资产编码: \"+_vm._s(_vm.newAssetInfo.barcode))]),_c('p',[_vm._v(\"通讯地址: \"+_vm._s(_vm.newAssetInfo.address))]),_c('p',[_vm._v(\"表号: \"+_vm._s(_vm.newAssetInfo.meter))]),_c('p',[_vm._v(\"硬件版本: \"+_vm._s(_vm.newAssetInfo.hardVersion))]),_c('p',[_vm._v(\"硬件日期: \"+_vm._s(_vm.newAssetInfo.hardDate))]),_c('p',[_vm._v(\"生产日期: \"+_vm._s(_vm.newAssetInfo.produceDate))]),_c('p',[_vm._v(\"厂商编码: \"+_vm._s(_vm.newAssetInfo.vendorCode))]),_c('p',[_vm._v(\"厂商扩展: \"+_vm._s(_vm.newAssetInfo.vendorExt))]),_c('p',[_vm._v(\"安全模式: \"+_vm._s(_vm.newAssetInfo.mode))])]):_vm._e()])]),_c('a-modal',{attrs:{\"visible\":_vm.loading,\"closable\":false,\"maskClosable\":false,\"footer\":null,\"centered\":\"\"}},[_c('div',{staticClass:\"loading-content\"},[_c('a-spin'),_c('p',[_vm._v(\"正在执行更换操作，预计需要50秒...\")]),_c('p',[_vm._v(\"剩余时间：\"+_vm._s(_vm.remainingTime)+\"秒\")])],1)])],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"code-replacement-container\">\r\n    <a-form :form=\"form\" @submit=\"handleSubmit\">\r\n      <div class=\"form-header\">\r\n        <div class=\"input-group\">\r\n          <span>原资产编码：</span>\r\n          <a-input\r\n              ref=\"oldCodeInput\"\r\n              v-model=\"oldCode\"\r\n              @keyup.enter=\"handleOldCodeChange\"\r\n              :maxLength=\"22\"\r\n              placeholder=\"请扫描原资产编码\"\r\n          />\r\n        </div>\r\n        <div class=\"input-group\">\r\n          <span>更换资产编码：</span>\r\n          <a-input\r\n              ref=\"newCodeInput\"\r\n              v-model=\"newCode\"\r\n              @keyup.enter=\"handleNewCodeChange\"\r\n              :maxLength=\"22\"\r\n              placeholder=\"请扫描新资产编码\"\r\n              :disabled=\"!oldCodeValid\"\r\n          />\r\n        </div>\r\n        <a-button\r\n            type=\"primary\"\r\n            @click=\"handleSubmit\"\r\n            :disabled=\"!isSubmitEnabled\"\r\n        >\r\n          确定\r\n        </a-button>\r\n      </div>\r\n      <div class=\"info-display\">\r\n        <div class=\"info-box\">\r\n          <h3>待更换资产</h3>\r\n          <div v-if=\"oldAssetInfo\">\r\n            <p>资产编码: {{ oldAssetInfo.barcode }}</p>\r\n            <p>通讯地址: {{ oldAssetInfo.address }}</p>\r\n            <p>表号: {{ oldAssetInfo.meter }}</p>\r\n            <p>硬件版本: {{ oldAssetInfo.hardVersion }}</p>\r\n            <p>硬件日期: {{ oldAssetInfo.hardDate }}</p>\r\n            <p>生产日期: {{ oldAssetInfo.produceDate }}</p>\r\n            <p>厂商编码: {{ oldAssetInfo.vendorCode }}</p>\r\n            <p>厂商扩展: {{ oldAssetInfo.vendorExt }}</p>\r\n            <p>安全模式: {{ oldAssetInfo.mode }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"info-box\">\r\n          <h3>更换资产信息</h3>\r\n          <div v-if=\"newAssetInfo\">\r\n            <p>资产编码: {{ newAssetInfo.barcode }}</p>\r\n            <p>通讯地址: {{ newAssetInfo.address }}</p>\r\n            <p>表号: {{ newAssetInfo.meter }}</p>\r\n            <p>硬件版本: {{ newAssetInfo.hardVersion }}</p>\r\n            <p>硬件日期: {{ newAssetInfo.hardDate }}</p>\r\n            <p>生产日期: {{ newAssetInfo.produceDate }}</p>\r\n            <p>厂商编码: {{ newAssetInfo.vendorCode }}</p>\r\n            <p>厂商扩展: {{ newAssetInfo.vendorExt }}</p>\r\n            <p>安全模式: {{ newAssetInfo.mode }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <a-modal\r\n          :visible=\"loading\"\r\n          :closable=\"false\"\r\n          :maskClosable=\"false\"\r\n          :footer=\"null\"\r\n          centered\r\n      >\r\n        <div class=\"loading-content\">\r\n          <a-spin />\r\n          <p>正在执行更换操作，预计需要50秒...</p>\r\n          <p>剩余时间：{{ remainingTime }}秒</p>\r\n        </div>\r\n      </a-modal>\r\n    </a-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { checkAsset, updateAsset } from \"@/api/api\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: this.$form.createForm(this),\r\n      oldCode: '',\r\n      newCode: '',\r\n      oldCodeValid: false,\r\n      loading: false,\r\n      remainingTime: 60,\r\n      timer: null,\r\n      oldAssetInfo: null,\r\n      newAssetInfo: null\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isSubmitEnabled() {\r\n      return this.oldCodeValid &&\r\n          this.newCode &&\r\n          this.oldAssetInfo &&\r\n          this.newAssetInfo &&\r\n          !this.loading\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    validateAssetCode(code) {\r\n      return /^\\d{22}$/.test(code)\r\n    },\r\n\r\n    async handleOldCodeChange() {\r\n      if (!this.validateAssetCode(this.oldCode)) {\r\n        this.$message.error('请输入22位的纯数字')\r\n        this.oldCodeValid = false\r\n        return\r\n      }\r\n\r\n      try {\r\n        const response = await this.fetchAssetInfo(this.oldCode)\r\n        if (response.isUsed) {\r\n          this.oldAssetInfo = response\r\n          this.oldCodeValid = true\r\n          this.$nextTick(() => {\r\n            this.$refs.newCodeInput.focus()\r\n          })\r\n        } else {\r\n          this.$message.error('原资产编码未使用')\r\n          this.oldCodeValid = false\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取资产信息失败')\r\n        this.oldCodeValid = false\r\n      }\r\n    },\r\n\r\n    async handleNewCodeChange() {\r\n      if (!this.validateAssetCode(this.newCode)) {\r\n        this.$message.error('请输入22位的纯数字')\r\n        return\r\n      }\r\n\r\n      try {\r\n        const response = await this.fetchAssetInfo(this.newCode)\r\n        if (!response.isUsed) {\r\n          this.newAssetInfo = response\r\n        } else {\r\n          this.$message.error('新资产编码已被使用')\r\n          this.newAssetInfo = null\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('获取资产信息失败')\r\n        this.newAssetInfo = null\r\n      }\r\n    },\r\n\r\n    async handleSubmit(e) {\r\n      if (e) e.preventDefault()\r\n      if (!this.isSubmitEnabled) return\r\n\r\n      this.loading = true\r\n      this.remainingTime = 60\r\n\r\n      this.timer = setInterval(() => {\r\n        this.remainingTime--\r\n        if (this.remainingTime <= 0) {\r\n          clearInterval(this.timer)\r\n          this.loading = false\r\n          this.$message.error('操作超时')\r\n        }\r\n      }, 1000)\r\n\r\n      try {\r\n        const response = await this.replaceAssetCode({\r\n          oldCode: this.oldCode,\r\n          newCode: this.newCode\r\n        })\r\n        clearInterval(this.timer)\r\n        this.loading = false\r\n        if (response.success) {\r\n          this.$message.success('更换成功')\r\n          this.resetForm()\r\n        } else {\r\n          this.$message.error(response.message || '更换失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('更换资产编码时出现错误：', error)\r\n        clearInterval(this.timer)\r\n        this.loading = false\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n\r\n    resetForm() {\r\n      this.oldCode = ''\r\n      this.newCode = ''\r\n      this.oldCodeValid = false\r\n      this.oldAssetInfo = null\r\n      this.newAssetInfo = null\r\n      this.$nextTick(() => {\r\n        this.$refs.oldCodeInput.focus()\r\n      })\r\n    },\r\n\r\n    async fetchAssetInfo(code) {\r\n      try {\r\n        const res = await checkAsset(code);\r\n        console.log(res)\r\n        await new Promise((resolve) => {\r\n          setTimeout(() => {\r\n            resolve();\r\n          }, 500);\r\n        });\r\n        return {\r\n          barcode: res.barcode,\r\n          address: res.address,\r\n          meter: res.meter,\r\n          hardVersion: res.hardVersion,\r\n          hardDate: res.hardDate,\r\n          produceDate: res.produceDate,\r\n          vendorCode: res.vendorCode,\r\n          vendorExt: res.vendorExt,\r\n          mode: res.mode,\r\n          isUsed: res.used\r\n        };\r\n      } catch (error) {\r\n        console.error('检查资产时出现错误：', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async replaceAssetCode(data) {\r\n      try {\r\n        const res = await updateAsset(data.oldCode, data.newCode)\r\n        console.log(res)\r\n        if (res.error_code === 0) {\r\n          return { success: true }\r\n        } else {\r\n          return { success: false, message: res.message || '更换失败' }\r\n        }\r\n      } catch (error) {\r\n        console.error('更换资产编码时出现错误：', error)\r\n        throw error\r\n      }\r\n    }\r\n  },\r\n\r\n  beforeDestroy() {\r\n    if (this.timer) {\r\n      clearInterval(this.timer)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.code-replacement-container {\r\n  padding: 24px;\r\n  background: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-header {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n  align-items: flex-start;\r\n}\r\n\r\n.input-group {\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.input-group span {\r\n  white-space: nowrap;\r\n}\r\n\r\n.info-display {\r\n  display: flex;\r\n  gap: 24px;\r\n}\r\n\r\n.info-box {\r\n  flex: 1;\r\n  padding: 16px;\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n}\r\n\r\n.info-box h3 {\r\n  margin-bottom: 16px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.info-box p {\r\n  margin-bottom: 8px;\r\n  color: #666;\r\n}\r\n\r\n.loading-content {\r\n  text-align: center;\r\n  padding: 24px;\r\n}\r\n\r\n.loading-content p {\r\n  margin-top: 16px;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ChangeAssetCoding.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./ChangeAssetCoding.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./ChangeAssetCoding.vue?vue&type=template&id=3d07d92b&scoped=true\"\nimport script from \"./ChangeAssetCoding.vue?vue&type=script&lang=js\"\nexport * from \"./ChangeAssetCoding.vue?vue&type=script&lang=js\"\nimport style0 from \"./ChangeAssetCoding.vue?vue&type=style&index=0&id=3d07d92b&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3d07d92b\",\n  null\n  \n)\n\nexport default component.exports", "//引入VueRouter\r\nimport Vue from 'vue';\r\nimport Router from 'vue-router';\r\n//引入Luyou 组件\r\nimport Home from '../views/Home.vue'\r\nimport BoardLevelTest from '../views/content/BoardLevelTest.vue'\r\nimport DataManage from '../views/content/DataManage.vue'\r\nimport HomePage from '../views/content/HomePage.vue'\r\nimport PowerBoardTest from '../views/content/PowerBoardTest.vue'\r\nimport SystemSetting from '../views/content/SystemSetting.vue'\r\nimport WholeMachineReTest from '../views/content/WholeMachineReTest.vue'\r\nimport WholeMachineTest from '../views/content/WholeMachineTest.vue'\r\nimport WriteNumberConfiguration from '../views/content/WriteNumberConfiguration.vue'\r\nimport CompleteMachineAssembly from '../views/content/CompleteMachineAssembly.vue'\r\nimport ChangeAssetCoding from '../views/content/ChangeAssetCoding.vue'\r\nVue.use(Router);\r\n\r\n//创建router实例对象，去管理一组一组的路由规则\r\nconst router = new Router({\r\n    mode: 'history',\r\n    base:'/work/',\r\n    routes:[\r\n        {\r\n            path:'/',\r\n            redirect:'/DataManage',\r\n            name:'数据管理',\r\n            component:DataManage\r\n        },\r\n        {\r\n            path:'/Home',\r\n            component:Home,\r\n            name:'主页',\r\n            children:[ //通过children配置子级路由\r\n                {\r\n                    path:'/BoardLevelTest',\r\n                    name:'板级测试',\r\n                    component:BoardLevelTest\r\n                },\r\n                {\r\n                    path:'/DataManage',\r\n                    name:'数据管理',\r\n                    component:DataManage\r\n                },\r\n                {\r\n                    path:'/HomePage',\r\n                    name:'首页',\r\n                    component:HomePage\r\n                },\r\n                {\r\n                    path:'/PowerBoardTest',\r\n                    name:'电源板测试',\r\n                    component:PowerBoardTest\r\n                }\r\n                ,\r\n                {\r\n                    path:'/SystemSetting',\r\n                    name:'系统设置',\r\n                    component:SystemSetting\r\n                },\r\n                {\r\n                    path:'/WholeMachineReTest',\r\n                    name:'整机复测',\r\n                    component:WholeMachineReTest\r\n                }\r\n                ,\r\n                {\r\n                    path:'/WholeMachineTest',\r\n                    name:'整机测试',\r\n                    component:WholeMachineTest\r\n                }\r\n                ,\r\n                {\r\n                    path:'/WriteNumberConfiguration',\r\n                    name:'写号配置',\r\n                    component:WriteNumberConfiguration\r\n                }\r\n                ,\r\n                {\r\n                    path:'/CompleteMachineAssembly',\r\n                    name:'整机组装',\r\n                    component:CompleteMachineAssembly\r\n                }\r\n                ,\r\n                {\r\n                    path:'/ChangeAssetCoding',\r\n                    name:'更换资产编码',\r\n                    component:ChangeAssetCoding\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n})\r\n\r\n//暴露router\r\nexport default router", "import Vue from 'vue';\r\n\r\n// 自定义指令，实现按下enter后，光标自动跳转下一个输入框\r\n\r\n\r\nVue.directive('enterNextInput', {\r\n    inserted: function (el) {\r\n        el.addEventListener(\"keypress\", function (e) {\r\n            window.isDirectiveEnter = false;\r\n            e = e || window.event;\r\n            let charcode = typeof e.charCode === 'number'? e.charCode : e.keyCode;\r\n            if (charcode === 13) {\r\n                e.preventDefault(); // 阻止回车键默认行为\r\n                var currentInput = document.activeElement;\r\n                if (currentInput) {\r\n                    var keyupEvent = new Event('keyup');\r\n                    keyupEvent.keyCode = 13;\r\n                    currentInput.dispatchEvent(keyupEvent);\r\n                }\r\n                window.isDirectiveEnter = true; // 设置标志位，表示是指令触发的焦点转移\r\n                var dom = document.getElementsByTagName(\"input\");\r\n                for (var i = 0; i < dom.length; i++) {\r\n                    if (dom[i] === document.activeElement) {\r\n                        if (i === dom.length - 1) {\r\n                            return;\r\n                        }\r\n                        dom[i + 1].focus();\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n});\r\n\r\nVue.directive('enterInput', {\r\n    inserted: function (el) {\r\n        el.addEventListener(\"keypress\", function (e) {\r\n            e = e || window.event;\r\n            let charcode = typeof e.charCode === 'number'? e.charCode : e.keyCode;\r\n            if (charcode === 13) {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                var dom = document.getElementsByTagName(\"input\");\r\n                for (var i = 0; i < dom.length; i++) {\r\n                    if (dom[i] === document.activeElement) {\r\n                        if (i === dom.length - 1) {\r\n                            return;\r\n                        }\r\n                        dom[i + 1].focus();\r\n                        return;\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n});\r\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport Antd from 'ant-design-vue';\nimport 'ant-design-vue/dist/antd.less';\nimport router from './router/router';\nimport './utils/enterNextInput'\n\n\nexport const EventBus = new Vue()\n// import service from './utils/api';\n//\n// Vue.prototype.$axios = service;\n// Vue.prototype.$backendAddress = 'http://**************:9080'; // 初始后端地址\n//\n// Vue.prototype.$updateBackendAddress = function (newAddress) {\n//   this.$backendAddress = newAddress;\n//   this.$axios.defaults.baseURL = newAddress;\n// };\n\n\nVue.use(Antd); // 注册Ant Design Vue组件库\n\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  render: h => h(App),\n}).$mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkant_design_vue\"] = self[\"webpackChunkant_design_vue\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(55927); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["map", "webpackContext", "req", "id", "webpackContextResolve", "__webpack_require__", "o", "e", "Error", "code", "keys", "Object", "resolve", "module", "exports", "render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "name", "components", "mounted", "updateTitle", "methods", "document", "title", "component", "staticClass", "_v", "on", "showModal", "staticStyle", "_l", "navItems", "item", "index", "key", "route", "icon", "_s", "modalVisible", "handleOk", "handleCancel", "backendName", "backendVersion", "currentHost", "window", "location", "host", "ip", "port", "split", "defaultBackendAddress", "service", "axios", "create", "baseURL", "JSON", "parse", "localStorage", "getItem", "timeout", "interceptors", "request", "use", "config", "error", "console", "log", "Promise", "reject", "response", "data", "undefined", "status", "errorMessage", "message", "notification", "description", "duration", "getInfo", "url", "method", "checkTest", "barcode", "result", "count", "page", "params", "addTest", "updateTest", "checkState", "type", "checkRelate", "powerBarcode", "boardBarcode", "wholeBarcode", "addRelate", "updateRelate", "headers", "deleteAction", "checkNumber", "address", "meter", "hardVersion", "hardDate", "produceDate", "vendorCode", "vendorExt", "addNumber", "mode", "updateNumber", "dataManage", "start", "end", "download", "async", "exportData", "planType", "downloadUrl", "responseType", "blob", "Blob", "link", "createElement", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "readParameter", "setParameter", "softVersion", "promises", "all", "getTestItems", "checkAsset", "updateAsset", "newBarcode", "computed", "currentRoute", "$route", "fetchData", "EventBus", "$on", "newInfo", "version", "then", "res", "columns", "testData", "record", "x", "scopedSlots", "_u", "fn", "text", "directives", "rawName", "class", "getBarcodeInputClass", "isTesting", "$event", "indexOf", "_k", "keyCode", "handleBarcodeSubmit", "model", "value", "callback", "$$v", "$set", "expression", "device", "testStatus", "time", "testItems", "getStatusColor", "getStatusText", "dataIndex", "width", "customRender", "websockets", "timers", "workbenchAddresses", "socketMap", "submissionStatus", "created", "storedBackendUrl", "for<PERSON>ach", "push", "validateBarcode", "regex", "test", "updateSubmissionStatus", "setTimeout", "isDirectiveEnter", "warning", "socket", "indexFind", "findIndex", "socketObj", "readyState", "WebSocket", "OPEN", "send", "stringify", "colorMap", "success", "fail", "testing", "waiting", "textMap", "initWebSocket", "timeoutId", "currentTimeout", "increaseTimeoutFactor", "hasSentMessage", "checkConnection", "close", "warn", "items", "clearTimeout", "onopen", "onerror", "onclose", "onmessage", "handleWebSocketMessage", "event", "error_code", "updateTestDataInfo", "updateTestStatus", "updateTestItemStatus", "handleBarcodeResponse", "find", "devices", "$forceUpdate", "newDataArr", "dataObj", "testItem", "concat", "startTimer", "stopTimer", "targetData", "seconds", "setInterval", "formatTime", "clearInterval", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON>", "form", "handleQuery", "handlePlanTypeChange", "rules", "validator", "locale", "handleDateRangeChange", "initialValue", "handleReset", "slot", "exportToExcel", "loading", "pagination", "handleTableChange", "powerItem", "boardItem", "wholeItem", "rewholeItem", "lang", "placeholder", "yearPlaceholder", "monthPlaceholder", "dayPlaceholder", "weekPlaceholder", "rangePlaceholder", "today", "now", "ok", "clear", "prevYear", "nextYear", "prevMonth", "nextMonth", "monthSelect", "yearSelect", "decadeSelect", "yearFormat", "monthFormat", "dateFormat", "dayFormat", "dateTimeFormat", "timeFormat", "secondFormat", "meridiem", "am", "pm", "timePickerLocale", "$form", "createForm", "current", "pageSize", "pageSizeOptions", "showTotal", "total", "range", "showQuickJumper", "showSizeChanger", "startTime", "endTime", "excelData", "currentPlanType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validateFields", "force", "rule", "numberRegex", "length", "dates", "startOf", "unix", "endOf", "preventDefault", "err", "values", "resetFields", "$message", "catch", "page_count", "records", "finally", "colors", "hasEmpty", "allQualified", "texts", "getFieldsValue", "style", "background", "padding", "minHeight", "fixtureColumns", "fixtureData", "onlineFixtures", "proxy", "testingModules", "testColumns", "progress", "recentResults", "backgroundColor", "passed", "currentModule", "completedTests", "moduleId", "handleSubmit", "required", "validateNumber", "validateCount", "valuePropName", "batchSave", "queryForm", "queryResults", "detailIpagination", "detailhandleTableChange", "_e", "updateData", "ref", "isStatus", "handleChildMessage", "visible", "confirmLoading", "updateForm", "handleUpdate", "localStatus", "props", "initialCode", "String", "initialStatus", "Boolean", "localBarcode", "barcodeError", "$parent", "newStatus", "$emit", "content", "submit", "UpdatePowerBoardTest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryCode", "queryStatus", "decimalValue", "Number", "isNaN", "numValue", "startCode", "quantity", "$refs", "backend<PERSON><PERSON><PERSON>", "handleSyncAddress", "board", "addIP", "boardIPs", "removeIP", "indeterminateBoard", "checkAllBoard", "onCheckAllBoardChange", "onBoardChange", "selectedBoard", "boardTestItems", "system", "systemIPs", "indeterminateSystem", "checkAllSystem", "onCheckAllSystemChange", "onSystemChange", "selectedSystem", "systemTestItems", "softwareVersion", "measurement", "measurementIPs", "indeterminateMeasurement", "checkAllMeasurement", "onCheckAllMeasurementChange", "onMeasurementChange", "selectedMeasurement", "measurementTestItems", "handleSync", "loadSettings", "fetchTestItems", "fetchSoftwareVersion", "baseurl", "setItem", "defaults", "localAddress", "replace", "plans", "updateCheckAllStatus", "soft_version", "section", "splice", "assign", "target", "checked", "checkedValues", "checkedCount", "selectedBoardItems", "selectedSystemItems", "selectedMeasurementItems", "remainingBoardItems", "filter", "remainingSystemItems", "addForm", "validateAssetCode", "initialDate", "height", "fontSize", "handleAddSave", "tableData", "handleEdit", "isMode", "errors", "validateInput", "localbarcode", "localaddress", "validateDate", "localproduceDate", "localmode", "localmeter", "localhardVersion", "localvendorCode", "localhardDate", "localvendorExt", "handleUpdateSave", "initialbarcode", "initialaddress", "initialmeter", "initialhardVersion", "initialhardDate", "initialproduceDate", "initialvendorCode", "initialvendorExt", "initialmode", "moment", "updateFields", "field", "newValue", "localField", "lengths", "onlyDigits", "Array", "isArray", "includes", "join", "validate<PERSON>ll<PERSON>ields", "some", "timeString", "format", "UpdateNumberInfo", "power", "whole", "myMoment", "getValidInitialDate", "dateStr", "momentDate", "<PERSON><PERSON><PERSON><PERSON>", "assetCode", "meterNo", "hardwareVersion", "hardwareDate", "productionDate", "manufacturerCode", "manufacturerExt", "checkStatus", "formData", "powerCode", "boardCode", "wholeCode", "handleSave", "searchForm", "handleSearch", "rowClassName", "handleDelete", "modal", "okText", "cancelText", "handleModalConfirm", "powerBarcodeError", "localPower", "boardBarcodeError", "localBoard", "wholeBarcodeError", "localWhole", "initialPowerCode", "initialBoardCode", "initialWholeCode", "<PERSON><PERSON><PERSON>", "UpdateAssemblyRelate", "isEditing", "editingRecord", "detail", "hasErrors", "hasAbnormalStatus", "saveData", "resetForm", "i", "deleteRecord", "handleOldCodeChange", "apply", "arguments", "oldCode", "oldCodeValid", "handleNewCodeChange", "newCode", "isSubmitEnabled", "oldAssetInfo", "newAssetInfo", "remainingTime", "timer", "fetchAssetInfo", "isUsed", "$nextTick", "newCodeInput", "focus", "replaceAssetCode", "oldCodeInput", "used", "<PERSON><PERSON>", "Router", "router", "base", "routes", "path", "redirect", "DataManage", "Home", "children", "BoardLevelTest", "HomePage", "PowerBoardTest", "SystemSetting", "WholeMachineReTest", "WholeMachineTest", "WriteNumberConfiguration", "CompleteMachineAssembly", "ChangeAssetCoding", "directive", "inserted", "el", "addEventListener", "charcode", "charCode", "currentInput", "activeElement", "keyupEvent", "Event", "dispatchEvent", "dom", "getElementsByTagName", "stopPropagation", "Antd", "productionTip", "h", "App", "$mount", "__webpack_module_cache__", "cachedModule", "loaded", "__webpack_modules__", "call", "m", "deferred", "O", "chunkIds", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "every", "r", "n", "getter", "__esModule", "d", "a", "definition", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "nmd", "paths", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "chunkLoadingGlobal", "self", "bind", "__webpack_exports__"], "sourceRoot": ""}