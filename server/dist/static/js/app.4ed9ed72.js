(function(){var e={35358:function(e,t,a){var s={"./af":22190,"./af.js":22190,"./ar":7218,"./ar-dz":85785,"./ar-dz.js":85785,"./ar-kw":29417,"./ar-kw.js":29417,"./ar-ly":56904,"./ar-ly.js":56904,"./ar-ma":98617,"./ar-ma.js":98617,"./ar-ps":91318,"./ar-ps.js":91318,"./ar-sa":82699,"./ar-sa.js":82699,"./ar-tn":36789,"./ar-tn.js":36789,"./ar.js":7218,"./az":23050,"./az.js":23050,"./be":8316,"./be.js":8316,"./bg":70310,"./bg.js":70310,"./bm":58884,"./bm.js":58884,"./bn":83469,"./bn-bd":46672,"./bn-bd.js":46672,"./bn.js":83469,"./bo":39118,"./bo.js":39118,"./br":13113,"./br.js":13113,"./bs":23626,"./bs.js":23626,"./ca":40921,"./ca.js":40921,"./cs":17799,"./cs.js":17799,"./cv":12828,"./cv.js":12828,"./cy":93521,"./cy.js":93521,"./da":56962,"./da.js":56962,"./de":93294,"./de-at":16158,"./de-at.js":16158,"./de-ch":95960,"./de-ch.js":95960,"./de.js":93294,"./dv":47963,"./dv.js":47963,"./el":3432,"./el.js":3432,"./en-au":20998,"./en-au.js":20998,"./en-ca":15931,"./en-ca.js":15931,"./en-gb":45930,"./en-gb.js":45930,"./en-ie":58081,"./en-ie.js":58081,"./en-il":71594,"./en-il.js":71594,"./en-in":23904,"./en-in.js":23904,"./en-nz":1507,"./en-nz.js":1507,"./en-sg":19409,"./en-sg.js":19409,"./eo":22177,"./eo.js":22177,"./es":84805,"./es-do":39155,"./es-do.js":39155,"./es-mx":69791,"./es-mx.js":69791,"./es-us":76098,"./es-us.js":76098,"./es.js":84805,"./et":96240,"./et.js":96240,"./eu":20391,"./eu.js":20391,"./fa":20612,"./fa.js":20612,"./fi":4220,"./fi.js":4220,"./fil":65570,"./fil.js":65570,"./fo":5466,"./fo.js":5466,"./fr":14461,"./fr-ca":66306,"./fr-ca.js":66306,"./fr-ch":27081,"./fr-ch.js":27081,"./fr.js":14461,"./fy":73484,"./fy.js":73484,"./ga":76957,"./ga.js":76957,"./gd":72978,"./gd.js":72978,"./gl":89866,"./gl.js":89866,"./gom-deva":65011,"./gom-deva.js":65011,"./gom-latn":84724,"./gom-latn.js":84724,"./gu":71601,"./gu.js":71601,"./he":79802,"./he.js":79802,"./hi":9358,"./hi.js":9358,"./hr":13907,"./hr.js":13907,"./hu":10218,"./hu.js":10218,"./hy-am":20533,"./hy-am.js":20533,"./id":52844,"./id.js":52844,"./is":97353,"./is.js":97353,"./it":6364,"./it-ch":20774,"./it-ch.js":20774,"./it.js":6364,"./ja":6008,"./ja.js":6008,"./jv":68221,"./jv.js":68221,"./ka":92417,"./ka.js":92417,"./kk":42071,"./kk.js":42071,"./km":76149,"./km.js":76149,"./kn":94572,"./kn.js":94572,"./ko":60659,"./ko.js":60659,"./ku":66285,"./ku-kmr":59398,"./ku-kmr.js":59398,"./ku.js":66285,"./ky":81609,"./ky.js":81609,"./lb":119,"./lb.js":119,"./lo":81748,"./lo.js":81748,"./lt":71973,"./lt.js":71973,"./lv":81347,"./lv.js":81347,"./me":53023,"./me.js":53023,"./mi":65747,"./mi.js":65747,"./mk":64341,"./mk.js":64341,"./ml":63840,"./ml.js":63840,"./mn":62058,"./mn.js":62058,"./mr":19182,"./mr.js":19182,"./ms":45197,"./ms-my":89136,"./ms-my.js":89136,"./ms.js":45197,"./mt":36408,"./mt.js":36408,"./my":74064,"./my.js":74064,"./nb":53141,"./nb.js":53141,"./ne":29344,"./ne.js":29344,"./nl":44703,"./nl-be":84641,"./nl-be.js":84641,"./nl.js":44703,"./nn":79873,"./nn.js":79873,"./oc-lnc":61217,"./oc-lnc.js":61217,"./pa-in":24612,"./pa-in.js":24612,"./pl":24457,"./pl.js":24457,"./pt":1089,"./pt-br":79146,"./pt-br.js":79146,"./pt.js":1089,"./ro":45950,"./ro.js":45950,"./ru":27292,"./ru.js":27292,"./sd":56774,"./sd.js":56774,"./se":87493,"./se.js":87493,"./si":3761,"./si.js":3761,"./sk":49711,"./sk.js":49711,"./sl":88558,"./sl.js":88558,"./sq":8633,"./sq.js":8633,"./sr":90688,"./sr-cyrl":47903,"./sr-cyrl.js":47903,"./sr.js":90688,"./ss":31991,"./ss.js":31991,"./sv":27020,"./sv.js":27020,"./sw":15891,"./sw.js":15891,"./ta":45714,"./ta.js":45714,"./te":30206,"./te.js":30206,"./tet":24768,"./tet.js":24768,"./tg":28276,"./tg.js":28276,"./th":57977,"./th.js":57977,"./tk":56928,"./tk.js":56928,"./tl-ph":8046,"./tl-ph.js":8046,"./tlh":41361,"./tlh.js":41361,"./tr":64367,"./tr.js":64367,"./tzl":10627,"./tzl.js":10627,"./tzm":12636,"./tzm-latn":98148,"./tzm-latn.js":98148,"./tzm.js":12636,"./ug-cn":68823,"./ug-cn.js":68823,"./uk":40461,"./uk.js":40461,"./ur":41366,"./ur.js":41366,"./uz":83454,"./uz-latn":18374,"./uz-latn.js":18374,"./uz.js":83454,"./vi":78572,"./vi.js":78572,"./x-pseudo":80464,"./x-pseudo.js":80464,"./yo":93709,"./yo.js":93709,"./zh-cn":65873,"./zh-cn.js":65873,"./zh-hk":17549,"./zh-hk.js":17549,"./zh-mo":52240,"./zh-mo.js":52240,"./zh-tw":90405,"./zh-tw.js":90405};function r(e){var t=o(e);return a(t)}function o(e){if(!a.o(s,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return s[e]}r.keys=function(){return Object.keys(s)},r.resolve=o,e.exports=r,r.id=35358},55927:function(e,t,a){"use strict";a.d(t,{l:function(){return xt}});var s=a(66848),r=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},o=[],i={name:"App",components:{},mounted(){this.updateTitle()},methods:{updateTitle(){document.title="II型光伏分布式电源测试系统"}}},n=i,l=a(81656),d=(0,l.A)(n,r,o,!1,null,null,null),c=d.exports,u=a(63673),m=(a(12193),a(56178)),h=function(){var e=this,t=e._self._c;return t("a-layout",{staticClass:"admin-layout"},[t("a-layout-header",{staticClass:"header"},[t("div",{staticClass:"logo"},[e._v("II型光伏分布式电源测试系统")]),t("div",{staticClass:"header-right"},[t("a-button",{attrs:{type:"primary"},on:{click:e.showModal}},[t("a-icon",{attrs:{type:"info-circle"}}),e._v(" 系统信息 ")],1)],1)]),t("a-layout",[t("a-layout-sider",{staticClass:"sidebar",attrs:{width:"200"}},[t("a-menu",{staticStyle:{height:"100%"},attrs:{mode:"inline"}},e._l(e.navItems,(function(a,s){return t("a-menu-item",{key:s+1},[t("router-link",{attrs:{to:a.route}},[t("a-icon",{attrs:{type:a.icon}}),t("span",[e._v(e._s(a.name))])],1)],1)})),1)],1),t("a-layout-content",{staticClass:"content"},[t("div",{staticClass:"content-wrapper"},[t("router-view")],1)])],1),t("a-modal",{attrs:{title:"系统信息",visible:e.modalVisible,footer:null},on:{ok:e.handleOk,cancel:e.handleCancel}},[t("div",{staticClass:"container"},[t("div",{staticClass:"info-card"},[t("div",{staticClass:"info-row"},[t("span",{staticClass:"label"},[e._v("后端名称:")]),t("span",{staticClass:"value"},[e._v(e._s(e.backendName))])]),t("div",{staticClass:"info-row"},[t("span",{staticClass:"label"},[e._v("后端版本:")]),t("span",{staticClass:"value"},[e._v(e._s(e.backendVersion))])])])])])],1)},p=[],f=(a(81454),a(14603),a(47566),a(98721),a(94373)),g=a(75108);const b=window.location.host,[v,y]=b.split(":"),k=`http://${v||"localhost"}:${y||"8080"}`,S=f.A.create({baseURL:JSON.parse(localStorage.getItem("backendAddress"))||k,timeout:5e3});S.interceptors.request.use((e=>e),(e=>{console.log(e),Promise.reject(e)})),S.interceptors.response.use((e=>e.data),(e=>{if(console.log("全局拦截器捕获到错误:",e),void 0!==e.response){if(400===e.response.status){const t=e.response.data.message;g.A.error({message:"系统提示",description:t,duration:4})}if(500===e.response.status){const t=e.response.data.message;g.A.error({message:"系统提示",description:t,duration:4})}}return Promise.resolve({data:[]})}));var w=S;const C=window.location.host,[I,x]=C.split(":"),_=`http://${I||"localhost"}:${x||"8080"}`;function D(){return w({url:"/api/info",method:"get"})}function T(e,t,a,s){return w({url:"/api/power",method:"get",params:{barcode:e,result:t,count:a,page:s}})}function $(e,t,a){return w({url:"/api/power",method:"post",data:{barcode:e,result:t,count:a}})}function j(e,t){return w({url:"/api/power",method:"put",data:{barcode:e,result:t}})}function A(e,t){return w({url:"/api/barcode",method:"get",params:{type:t,barcode:e}})}function N(e,t,a,s,r){return w({url:"/api/assemble",method:"get",params:{powerBarcode:e,boardBarcode:t,wholeBarcode:a,page:s,count:r}})}function M(e,t,a){return w({url:"/api/assemble",method:"post",data:{powerBarcode:e,boardBarcode:t,wholeBarcode:a}})}function B(e,t,a){return w({url:"/api/assemble",method:"put",headers:{"Content-Type":"application/json"},data:{powerBarcode:e,boardBarcode:t,wholeBarcode:a}})}function E(e){return w({url:"/api/assemble",method:"DELETE",headers:{"Content-Type":"application/json"},data:e})}function F(e,t,a,s,r,o,i,n,l,d){return w({url:"/api/code",method:"get",params:{barcode:t,address:e,meter:a,hardVersion:s,hardDate:r,produceDate:o,vendorCode:i,vendorExt:n,page:l,count:d}})}function P(e,t,a,s,r,o,i,n,l,d){return w({url:"/api/code",method:"post",headers:{"Content-Type":"application/json"},data:{barcode:e,address:t,meter:a,hardVersion:s,hardDate:r,produceDate:o,vendorCode:i,vendorExt:n,mode:l,count:d}})}function O(e,t,a,s,r,o,i,n,l){return w({url:"/api/code",method:"put",headers:{"Content-Type":"application/json"},data:{barcode:e,address:t,meter:a,hardVersion:s,hardDate:r,produceDate:o,vendorCode:i,vendorExt:n,mode:l}})}function V(e,t,a,s,r,o,i,n){return w({url:"/api/record",method:"get",params:{type:e,barcode:t,start:a,end:s,status:r,download:o,page:i,count:n}})}async function L(e,t,a,s,r,o,i){try{const n=`${JSON.parse(localStorage.getItem("backendAddress"))||_}/api/record?planType=${e}&barcode=${t}&start=${a}&end=${s}&status=${r}&download=true&page=${o}&count=${i}`,l=await(0,f.A)({method:"get",url:n,responseType:"blob"}),d=new Blob([l.data],{type:l.headers["content-type"]}),c=document.createElement("a"),u=URL.createObjectURL(d);c.href=u,c.download="exported_data.xlsx",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)}catch(n){console.error(n)}}function Y(){return w({url:"/api/config/whole",method:"get"})}function q(e,t){const a=e.map((e=>(0,f.A)({url:`http://${e}/api/config/whole`,method:"put",headers:{"Content-Type":"application/json"},data:{soft_version:t}})));return Promise.all(a)}function R(){return w({url:"/api/config/testitems",method:"get"})}function z(e){return w({url:"/api/codeused",method:"get",params:{barcode:e}})}function W(e,t){return w({url:"/api/codeused",method:"put",headers:{"Content-Type":"application/json"},data:{barcode:e,newBarcode:t},timeout:6e4})}var U={name:"AdminLayout",data(){return{backendName:"",backendVersion:"",navItems:[{name:"数据管理",route:"/DataManage",icon:"database"},{name:"电源板测试",route:"/PowerBoardTest",icon:"thunderbolt"},{name:"板级测试",route:"/BoardLevelTest",icon:"cluster"},{name:"整机测试",route:"/WholeMachineTest",icon:"desktop"},{name:"整机复试",route:"/WholeMachineReTest",icon:"sync"},{name:"整机组装",route:"/CompleteMachineAssembly",icon:"tool"},{name:"更换资产编码",route:"/ChangeAssetCoding",icon:"edit"},{name:"写号配置",route:"/WriteNumberConfiguration",icon:"setting"},{name:"系统设置",route:"/SystemSetting",icon:"setting"}],modalVisible:!1}},computed:{currentRoute(){return this.$route.name||"Dashboard"}},mounted(){this.fetchData()},methods:{fetchData(){xt.$on("updateBackendInfo",(e=>{this.backendName=e.name,this.backendVersion=e.version})),""===this.backendName&&""===this.backendVersion&&D().then((e=>{this.backendName=e.name,this.backendVersion=e.version}))},showModal(){this.modalVisible=!0},handleOk(){this.modalVisible=!1},handleCancel(){this.modalVisible=!1}}},J=U,Q=(0,l.A)(J,h,p,!1,null,"3f7ce64d",null),H=Q.exports,K=function(){var e=this,t=e._self._c;return t("div",{staticClass:"testing-interface"},[t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{columns:e.columns,"data-source":e.testData,pagination:!1,rowKey:e=>e.key,scroll:{x:800}},scopedSlots:e._u([{key:"barcode",fn:function(a,s){return[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],class:e.getBarcodeInputClass(s),attrs:{maxLength:20,placeholder:"请输入条码",disabled:s.isTesting},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleBarcodeSubmit(s)}},model:{value:s.barcode,callback:function(t){e.$set(s,"barcode",t)},expression:"record.barcode"}})]}},{key:"device",fn:function(a,s){return[t("div",{staticClass:"device-info"},[t("div",{staticClass:"device-id"},[e._v(e._s(s.device))]),t("div",{staticClass:"device-version"},[t("span",{staticClass:"position"},[e._v(e._s(s.name)+"/")]),t("span",{staticClass:"version"},[e._v("v"+e._s(s.version))])])])]}},{key:"time",fn:function(a,s){return[t("span",{class:{"time-default":!s.isTesting&&(!s.testStatus||"waiting"===s.testStatus),"time-testing":s.isTesting||"testing"===s.testStatus,"time-success":!s.isTesting&&"success"===s.testStatus,"time-error":!s.isTesting&&"fail"===s.testStatus}},[e._v(e._s(s.time))])]}},e._l(e.testItems,(function(a){return{key:a.key,fn:function(s,r){return[t("a-tag",{key:a.key,staticStyle:{width:"50px"},attrs:{color:e.getStatusColor(r[a.key])}},[e._v(" "+e._s(e.getStatusText(r[a.key]))+" ")])]}}}))],null,!0)})],1)],1)},G=[],X=(a(44114),a(98992),a(72577),a(3949),a(17694)),Z={name:"TestingInterface",data(){return{testItems:[],columns:[{title:"工装",dataIndex:"device",width:150,scopedSlots:{customRender:"device"}},{title:"时间",dataIndex:"time",width:80,scopedSlots:{customRender:"time"}},{title:"条码",dataIndex:"barcode",width:180,scopedSlots:{customRender:"barcode"}}],testData:[],websockets:[],timers:{},workbenchAddresses:[],socketMap:[],submissionStatus:{}}},created(){const e=localStorage.getItem("selectedBoardItems");e&&(this.testItems=JSON.parse(e).map((e=>({key:e.key,title:e.title}))),this.testItems.forEach((e=>{this.columns.push({title:e.title,dataIndex:e.key,width:150,scopedSlots:{customRender:e.key}})})))},methods:{validateBarcode(e){const t=/^\d{4,20}$/;return t.test(e)},updateSubmissionStatus(e,t){this.$set(this.submissionStatus,e,t),setTimeout((()=>{this.$set(this.submissionStatus,e,null)}),2e3)},getBarcodeInputClass(e){return{"input-success":"success"===this.submissionStatus[e.key],"input-error":"error"===this.submissionStatus[e.key]}},handleBarcodeSubmit(e){if(!window.isDirectiveEnter)return this.validateBarcode(e.barcode)?void(e.isTesting?X.A.warning("测试进行中，请等待测试完成"):this.websockets.forEach(((t,a)=>{const s=this.socketMap.findIndex((t=>t===e.device));t.readyState===WebSocket.OPEN&&a===s&&(t.send(JSON.stringify({type:"boardCode",planType:"board",device:e.name,barcode:e.barcode})),this.updateSubmissionStatus(e.key,"success"))}))):(X.A.error("条码必须为4-20位纯数字"),void this.updateSubmissionStatus(e.key,"error"));window.isDirectiveEnter=!1},getStatusColor(e){const t={success:"#00FF99",fail:"#FF3333",testing:"#66CCFF",waiting:"#999999"};return t[e]||t.waiting},getStatusText(e){const t={success:"成功",fail:"失败",testing:"测试中",waiting:"未测试"};return t[e]||t.waiting},initWebSocket(){const e=localStorage.getItem("boardIPs");this.workbenchAddresses=JSON.parse(e).map((e=>`ws://${e}/echo`)),this.workbenchAddresses.forEach((e=>{let t,a=new WebSocket(e),s=2e3;const r=1.5;let o=!1;const i=()=>{if(a.readyState!==WebSocket.OPEN){if(s*=r,s>1e4)return console.error(`WebSocket连接超时（地址：${e}）`),X.A.error(`与工装（地址：${e}）的连接超时，请检查网络`),void a.close();console.warn(`WebSocket连接未建立，正在延长超时等待时间至${s}毫秒（地址：${e}）`),X.A.warning(`与工装（地址：${e}）的连接较慢，正在尝试继续等待...`),t=setTimeout(i,s)}else o||(a.send(JSON.stringify({type:"devicePlan",planType:"board",items:this.testItems.map((e=>e.key))})),o=!0),clearTimeout(t)};a.onopen=()=>{console.log(`WebSocket连接已建立，地址：${e}`),X.A.success(`WebSocket连接已建立，地址：${e}`),o||(a.send(JSON.stringify({type:"devicePlan",planType:"board",items:this.testItems.map((e=>e.key))})),o=!0)},a.onerror=t=>{console.error(`WebSocket错误（地址：${e}）:`,t),X.A.error(`与工装（地址：${e}）的连接出现错误，请检查网络`)},a.onclose=()=>{console.log(`WebSocket连接已断开，地址：${e}`),X.A.warning(`WebSocket连接已断开，地址：${e}`)},a.onmessage=this.handleWebSocketMessage,t=setTimeout(i,s),this.websockets.push(a)}))},handleWebSocketMessage(e){const t=JSON.parse(e.data);if(console.log(t),"startTest"!==t.type)switch(t.type){case"devicePlan":0===t.error_code?X.A.success("测试方案已配置成功"):X.A.warning(`${t.message}`);break;case"deviceInfo":this.updateTestDataInfo(t);break;case"finishTest":case"failTest":this.updateTestStatus(t);break;case"startItem":case"finishItem":case"failItem":this.updateTestItemStatus(t);break;case"boardCode":this.handleBarcodeResponse(t);break;default:console.warn("未知的消息类型:",t.type)}else this.updateTestStatus(t)},handleBarcodeResponse(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&(0===e.error_code?(this.updateSubmissionStatus(t.key,"success"),t.testStatus=null,t.isTesting=!1,t.time=0):(this.updateSubmissionStatus(t.key,"error"),X.A.error(`条码提交失败: ${e.message}`)),this.$forceUpdate())},updateTestDataInfo(e){const t=e.devices.map(((t,a)=>{const s={key:`${e.name}-${t}-${a}`,name:t,device:e.name,time:0,barcode:"",version:e.version};return this.testItems.forEach((e=>{s[e.key]=null})),s}));this.testData=this.testData.concat(t),this.socketMap.push(e.name)},updateTestStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startTest"===e.type?(t.isTesting=!0,t.testStatus="testing",this.startTimer(e),this.$forceUpdate()):(t.isTesting=!1,t.testStatus="finishTest"===e.type?"success":"fail",this.$set(t,"testStatus",t.testStatus),this.stopTimer(e),setTimeout((()=>{"finishTest"===e.type&&(t.barcode=""),this.$set(t,"barcode",t.barcode)}),2e3),this.$forceUpdate()))},updateTestItemStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startItem"===e.type?(t[e.item]="testing",this.testData[t.key]=t):"finishItem"===e.type?(t[e.item]="success",this.testData[t.key]=t):(t[e.item]="fail",this.testData[t.key]=t))},startTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));if(t&&!this.timers[t.key]){let e=0;this.timers[t.key]=setInterval((()=>{e++,t.time=this.formatTime(e),this.testData[t.key].time=t.time}),1e3)}},stopTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&this.timers[t.key]&&(clearInterval(this.timers[t.key]),delete this.timers[t.key])},formatTime(e){const t=Math.floor(e/60),a=e%60;return`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}},mounted(){setTimeout((()=>{this.initWebSocket()}),1e3)},beforeDestroy(){Object.keys(this.timers).forEach((e=>{clearInterval(this.timers[e])})),this.websockets.forEach((e=>{e&&e.close()}))}},ee=Z,te=(0,l.A)(ee,K,G,!1,null,"368db563",null),ae=te.exports,se=function(){var e=this,t=e._self._c;return t("div",{staticClass:"data-management"},[t("a-card",{staticClass:"query-card",attrs:{bordered:!1}},[t("a-form",{attrs:{form:e.form,layout:"inline"},on:{submit:e.handleQuery}},[t("a-form-item",{attrs:{label:"测试方案类型"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["planType"],expression:"['planType']"}],staticStyle:{width:"140px"},attrs:{placeholder:"请选择"},on:{change:e.handlePlanTypeChange}},[t("a-select-option",{attrs:{value:"power"}},[e._v("电源板条码")]),t("a-select-option",{attrs:{value:"board"}},[e._v("主板条码")]),t("a-select-option",{attrs:{value:"whole"}},[e._v("整机资产条码")])],1)],1),t("a-form-item",{attrs:{label:"条码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["barcode",{rules:[{validator:e.validateBarcode}]}],expression:"['barcode', { rules: [{ validator: validateBarcode }] }]"}],attrs:{placeholder:"请输入条码",maxLength:22}})],1),t("a-form-item",{attrs:{label:"时间范围"}},[t("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["dateRange"],expression:"['dateRange']"}],staticClass:"custom-range-picker",attrs:{placeholder:["开始日期","结束日期"],locale:e.locale},on:{change:e.handleDateRangeChange}})],1),t("a-form-item",{attrs:{label:"测试状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{initialValue:0}],expression:"['status', { initialValue: 0 }]"}],staticStyle:{width:"180px"}},[t("a-select-option",{attrs:{value:0}},[e._v("全部状态")]),t("a-select-option",{attrs:{value:1}},[e._v("全部测完且全部合格")]),t("a-select-option",{attrs:{value:2}},[e._v("全部测完存在不合格")]),t("a-select-option",{attrs:{value:3}},[e._v("有未测试项")])],1)],1),t("a-form-item",[t("a-button",{attrs:{type:"primary","html-type":"submit"},on:{click:e.handleQuery}},[e._v("查询")]),t("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),t("a-card",{staticClass:"result-card",attrs:{bordered:!1}},[t("template",{slot:"extra"},[t("a-button",{attrs:{type:"primary"},on:{click:e.exportToExcel}},[t("a-icon",{attrs:{type:"download"}}),e._v("导出数据 ")],1)],1),t("a-table",{attrs:{columns:e.columns,dataSource:e.data,loading:e.loading,pagination:e.pagination},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"powerItem",fn:function(a,s){return[t("a-tag",{attrs:{color:e.getStatusColor(s.powerItem)}},[e._v(" "+e._s(e.getStatusText(s.powerItem))+" ")])]}},{key:"boardItem",fn:function(a,s){return[t("a-tag",{attrs:{color:e.getStatusColor(s.boardItem)}},[e._v(" "+e._s(e.getStatusText(s.boardItem))+" ")])]}},{key:"wholeItem",fn:function(a,s){return[t("a-tag",{attrs:{color:e.getStatusColor(s.wholeItem)}},[e._v(" "+e._s(e.getStatusText(s.wholeItem))+" ")])]}},{key:"rewholeItem",fn:function(a,s){return[t("a-tag",{attrs:{color:e.getStatusColor(s.rewholeItem)}},[e._v(" "+e._s(e.getStatusText(s.rewholeItem))+" ")])]}}])})],2)],1)},re=[],oe={data(){return{locale:{lang:{placeholder:"请选择日期",yearPlaceholder:"年",monthPlaceholder:"月",dayPlaceholder:"日",weekPlaceholder:"周",rangePlaceholder:["开始日期","结束日期"],today:"今天",now:"此刻",ok:"确定",clear:"清除",prevYear:"去年",nextYear:"明年",prevMonth:"上月",nextMonth:"下月",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",yearFormat:"YYYY年",monthFormat:"MM月",dateFormat:"YYYY-MM-DD",dayFormat:"D日",dateTimeFormat:"YYYY-MM-DD HH:mm:ss",timeFormat:"HH:mm:ss",secondFormat:"ss秒",meridiem:"上午/下午",am:"上午",pm:"下午"},timePickerLocale:{placeholder:"请选择时间"}},form:this.$form.createForm(this),loading:!1,data:[],pagination:{current:1,pageSize:10,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>t[0]+"-"+t[1]+" 共"+e+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},columns:[{title:"电源板条码",dataIndex:"powerBarcode",key:"powerBarcode"},{title:"主板条码",dataIndex:"boardBarcode",key:"boardBarcode"},{title:"资产条码",dataIndex:"wholeBarcode",key:"wholeBarcode"},{title:"电源板测试结果",dataIndex:"powerItem",key:"powerItem",scopedSlots:{customRender:"powerItem"}},{title:"主板测试结果",dataIndex:"boardItem",key:"boardItem",scopedSlots:{customRender:"boardItem"}},{title:"整机测试结果",dataIndex:"wholeItem",key:"wholeItem",scopedSlots:{customRender:"wholeItem"}},{title:"整机复测结果",dataIndex:"rewholeItem",key:"rewholeItem",scopedSlots:{customRender:"rewholeItem"}}],startTime:0,endTime:0,excelData:[["","电源板条码","电源板测试结果","主板条码","主板测试结果","资产条码","整机测试结果","整机复测结果"]],currentPlanType:""}},methods:{handlePlanTypeChange(e){this.currentPlanType=e,this.form.setFieldsValue({barcode:""}),this.form.validateFields(["barcode"],{force:!0})},validateBarcode(e,t,a){if(this.currentPlanType&&!t)return void a("请输入条码");if(!t)return void a();const s=/^\d+$/;if(s.test(t))switch(this.currentPlanType){case"power":case"board":t.length<4||t.length>20?a("请输入4-20位纯数字"):a();break;case"whole":22!==t.length?a("请输入22位纯数字"):a();break;default:a()}else a("请输入纯数字")},handleDateRangeChange(e){e&&2===e.length?(this.startTime=e[0].startOf("day").unix(),this.endTime=e[1].endOf("day").unix()):(this.startTime=0,this.endTime=0)},handleQuery(e){e.preventDefault(),this.form.validateFields(((e,t)=>{e||this.fetchData(t)}))},handleReset(){this.form.resetFields(),this.currentPlanType="",this.startTime=0,this.endTime=0},exportToExcel(){this.form.validateFields(((e,t)=>{if(e)this.$message.error("请修正输入错误后再导出");else{const e=t.planType||"",a=t.barcode||"";L(e,a,this.startTime,this.endTime,t.status,this.pagination.current,this.pagination.pageSize).catch((e=>{console.log(e)}))}}))},handleTableChange(e){this.pagination=e,this.form.validateFields(((e,t)=>{e||this.fetchData(t)}))},fetchData(e){this.loading=!0;const t=e.planType||"",a=e.barcode||"";console.log(this.startTime),console.log(this.endTime),V(t,a,this.startTime,this.endTime,e.status,!1,this.pagination.current,this.pagination.pageSize).then((e=>{this.pagination.total=e.page_count*this.pagination.pageSize,this.data=e.records.map(((e,t)=>({key:t,powerBarcode:e.powerBarcode,powerItem:e.powerItem,boardBarcode:e.boardBarcode,boardItem:e.boardItem,wholeBarcode:e.wholeBarcode,wholeItem:e.wholeItem,rewholeItem:e.rewholeItem})))})).catch((e=>{console.log(e),this.$message.error("获取数据失败")})).finally((()=>{this.loading=!1}))},getStatusColor(e){const t=["orange","green","red"];if(0===Object.keys(e).length)return t[0];let a=!1,s=!0;for(const r in e){const t=e[r];if(""===t){a=!0;break}"不合格"===t&&(s=!1)}return a?t[0]:s?t[1]:t[2]},getStatusText(e){const t=["有未测试项","全部测完且全部合格","全部测完存在不合格"];if(0===Object.keys(e).length)return t[0];let a=!1,s=!0;for(const r in e){const t=e[r];if(""===t){a=!0;break}"不合格"===t&&(s=!1)}return a?t[0]:s?t[1]:t[2]}},mounted(){this.fetchData(this.form.getFieldsValue())}},ie=oe,ne=(0,l.A)(ie,se,re,!1,null,"b50d7ad8",null),le=ne.exports,de=function(){var e=this,t=e._self._c;return t("a-layout",{staticClass:"layout"},[t("div",{style:{background:"#fff",padding:"24px",minHeight:"280px"}},[t("a-row",{attrs:{gutter:16}},[t("a-col",{attrs:{span:16}},[t("a-card",{attrs:{title:"测试工装状态",bordered:!1}},[t("a-table",{attrs:{columns:e.fixtureColumns,"data-source":e.fixtureData,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:"运行中"===a?"green":"空闲"===a?"blue":"red"}},[e._v(" "+e._s(a)+" ")])]}}])})],1)],1),t("a-col",{attrs:{span:8}},[t("a-card",{attrs:{title:"系统概览",bordered:!1}},[t("a-statistic-card",[t("a-statistic",{staticStyle:{"margin-top":"15%",display:"block"},attrs:{title:"在线测试工装",value:e.onlineFixtures,precision:0},scopedSlots:e._u([{key:"prefix",fn:function(){return[t("a-icon",{attrs:{type:"cluster"}})]},proxy:!0}])}),t("a-statistic",{staticStyle:{"margin-top":"30px",display:"block"},attrs:{title:"正在测试的模块",value:e.testingModules,precision:0},scopedSlots:e._u([{key:"prefix",fn:function(){return[t("a-icon",{attrs:{type:"experiment"}})]},proxy:!0}])})],1)],1)],1)],1),t("a-card",{staticStyle:{"margin-top":"16px"},attrs:{title:"当前测试"}},[t("a-table",{attrs:{columns:e.testColumns,"data-source":e.testData},scopedSlots:e._u([{key:"progress",fn:function(e,a){return[t("a-progress",{attrs:{percent:a.progress,size:"small"}})]}},{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:"进行中"===a?"blue":"完成"===a?"green":"orange"}},[e._v(" "+e._s(a)+" ")])]}}])})],1),t("a-card",{staticStyle:{"margin-top":"16px"},attrs:{title:"最近测试结果"}},[t("a-list",{attrs:{itemLayout:"horizontal",dataSource:e.recentResults},scopedSlots:e._u([{key:"renderItem",fn:function(a){return t("a-list-item",{},[t("a-list-item-meta",{attrs:{description:a.description}},[t("template",{slot:"title"},[t("a",{attrs:{href:"javascript:;"}},[e._v(e._s(a.title))])]),t("a-avatar",{style:{backgroundColor:a.passed?"#52c41a":"#f5222d"},attrs:{slot:"avatar",icon:a.passed?"check":"close"},slot:"avatar"})],2),t("div",[e._v(e._s(a.time))])],1)}}])})],1)],1),t("a-layout-footer",{staticStyle:{"text-align":"center"}},[e._v(" II型光伏分布式电源测试系统 ©2023 Created by Your Company ")])],1)},ce=[],ue={data(){return{fixtureColumns:[{title:"工装ID",dataIndex:"id",key:"id"},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:"当前测试模块",dataIndex:"currentModule",key:"currentModule"},{title:"已完成测试项",dataIndex:"completedTests",key:"completedTests"}],fixtureData:[{id:"F001",status:"运行中",currentModule:"PV-M-001",completedTests:3},{id:"F002",status:"空闲",currentModule:"-",completedTests:0},{id:"F003",status:"运行中",currentModule:"PV-M-002",completedTests:1},{id:"F004",status:"故障",currentModule:"-",completedTests:0}],onlineFixtures:3,testingModules:2,testColumns:[{title:"模块ID",dataIndex:"moduleId",key:"moduleId"},{title:"测试项目",dataIndex:"testItem",key:"testItem"},{title:"进度",dataIndex:"progress",key:"progress",scopedSlots:{customRender:"progress"}},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}}],testData:[{moduleId:"PV-M-001",testItem:"电源板测试",progress:75,status:"进行中"},{moduleId:"PV-M-002",testItem:"板级测试",progress:30,status:"进行中"},{moduleId:"PV-M-003",testItem:"整机测试",progress:100,status:"完成"}],recentResults:[{title:"PV-M-004 测试完成",description:"电源板测试项目通过",passed:!0,time:"2023-05-20 14:30"},{title:"PV-M-005 测试完成",description:"板级测试未通过",passed:!1,time:"2023-05-20 13:45"},{title:"PV-M-006 测试完成",description:"整机测试项目通过",passed:!0,time:"2023-05-20 12:15"}]}}},me=ue,he=(0,l.A)(me,de,ce,!1,null,"7f775ba0",null),pe=he.exports,fe=function(){var e=this,t=e._self._c;return t("div",{staticClass:"power-supply-test"},[t("a-card",{staticClass:"query-card",attrs:{bordered:!1}},[t("a-col",{attrs:{span:24}},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("a-col",{attrs:{span:7}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"电源板条码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["startCode",{rules:[{required:!0,message:"请输入电源板条码"},{validator:e.validateNumber}]}],expression:"['startCode', { rules: [{ required: true, message: '请输入电源板条码' }, { validator: validateNumber }] }]"}],attrs:{placeholder:"请输入电源板条码",maxLength:20}})],1)],1),t("a-col",{attrs:{span:6}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"连续的条码数量"}},[t("a-input-group",{attrs:{compact:""}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["quantity",{rules:[{required:!0,message:"请输入数量"},{validator:e.validateCount}]}],expression:"['quantity', { rules: [{ required: true, message: '请输入数量' }, { validator: validateCount }] }]"}],staticStyle:{width:"70%"},attrs:{min:1,max:1e4}})],1)],1)],1),t("a-col",{attrs:{span:3}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"状态"}},[t("a-switch",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{valuePropName:"checked",initialValue:!0}],expression:"['status', { valuePropName: 'checked', initialValue: true }]"}],attrs:{"checked-children":"合格","un-checked-children":"不合格"}})],1)],1),t("a-col",{attrs:{span:3}},[t("a-form-item",[t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"save"},on:{click:e.batchSave}},[e._v("添加")])],1)],1)],1)],1)],1),t("a-card",{staticClass:"query-card_",attrs:{bordered:!1}},[t("a-form",{attrs:{form:e.queryForm,layout:"inline"},on:{submit:e.handleQuery}},[t("a-row",{staticStyle:{height:"80px","background-color":"#f0f9eb",display:"flex","align-items":"center"},attrs:{gutter:16}},[t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"条码"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["queryCode",{rules:[{validator:e.validateNumber}]}],expression:"['queryCode', { rules: [{ validator: validateNumber }]}]"}],attrs:{placeholder:"请输入条码",maxLength:20}})],1)],1),t("a-col",{attrs:{span:4}},[t("a-form-item",{attrs:{label:"状态"}},[t("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["queryStatus"],expression:"['queryStatus']"}],staticStyle:{width:"120px"},attrs:{placeholder:"请选择状态"}},[t("a-select-option",{attrs:{value:""}},[e._v("全部")]),t("a-select-option",{attrs:{value:"true"}},[e._v("合格")]),t("a-select-option",{attrs:{value:"false"}},[e._v("不合格")])],1)],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",[t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"search"},on:{click:e.handleQuery}},[e._v("查询")])],1)],1)],1)],1),t("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"key",columns:e.columns,dataSource:e.queryResults,pagination:e.detailIpagination},on:{change:e.detailhandleTableChange},scopedSlots:e._u([{key:"status",fn:function(a){return[t("a-tag",{attrs:{color:a?"green":"red"}},[e._v(" "+e._s(a?"合格":"不合格")+" ")])]}},{key:"action",fn:function(a,s){return[s.code?e._e():t("span",{staticClass:"no-code"},[e._v("无条码")]),t("a-button",{staticClass:"update-button",attrs:{type:"primary",icon:"edit"},on:{click:function(t){return e.updateData(s)}}},[e._v(" 更新 ")])]}}])})],1),t("UpdatePowerBoardTest",{ref:"UpdatePowerBoardTest",attrs:{initialCode:e.barcode,initialStatus:e.isStatus},on:{"child-message":e.handleChildMessage}})],1)},ge=[],be=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"更新电源板状态",visible:e.visible,"confirm-loading":e.confirmLoading,footer:null},on:{ok:e.handleOk,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.loading}},[t("a-form",{staticStyle:{display:"flex","align-items":"center",gap:"16px"},attrs:{form:e.updateForm},on:{submit:e.handleUpdate}},[t("a-form-item",{staticStyle:{"margin-left":"25%",display:"flex","align-items":"center",gap:"8px"},attrs:{label:"状态"}},[t("a-switch",{attrs:{"checked-children":"合格","un-checked-children":"不合格",disabled:e.confirmLoading},model:{value:e.localStatus,callback:function(t){e.localStatus=t},expression:"localStatus"}})],1),t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"}},[t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"sync",loading:e.confirmLoading},on:{click:e.handleUpdate}},[e._v(" 更新 ")])],1)],1)],1)],1)],1)},ve=[],ye={name:"UpdatePowerBoardTest",props:{initialCode:{type:String,required:!0},initialStatus:{type:Boolean,required:!0}},data(){return{localBarcode:"",localStatus:this.initialStatus,updateForm:this.$form.createForm(this),visible:!1,confirmLoading:!1,loading:!1,barcodeError:""}},created(){this.$parent.$on("initial-status-updated",(e=>{this.localStatus=e})),this.$parent.$on("initial-barcode-updated",(e=>{this.localBarcode=e}))},methods:{showModal(){this.visible=!0},handleCancel(){this.visible=!1,this.updateForm.resetFields(),this.barcodeError=""},handleUpdate(e){e.preventDefault(),this.updateForm.validateFields((async(e,t)=>{if(console.log(t),!e){this.confirmLoading=!0;try{j(this.localBarcode,this.localStatus).then((e=>{console.log(e),this.$emit("child-message",e.error_code),0===e.error_code?(this.$message.success({content:"电源板状态修改成功",duration:3}),this.handleCancel()):g.A.error({message:"系统提示",description:e.message,duration:4})}))}catch(a){g.A.error({message:"系统提示",description:a.response.data.message,duration:4})}finally{this.confirmLoading=!1}}}))},handleOk(){this.updateForm.submit()}}},ke=ye,Se=(0,l.A)(ke,be,ve,!1,null,"0226015e",null),we=Se.exports,Ce={components:{UpdatePowerBoardTest:we},data(){return{detailIpagination:{current:1,pageSize:10,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>t[0]+"-"+t[1]+" 共"+e+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},barcode:"",isStatus:!1,form:this.$form.createForm(this),queryForm:this.$form.createForm(this),queryResults:[],columns:[{title:"条码",dataIndex:"code",key:"code"},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:"操作",key:"action",scopedSlots:{customRender:"action"}}],selectedRowKeys:[]}},mounted(){this.fetchData()},methods:{handleChildMessage(e){0===e&&this.fetchData()},fetchData(){this.queryForm.validateFields(((e,t)=>{e||T(t.queryCode,t.queryStatus,this.detailIpagination.pageSize,this.detailIpagination.current).then((e=>{console.log(e),this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize,this.queryResults=e.records.map(((e,t)=>({key:t,code:e.barcode,status:e.result})))}))}))},validateNumber(e,t,a){if(!t)return void a();const s=Number(t);console.log(s),Number.isNaN(s)?a("请输入数字"):t.toString().length<4||t.toString().length>20?a("输入4 ~ 20位数字"):a()},validateCount(e,t,a){if(void 0===t||null===t||""===t)return void a();const s=Number(t);isNaN(s)?a(new Error("请输入数字")):s%1===0?/^\d+$/.test(s.toString())?a():a(new Error("请输入纯数字，不能包含其他字符")):a(new Error("请输入整数，不能输入小数"))},detailhandleTableChange(e){this.detailIpagination=e,this.queryForm.validateFields(((e,t)=>{e||T(t.queryCode,t.queryStatus,this.detailIpagination.pageSize,this.detailIpagination.current).then((e=>{this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize,console.log(e),this.queryResults=e.records.map(((e,t)=>({key:t,code:e.barcode,status:e.result})))}))}))},async batchSave(){this.form.validateFields(((e,t)=>{e||$(t.startCode,t.status,t.quantity).then((e=>{console.log(e),this.queryForm.validateFields(((e,t)=>{e||(T(t.queryCode,t.queryStatus,this.detailIpagination.pageSize,this.detailIpagination.current).then((e=>{console.log(e),0===e.error_code?(this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize,this.queryResults=e.records.map(((e,t)=>({key:t,code:e.barcode,status:e.result})))):this.$message.error(e.message)})),this.form.resetFields())}))}))}))},handleSubmit(e){e.preventDefault(),this.form.validateFields(((e,t)=>{e||(this.$message.success("批量录入成功"),console.log("批量录入表单提交:",t))}))},handleQuery(e){e.preventDefault(),this.queryForm.validateFields(((e,t)=>{e||T(t.queryCode,t.queryStatus,this.detailIpagination.pageSize,this.detailIpagination.current).then((e=>{console.log(e),this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize,this.queryResults=e.records.map(((e,t)=>({key:t,code:e.barcode,status:e.result})))}))}))},updateData(e){try{this.barcode=e.code,this.isStatus=e.status,this.$emit("initial-status-updated",this.isStatus),this.$emit("initial-barcode-updated",this.barcode),this.$refs.UpdatePowerBoardTest.title="更新写号信息",this.$refs.UpdatePowerBoardTest.visible=!0}catch(t){console.error("Error fetching initial status:",t)}}}},Ie=Ce,xe=(0,l.A)(Ie,fe,ge,!1,null,"3967c1d9",null),_e=xe.exports,De=function(){var e=this,t=e._self._c;return t("div",{staticClass:"test-interface"},[t("a-form",{attrs:{form:e.form,layout:"vertical"}},[t("a-card",{staticClass:"main-card"},[t("a-form-item",{staticClass:"backend-address",attrs:{label:"后端地址:"}},[t("div",{staticClass:"backend-address-input"},[t("a-input",{attrs:{placeholder:"请输入后端地址"},model:{value:e.backendAddress,callback:function(t){e.backendAddress=t},expression:"backendAddress"}}),t("a-button",{attrs:{type:"primary"},on:{click:e.handleSyncAddress}},[e._v(" 同步后端地址 ")])],1)]),t("a-card",{staticClass:"section-card",attrs:{title:"板级测试"}},[t("div",{staticClass:"ip-list"},[t("div",{staticClass:"ip-input-group"},[t("a-input",{attrs:{placeholder:"请输入工装地址"},model:{value:e.board,callback:function(t){e.board=t},expression:"board"}}),t("a-button",{attrs:{type:"primary"},on:{click:function(t){return e.addIP("board",e.board)}}},[e._v(" 添加地址 ")])],1),t("h4",{staticClass:"ip-list-title"},[e._v("工装地址列表")]),t("a-list",{staticClass:"ip-list-items",attrs:{bordered:"",dataSource:e.boardIPs},scopedSlots:e._u([{key:"renderItem",fn:function(a,s){return t("a-list-item",{},[t("a-input",{model:{value:e.boardIPs[s],callback:function(t){e.$set(e.boardIPs,s,t)},expression:"boardIPs[index]"}}),t("a-button",{attrs:{type:"link",icon:"delete"},on:{click:function(t){return e.removeIP("board",s)}}},[e._v(" 删除 ")])],1)}}])})],1),t("div",{staticClass:"test-items-section"},[t("h4",{staticClass:"test-items-title"},[e._v("测试项选择")]),t("a-checkbox",{attrs:{indeterminate:e.indeterminateBoard,checked:e.checkAllBoard},on:{change:e.onCheckAllBoardChange}},[e._v(" 全选 ")]),t("a-divider",{staticClass:"divider"}),t("a-checkbox-group",{on:{change:e.onBoardChange},model:{value:e.selectedBoard,callback:function(t){e.selectedBoard=t},expression:"selectedBoard"}},[t("a-row",{attrs:{gutter:[16,16]}},e._l(e.boardTestItems,(function(a){return t("a-col",{key:a.key,attrs:{span:8}},[t("a-checkbox",{attrs:{value:a.key}},[e._v(" "+e._s(a.title)+" ")])],1)})),1)],1)],1)]),t("a-card",{staticClass:"section-card",attrs:{title:"整机测试"}},[t("div",{staticClass:"ip-list"},[t("div",{staticClass:"ip-input-group"},[t("a-input",{attrs:{placeholder:"请输入工装地址"},model:{value:e.system,callback:function(t){e.system=t},expression:"system"}}),t("a-button",{attrs:{type:"primary"},on:{click:function(t){return e.addIP("system",e.system)}}},[e._v(" 添加地址 ")])],1),t("h4",{staticClass:"ip-list-title"},[e._v("工装地址列表")]),t("a-list",{staticClass:"ip-list-items",attrs:{bordered:"",dataSource:e.systemIPs},scopedSlots:e._u([{key:"renderItem",fn:function(a,s){return t("a-list-item",{},[t("a-input",{model:{value:e.systemIPs[s],callback:function(t){e.$set(e.systemIPs,s,t)},expression:"systemIPs[index]"}}),t("a-button",{attrs:{type:"link",icon:"delete"},on:{click:function(t){return e.removeIP("system",s)}}},[e._v(" 删除 ")])],1)}}])})],1),t("div",{staticClass:"test-items-section"},[t("h4",{staticClass:"test-items-title"},[e._v("测试项选择")]),t("a-checkbox",{attrs:{indeterminate:e.indeterminateSystem,checked:e.checkAllSystem},on:{change:e.onCheckAllSystemChange}},[e._v(" 全选 ")]),t("a-divider",{staticClass:"divider"}),t("a-checkbox-group",{on:{change:e.onSystemChange},model:{value:e.selectedSystem,callback:function(t){e.selectedSystem=t},expression:"selectedSystem"}},[t("a-row",{attrs:{gutter:[16,16]}},e._l(e.systemTestItems,(function(a){return t("a-col",{key:a.key,attrs:{span:8}},[t("a-checkbox",{attrs:{value:a.key}},[e._v(" "+e._s(a.title)+" ")])],1)})),1)],1)],1),t("a-card",{staticClass:"sub-section-card",attrs:{title:"光伏模块检验参数"}},[t("a-form-item",{attrs:{label:"软件版本:"}},[t("a-input",{attrs:{placeholder:"请输入软件版本"},model:{value:e.softwareVersion,callback:function(t){e.softwareVersion=t},expression:"softwareVersion"}})],1)],1)],1),t("a-card",{staticClass:"section-card",attrs:{title:"整机复试"}},[t("div",{staticClass:"ip-list"},[t("div",{staticClass:"ip-input-group"},[t("a-input",{attrs:{placeholder:"请输入工装地址"},model:{value:e.measurement,callback:function(t){e.measurement=t},expression:"measurement"}}),t("a-button",{attrs:{type:"primary"},on:{click:function(t){return e.addIP("measurement",e.measurement)}}},[e._v(" 添加地址 ")])],1),t("h4",{staticClass:"ip-list-title"},[e._v("工装地址列表")]),t("a-list",{staticClass:"ip-list-items",attrs:{bordered:"",dataSource:e.measurementIPs},scopedSlots:e._u([{key:"renderItem",fn:function(a,s){return t("a-list-item",{},[t("a-input",{model:{value:e.measurementIPs[s],callback:function(t){e.$set(e.measurementIPs,s,t)},expression:"measurementIPs[index]"}}),t("a-button",{attrs:{type:"link",icon:"delete"},on:{click:function(t){return e.removeIP("measurement",s)}}},[e._v(" 删除 ")])],1)}}])})],1),t("div",{staticClass:"test-items-section"},[t("h4",{staticClass:"test-items-title"},[e._v("测试项选择")]),t("a-checkbox",{attrs:{indeterminate:e.indeterminateMeasurement,checked:e.checkAllMeasurement},on:{change:e.onCheckAllMeasurementChange}},[e._v(" 全选 ")]),t("a-divider",{staticClass:"divider"}),t("a-checkbox-group",{on:{change:e.onMeasurementChange},model:{value:e.selectedMeasurement,callback:function(t){e.selectedMeasurement=t},expression:"selectedMeasurement"}},[t("a-row",{attrs:{gutter:[16,16]}},e._l(e.measurementTestItems,(function(a){return t("a-col",{key:a.key,attrs:{span:8}},[t("a-checkbox",{attrs:{value:a.key}},[e._v(" "+e._s(a.title)+" ")])],1)})),1)],1)],1)]),t("div",{staticClass:"footer"},[t("a-button",{attrs:{type:"primary"},on:{click:e.handleSync}},[e._v("同步")])],1)],1)],1)],1)},Te=[],$e=(a(54520),{name:"TestInterface",data(){return{form:this.$form.createForm(this),backendAddress:"",softwareVersion:"",board:"",system:"",measurement:"",boardIPs:[],systemIPs:[],measurementIPs:[],boardTestItems:[],systemTestItems:[],measurementTestItems:[],selectedBoard:[],selectedSystem:[],selectedMeasurement:[],checkAllBoard:!1,indeterminateBoard:!1,checkAllSystem:!1,indeterminateSystem:!1,checkAllMeasurement:!1,indeterminateMeasurement:!1}},created(){this.loadSettings()},mounted(){this.fetchTestItems(),this.fetchSoftwareVersion()},methods:{handleSyncAddress(){const e=`http://${this.backendAddress}`;localStorage.setItem("backendAddress",JSON.stringify(e)),w.defaults.baseURL=e,setTimeout((()=>{this.loadSettings(),this.fetchTestItems(),this.fetchSoftwareVersion(),D().then((e=>{const t={name:e.name,version:e.version};xt.$emit("updateBackendInfo",t)})),X.A.success("已同步完成")}),2e3)},loadSettings(){const e=window.location.host,[t,a]=e.split(":"),s=`${t||"localhost"}:${a||"8080"}`,r=JSON.parse(localStorage.getItem("backendAddress"))||"";this.backendAddress=r.replace("http://","")||s,this.boardIPs=JSON.parse(localStorage.getItem("boardIPs"))||[],this.systemIPs=JSON.parse(localStorage.getItem("systemIPs"))||[],this.measurementIPs=JSON.parse(localStorage.getItem("measurementIPs"))||[],this.selectedBoard=JSON.parse(localStorage.getItem("selectedBoard"))||[],this.selectedSystem=JSON.parse(localStorage.getItem("selectedSystem"))||[],this.selectedMeasurement=JSON.parse(localStorage.getItem("selectedMeasurement"))||[]},fetchTestItems(){R().then((e=>{this.boardTestItems=e.plans[0].items.map((e=>({key:e.item,title:e.name}))),this.systemTestItems=e.plans[1].items.map((e=>({key:e.item,title:e.name}))),this.measurementTestItems=e.plans[2].items.map((e=>({key:e.item,title:e.name}))),0===this.selectedBoard.length&&null===localStorage.getItem("remainingBoardItems")&&(this.selectedBoard=this.boardTestItems.map((e=>e.key))),0===this.selectedSystem.length&&null===localStorage.getItem("remainingSystemItems")&&(this.selectedSystem=this.systemTestItems.map((e=>e.key))),0===this.selectedMeasurement.length&&(this.selectedMeasurement=[]),this.updateCheckAllStatus()})).catch((e=>{console.log(e)}))},fetchSoftwareVersion(){Y().then((e=>{0===e.error_code&&(this.softwareVersion=e.soft_version)}))},addIP(e,t){t?(this[`${e}IPs`].push(t),this[e]=""):X.A.error("请输入内容后再添加地址")},removeIP(e,t){this[`${e}IPs`].splice(t,1)},onCheckAllBoardChange(e){Object.assign(this,{selectedBoard:e.target.checked?this.boardTestItems.map((e=>e.key)):[],indeterminateBoard:!1,checkAllBoard:e.target.checked})},onBoardChange(e){const t=e.length;this.checkAllBoard=t===this.boardTestItems.length,this.indeterminateBoard=t>0&&t<this.boardTestItems.length},onCheckAllSystemChange(e){Object.assign(this,{selectedSystem:e.target.checked?this.systemTestItems.map((e=>e.key)):[],indeterminateSystem:!1,checkAllSystem:e.target.checked})},onSystemChange(e){const t=e.length;this.checkAllSystem=t===this.systemTestItems.length,this.indeterminateSystem=t>0&&t<this.systemTestItems.length},onCheckAllMeasurementChange(e){Object.assign(this,{selectedMeasurement:e.target.checked?this.measurementTestItems.map((e=>e.key)):[],indeterminateMeasurement:!1,checkAllMeasurement:e.target.checked})},onMeasurementChange(e){const t=e.length;this.checkAllMeasurement=t===this.measurementTestItems.length,this.indeterminateMeasurement=t>0&&t<this.measurementTestItems.length},updateCheckAllStatus(){this.onBoardChange(this.selectedBoard),this.onSystemChange(this.selectedSystem),this.onMeasurementChange(this.selectedMeasurement)},handleSync(){localStorage.setItem("selectedBoard",JSON.stringify(this.selectedBoard)),localStorage.setItem("selectedSystem",JSON.stringify(this.selectedSystem)),localStorage.setItem("selectedMeasurement",JSON.stringify(this.selectedMeasurement)),this.selectedBoardItems=this.selectedBoard.map((e=>({key:e,title:this.boardTestItems.find((t=>t.key===e)).title}))),this.selectedSystemItems=this.selectedSystem.map((e=>({key:e,title:this.systemTestItems.find((t=>t.key===e)).title}))),this.selectedMeasurementItems=this.selectedMeasurement.map((e=>({key:e,title:this.measurementTestItems.find((t=>t.key===e)).title})));const e=this.boardTestItems.filter((e=>-1===this.selectedBoard.indexOf(e.key))),t=this.systemTestItems.filter((e=>-1===this.selectedSystem.indexOf(e.key)));localStorage.setItem("remainingBoardItems",JSON.stringify(e)),localStorage.setItem("remainingSystemItems",JSON.stringify(t)),localStorage.setItem("selectedBoardItems",JSON.stringify(this.selectedBoardItems)),localStorage.setItem("selectedSystemItems",JSON.stringify(this.selectedSystemItems)),localStorage.setItem("selectedMeasurementItems",JSON.stringify(this.selectedMeasurementItems)),localStorage.setItem("boardIPs",JSON.stringify(this.boardIPs)),localStorage.setItem("systemIPs",JSON.stringify(this.systemIPs)),localStorage.setItem("measurementIPs",JSON.stringify(this.measurementIPs)),setTimeout((()=>{this.loadSettings(),this.fetchTestItems(),this.fetchSoftwareVersion(),q(this.systemIPs,this.softwareVersion).then((e=>{console.log(e)})),X.A.success("已同步完成")}),2e3)}}}),je=$e,Ae=(0,l.A)(je,De,Te,!1,null,"90feee94",null),Ne=Ae.exports,Me=function(){var e=this,t=e._self._c;return t("div",{staticClass:"testing-interface"},[t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{columns:e.columns,"data-source":e.testData,pagination:!1,rowKey:e=>e.key,scroll:{x:800}},scopedSlots:e._u([{key:"barcode",fn:function(a,s){return[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],class:e.getBarcodeInputClass(s),attrs:{maxLength:22,placeholder:"请输入条码",disabled:s.isTesting},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleBarcodeSubmit(s)}},model:{value:s.barcode,callback:function(t){e.$set(s,"barcode",t)},expression:"record.barcode"}})]}},{key:"device",fn:function(a,s){return[t("div",{staticClass:"device-info"},[t("div",{staticClass:"device-id"},[e._v(e._s(s.device))]),t("div",{staticClass:"device-version"},[t("span",{staticClass:"position"},[e._v(e._s(s.name)+"/")]),t("span",{staticClass:"version"},[e._v("v"+e._s(s.version))])])])]}},{key:"time",fn:function(a,s){return[t("span",{class:{"time-testing":s.isTesting,"time-success":"success"===s.testStatus,"time-error":"fail"===s.testStatus}},[e._v(e._s(s.time))])]}},e._l(e.testItems,(function(a){return{key:a.key,fn:function(s,r){return[t("a-tag",{key:a.key,staticStyle:{width:"50px"},attrs:{color:e.getStatusColor(r[a.key])}},[e._v(" "+e._s(e.getStatusText(r[a.key]))+" ")])]}}}))],null,!0)})],1)],1)},Be=[],Ee={name:"TestingInterface",data(){return{testItems:[],columns:[{title:"工装",dataIndex:"device",width:150,scopedSlots:{customRender:"device"}},{title:"时间",dataIndex:"time",width:80,scopedSlots:{customRender:"time"}},{title:"条码",dataIndex:"barcode",width:180,scopedSlots:{customRender:"barcode"}}],testData:[],websockets:[],timers:{},workbenchAddresses:[],socketMap:[],submissionStatus:{}}},created(){const e=localStorage.getItem("selectedMeasurementItems");e&&(this.testItems=JSON.parse(e).map((e=>({key:e.key,title:e.title}))),this.testItems.forEach((e=>{this.columns.push({title:e.title,dataIndex:e.key,width:150,scopedSlots:{customRender:e.key}})})))},methods:{validateBarcode(e){const t=/^\d{22}$/;return t.test(e)},updateSubmissionStatus(e,t){this.$set(this.submissionStatus,e,t),setTimeout((()=>{this.$set(this.submissionStatus,e,null)}),2e3)},getBarcodeInputClass(e){return{"input-success":"success"===this.submissionStatus[e.key],"input-error":"error"===this.submissionStatus[e.key]}},handleBarcodeSubmit(e){if(!window.isDirectiveEnter)return this.validateBarcode(e.barcode)?void(e.isTesting?X.A.warning("测试进行中，请等待测试完成"):this.websockets.forEach(((t,a)=>{const s=this.socketMap.findIndex((t=>t===e.device));t.readyState===WebSocket.OPEN&&a===s&&(t.send(JSON.stringify({type:"boardCode",planType:"rewhole",device:e.name,barcode:e.barcode})),this.updateSubmissionStatus(e.key,"success"))}))):(X.A.error("条码必须为22位纯数字"),void this.updateSubmissionStatus(e.key,"error"));window.isDirectiveEnter=!1},getStatusColor(e){const t={success:"#00FF99",fail:"#FF3333",testing:"#66CCFF",waiting:"#999999"};return t[e]||t.waiting},getStatusText(e){const t={success:"成功",fail:"失败",testing:"测试中",waiting:"未测试"};return t[e]||t.waiting},initWebSocket(){const e=localStorage.getItem("measurementIPs");this.workbenchAddresses=JSON.parse(e).map((e=>`ws://${e}/echo`)),this.workbenchAddresses.forEach((e=>{let t,a=new WebSocket(e),s=2e3;const r=1.5;let o=!1;const i=()=>{if(a.readyState!==WebSocket.OPEN){if(s*=r,s>1e4)return console.error(`WebSocket连接超时（地址：${e}）`),X.A.error(`与工装（地址：${e}）的连接超时，请检查网络`),void a.close();console.warn(`WebSocket连接未建立，正在延长超时等待时间至${s}毫秒（地址：${e}）`),X.A.warning(`与工装（地址：${e}）的连接较慢，正在尝试继续等待...`),t=setTimeout(i,s)}else o||(a.send(JSON.stringify({type:"devicePlan",planType:"rewhole",items:this.testItems.map((e=>e.key))})),o=!0),clearTimeout(t)};a.onopen=()=>{console.log(`WebSocket连接已建立，地址：${e}`),X.A.success(`WebSocket连接已建立，地址：${e}`),o||(a.send(JSON.stringify({type:"devicePlan",planType:"rewhole",items:this.testItems.map((e=>e.key))})),o=!0)},a.onerror=t=>{console.error(`WebSocket错误（地址：${e}）:`,t),X.A.error(`与工装（地址：${e}）的连接出现错误，请检查网络`)},a.onclose=()=>{console.log(`WebSocket连接已断开，地址：${e}`),X.A.warning(`WebSocket连接已断开，地址：${e}`)},a.onmessage=this.handleWebSocketMessage,t=setTimeout(i,s),this.websockets.push(a)}))},handleWebSocketMessage(e){const t=JSON.parse(e.data);if(console.log(t),"startTest"!==t.type)switch(t.type){case"devicePlan":0===t.error_code?X.A.success("测试方案已配置成功"):X.A.warning(`${t.message}`);break;case"deviceInfo":this.updateTestDataInfo(t);break;case"finishTest":case"failTest":this.updateTestStatus(t);break;case"startItem":case"finishItem":case"failItem":this.updateTestItemStatus(t);break;case"boardCode":this.handleBarcodeResponse(t);break;default:console.warn("未知的消息类型:",t.type)}else this.updateTestStatus(t)},handleBarcodeResponse(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&(0===e.error_code?(this.updateSubmissionStatus(t.key,"success"),t.testStatus=null,t.isTesting=!1,t.time=0):(this.updateSubmissionStatus(t.key,"error"),X.A.error(`条码提交失败: ${e.message}`)),this.$forceUpdate())},updateTestDataInfo(e){const t=e.devices.map(((t,a)=>{const s={key:`${e.name}-${t}-${a}`,name:t,device:e.name,time:0,barcode:"",version:e.version};return this.testItems.forEach((e=>{s[e.key]=null})),s}));this.testData=this.testData.concat(t),this.socketMap.push(e.name)},updateTestStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startTest"===e.type?(t.isTesting=!0,t.testStatus="testing",this.startTimer(e),this.$forceUpdate()):(t.isTesting=!1,t.testStatus="finishTest"===e.type?"success":"fail",this.$set(t,"testStatus",t.testStatus),this.stopTimer(e),setTimeout((()=>{"finishTest"===e.type&&(t.barcode=""),this.$set(t,"barcode",t.barcode)}),2e3),this.$forceUpdate()))},updateTestItemStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startItem"===e.type?(t[e.item]="testing",this.testData[t.key]=t):"finishItem"===e.type?(t[e.item]="success",this.testData[t.key]=t):(t[e.item]="fail",this.testData[t.key]=t))},startTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));if(t&&!this.timers[t.key]){let e=0;this.timers[t.key]=setInterval((()=>{e++,t.time=this.formatTime(e),this.testData[t.key].time=t.time}),1e3)}},stopTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&this.timers[t.key]&&(clearInterval(this.timers[t.key]),delete this.timers[t.key])},formatTime(e){const t=Math.floor(e/60),a=e%60;return`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}},mounted(){setTimeout((()=>{this.initWebSocket()}),1e3)},beforeDestroy(){Object.keys(this.timers).forEach((e=>{clearInterval(this.timers[e])})),this.websockets.forEach((e=>{e&&e.close()}))}},Fe=Ee,Pe=(0,l.A)(Fe,Me,Be,!1,null,"19f0c960",null),Oe=Pe.exports,Ve=function(){var e=this,t=e._self._c;return t("div",{staticClass:"testing-interface"},[t("a-card",{attrs:{bordered:!1}},[t("a-table",{attrs:{columns:e.columns,"data-source":e.testData,pagination:!1,rowKey:e=>e.key,scroll:{x:800}},scopedSlots:e._u([{key:"barcode",fn:function(a,s){return[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],class:e.getBarcodeInputClass(s),attrs:{maxLength:22,placeholder:"请输入条码",disabled:s.isTesting},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleBarcodeSubmit(s)}},model:{value:s.barcode,callback:function(t){e.$set(s,"barcode",t)},expression:"record.barcode"}})]}},{key:"device",fn:function(a,s){return[t("div",{staticClass:"device-info"},[t("div",{staticClass:"device-id"},[e._v(e._s(s.device))]),t("div",{staticClass:"device-version"},[t("span",{staticClass:"position"},[e._v(e._s(s.name)+"/")]),t("span",{staticClass:"version"},[e._v("v"+e._s(s.version))])])])]}},{key:"time",fn:function(a,s){return[t("span",{class:{"time-testing":s.isTesting,"time-success":"success"===s.testStatus,"time-error":"fail"===s.testStatus}},[e._v(e._s(s.time))])]}},e._l(e.testItems,(function(a){return{key:a.key,fn:function(s,r){return[t("a-tag",{key:a.key,staticStyle:{width:"50px"},attrs:{color:e.getStatusColor(r[a.key])}},[e._v(" "+e._s(e.getStatusText(r[a.key]))+" ")])]}}}))],null,!0)})],1)],1)},Le=[],Ye={name:"TestingInterface",data(){return{testItems:[],columns:[{title:"工装",dataIndex:"device",width:150,scopedSlots:{customRender:"device"}},{title:"时间",dataIndex:"time",width:80,scopedSlots:{customRender:"time"}},{title:"条码",dataIndex:"barcode",width:180,scopedSlots:{customRender:"barcode"}}],testData:[],websockets:[],timers:{},workbenchAddresses:[],socketMap:[],submissionStatus:{}}},created(){const e=localStorage.getItem("selectedSystemItems");e&&(this.testItems=JSON.parse(e).map((e=>({key:e.key,title:e.title}))),this.testItems.forEach((e=>{this.columns.push({title:e.title,dataIndex:e.key,width:150,scopedSlots:{customRender:e.key}})})))},methods:{validateBarcode(e){const t=/^\d{22}$/;return t.test(e)},updateSubmissionStatus(e,t){this.$set(this.submissionStatus,e,t),setTimeout((()=>{this.$set(this.submissionStatus,e,null)}),2e3)},getBarcodeInputClass(e){return{"input-success":"success"===this.submissionStatus[e.key],"input-error":"error"===this.submissionStatus[e.key]}},handleBarcodeSubmit(e){if(!window.isDirectiveEnter)return this.validateBarcode(e.barcode)?void(e.isTesting?X.A.warning("测试进行中，请等待测试完成"):this.websockets.forEach(((t,a)=>{const s=this.socketMap.findIndex((t=>t===e.device));t.readyState===WebSocket.OPEN&&a===s&&(t.send(JSON.stringify({type:"boardCode",planType:"whole",device:e.name,barcode:e.barcode})),this.updateSubmissionStatus(e.key,"success"))}))):(X.A.error("条码必须为22位纯数字"),void this.updateSubmissionStatus(e.key,"error"));window.isDirectiveEnter=!1},getStatusColor(e){const t={success:"#00FF99",fail:"#FF3333",testing:"#66CCFF",waiting:"#999999"};return t[e]||t.waiting},getStatusText(e){const t={success:"成功",fail:"失败",testing:"测试中",waiting:"未测试"};return t[e]||t.waiting},initWebSocket(){const e=localStorage.getItem("systemIPs");this.workbenchAddresses=JSON.parse(e).map((e=>`ws://${e}/echo`)),this.workbenchAddresses.forEach((e=>{let t,a=new WebSocket(e),s=2e3;const r=1.5;let o=!1;const i=()=>{if(a.readyState!==WebSocket.OPEN){if(s*=r,s>1e4)return console.error(`WebSocket连接超时（地址：${e}）`),X.A.error(`与工装（地址：${e}）的连接超时，请检查网络`),void a.close();console.warn(`WebSocket连接未建立，正在延长超时等待时间至${s}毫秒（地址：${e}）`),X.A.warning(`与工装（地址：${e}）的连接较慢，正在尝试继续等待...`),t=setTimeout(i,s)}else o||(a.send(JSON.stringify({type:"devicePlan",planType:"whole",items:this.testItems.map((e=>e.key))})),o=!0),clearTimeout(t)};a.onopen=()=>{console.log(`WebSocket连接已建立，地址：${e}`),X.A.success(`WebSocket连接已建立，地址：${e}`),o||(a.send(JSON.stringify({type:"devicePlan",planType:"whole",items:this.testItems.map((e=>e.key))})),o=!0)},a.onerror=t=>{console.error(`WebSocket错误（地址：${e}）:`,t),X.A.error(`与工装（地址：${e}）的连接出现错误，请检查网络`)},a.onclose=()=>{console.log(`WebSocket连接已断开，地址：${e}`),X.A.warning(`WebSocket连接已断开，地址：${e}`)},a.onmessage=this.handleWebSocketMessage,t=setTimeout(i,s),this.websockets.push(a)}))},handleWebSocketMessage(e){const t=JSON.parse(e.data);if(console.log(t),"startTest"!==t.type)switch(t.type){case"devicePlan":0===t.error_code?X.A.success("测试方案已配置成功"):X.A.warning(`${t.message}`);break;case"deviceInfo":this.updateTestDataInfo(t);break;case"finishTest":case"failTest":this.updateTestStatus(t);break;case"startItem":case"finishItem":case"failItem":this.updateTestItemStatus(t);break;case"boardCode":this.handleBarcodeResponse(t);break;default:console.warn("未知的消息类型:",t.type)}else this.updateTestStatus(t)},handleBarcodeResponse(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&(0===e.error_code?(this.updateSubmissionStatus(t.key,"success"),t.testStatus=null,t.isTesting=!1,t.time=0):(this.updateSubmissionStatus(t.key,"error"),X.A.error(`条码提交失败: ${e.message}`)),this.$forceUpdate())},updateTestDataInfo(e){const t=e.devices.map(((t,a)=>{const s={key:`${e.name}-${t}-${a}`,name:t,device:e.name,time:0,barcode:"",version:e.version};return this.testItems.forEach((e=>{s[e.key]=null})),s}));this.testData=this.testData.concat(t),this.socketMap.push(e.name)},updateTestStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startTest"===e.type?(t.isTesting=!0,t.testStatus="testing",this.startTimer(e),this.$forceUpdate()):(t.isTesting=!1,t.testStatus="finishTest"===e.type?"success":"fail",this.$set(t,"testStatus",t.testStatus),this.stopTimer(e),setTimeout((()=>{"finishTest"===e.type&&(t.barcode=""),this.$set(t,"barcode",t.barcode)}),2e3),this.$forceUpdate()))},updateTestItemStatus(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&("startItem"===e.type?(t[e.item]="testing",this.testData[t.key]=t):"finishItem"===e.type?(t[e.item]="success",this.testData[t.key]=t):(t[e.item]="fail",this.testData[t.key]=t))},startTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));if(t&&!this.timers[t.key]){let e=0;this.timers[t.key]=setInterval((()=>{e++,t.time=this.formatTime(e),this.testData[t.key].time=t.time}),1e3)}},stopTimer(e){const t=this.testData.find((t=>t.name===e.devices&&t.barcode===e.barcode));t&&this.timers[t.key]&&(clearInterval(this.timers[t.key]),delete this.timers[t.key])},formatTime(e){const t=Math.floor(e/60),a=e%60;return`${t.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}},mounted(){setTimeout((()=>{this.initWebSocket()}),1e3)},beforeDestroy(){Object.keys(this.timers).forEach((e=>{clearInterval(this.timers[e])})),this.websockets.forEach((e=>{e&&e.close()}))}},qe=Ye,Re=(0,l.A)(qe,Ve,Le,!1,null,"b7a44aa0",null),ze=Re.exports,We=function(){var e=this,t=e._self._c;return t("div",{staticClass:"form-container"},[t("div",{staticClass:"info-section add-section"},[t("a-form",{attrs:{form:e.addForm,layout:"horizontal"}},[t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"起始资产编码:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["barcode",{rules:[{required:!0,message:"请输入资产编码"},{validator:e.validateAssetCode}]}],expression:"['barcode',{ rules: [{ required: true, message: '请输入资产编码' }, { validator: validateAssetCode }]}]"}],attrs:{placeholder:"只允许输入21或22位10进制值",maxLength:22}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"添加数量:"}},[t("a-input-group",{attrs:{compact:""}},[t("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["count",{rules:[{required:!0,message:"请输入数量"},{validator:e.validateCount}]}],expression:"['count', { rules: [{ required: true, message: '请输入数量' },{ validator: validateCount }] }]"}],attrs:{min:1,max:1e4}})],1)],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"起始通讯地址:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["address",{rules:[{required:!0,message:"请输入通讯地址"},{validator:e.validateNumber}]}],expression:"['address',{ rules: [{ required: true, message: '请输入通讯地址' },{ validator: validateNumber }]}]"}],attrs:{placeholder:"只允许输入12位10进制值",maxLength:12}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"生产日期:"}},[t("a-date-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["produceDate",{initialValue:e.initialDate}],expression:"['produceDate', { initialValue: initialDate }]"}],attrs:{placeholder:"格式: YYYY-MM-DD",locale:e.locale,format:"YYYY-MM-DD"}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-checkbox",{model:{value:e.mode,callback:function(t){e.mode=t},expression:"mode"}},[e._v("上电启用安全模式")])],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"起始表号:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["meter",{rules:[{required:!0,message:"请输入表号"},{validator:e.validateNumber}]}],expression:"['meter',{ rules: [{ required: true, message: '请输入表号' },{ validator: validateNumber }]}]"}],attrs:{placeholder:"只允许输入12位10进制值",maxLength:12}})],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"硬件版本:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["hardVersion"],expression:"['hardVersion']"}],attrs:{placeholder:"最大长度4",maxLength:4}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"厂商代码:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["vendorCode"],expression:"['vendorCode']"}],attrs:{placeholder:"最大长度4",maxLength:4}})],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"硬件日期:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["hardDate",{rules:[{validator:e.validateNumber}]}],expression:"['hardDate', { rules: [{ validator: validateNumber }] }]"}],attrs:{placeholder:"最大长度6",maxLength:6}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"厂商扩展:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["vendorExt"],expression:"['vendorExt']"}],attrs:{placeholder:"最大长度8",maxLength:8}})],1)],1),t("a-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[t("a-button",{style:{width:"120px",height:"40px",fontSize:"16px"},attrs:{type:"primary"},on:{click:e.handleAddSave}},[e._v("保存")])],1)],1)],1)],1),t("a-card",{staticClass:"query-card_",attrs:{bordered:!1}},[t("a-form",{attrs:{form:e.queryForm,layout:"inline"},on:{submit:e.handleQuery}},[t("a-row",{staticStyle:{height:"100px","background-color":"#f0f9eb",display:"flex","align-items":"center"}},[t("a-col",{attrs:{span:4}},[t("a-form-item",{attrs:{label:"资产编码:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["barcode",{rules:[{validator:e.validateNumber}]}],expression:"['barcode',{ rules: [{ validator: validateNumber }]}]"}],attrs:{maxLength:22}})],1)],1),t("a-col",{attrs:{span:4}},[t("a-form-item",{attrs:{label:"通讯地址:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["address",{rules:[{validator:e.validateNumber}]}],expression:"['address',{ rules: [{ validator: validateNumber }]}]"}],attrs:{maxLength:12,inputProps:{style:{width:"500px"}}}})],1)],1),t("a-col",{attrs:{span:3}},[t("a-form-item",{attrs:{label:"表号:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["meter",{rules:[{validator:e.validateNumber}]}],expression:"['meter',{ rules: [{ validator: validateNumber }]}]"}],attrs:{maxLength:12}})],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",{attrs:{label:"硬件版本:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["hardVersion"],expression:"['hardVersion']"}],attrs:{maxLength:4}})],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",{attrs:{label:"硬件日期:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["hardDate",{rules:[{validator:e.validateNumber}]}],expression:"['hardDate', { rules: [{ validator: validateNumber }] }]"}],attrs:{maxLength:6}})],1)],1),t("a-col",{attrs:{span:3}},[t("a-form-item",{attrs:{label:"生产日期:"}},[t("a-date-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["produceDate"],expression:"['produceDate']"}],attrs:{placeholder:"选择日期",locale:e.locale,format:"YYYY-MM-DD"}})],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",{attrs:{label:"厂商代码:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["vendorCode"],expression:"['vendorCode']"}],attrs:{maxLength:4}})],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",{attrs:{label:"厂商扩展:"}},[t("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["vendorExt"],expression:"['vendorExt']"}],attrs:{maxLength:8}})],1)],1),t("a-col",{attrs:{span:2}},[t("a-form-item",{attrs:{label:"操作:"}},[t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"search"},on:{click:e.handleQuery}},[e._v("查询")])],1)],1)],1)],1),t("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"key",columns:e.columns,dataSource:e.tableData,pagination:e.pagination},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"mode",fn:function(a){return[t("a-tag",{attrs:{color:a?"green":"red"}},[e._v(" "+e._s(a?"已启用":"未启用")+" ")])]}},{key:"operation",fn:function(a,s){return[t("a-button",{staticClass:"update-button",attrs:{type:"primary",icon:"edit"},on:{click:function(t){return e.handleEdit(s)}}},[e._v("更新 ")])]}}])})],1),t("UpdateNumberInfo",{ref:"UpdateNumberInfo",attrs:{initialbarcode:e.barcode,initialaddress:e.address,initialmeter:e.meter,initialhardVersion:e.hardVersion,initialhardDate:e.hardDate,initialproduceDate:e.produceDate,initialvendorCode:e.vendorCode,initialvendorExt:e.vendorExt,initialmode:e.isMode}})],1)},Ue=[],Je=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"更新写号信息",visible:e.visible,"confirm-loading":e.confirmLoading,footer:null,width:"1000px",height:"1000px"},on:{ok:e.handleOk,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.loading}},[t("div",{staticClass:"info-section update-section"},[t("a-form",{attrs:{form:e.updateForm,layout:"horizontal"}},[t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"资产编码:","validate-status":e.errors.barcode?"error":"",help:e.errors.barcode}},[t("a-input",{attrs:{placeholder:"只允许输入21或22位10进制值",required:"",maxLength:22,disabled:!0},on:{blur:function(t){return e.validateInput("barcode",[21,22])}},model:{value:e.localbarcode,callback:function(t){e.localbarcode=t},expression:"localbarcode"}})],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"通讯地址:","validate-status":e.errors.address?"error":"",help:e.errors.address}},[t("a-input",{attrs:{placeholder:"只允许输入12位10进制值",maxLength:12},on:{blur:function(t){return e.validateInput("address",12)}},model:{value:e.localaddress,callback:function(t){e.localaddress=t},expression:"localaddress"}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"生产日期:","validate-status":e.errors.produceDate?"error":"",help:e.errors.produceDate}},[t("a-date-picker",{attrs:{placeholder:"格式: YYYY-MM-DD"},on:{change:e.validateDate},model:{value:e.localproduceDate,callback:function(t){e.localproduceDate=t},expression:"localproduceDate"}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-checkbox",{model:{value:e.localmode,callback:function(t){e.localmode=t},expression:"localmode"}},[e._v("上电启用安全模式")])],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"表号:","validate-status":e.errors.meter?"error":"",help:e.errors.meter}},[t("a-input",{attrs:{placeholder:"只允许输入12位10进制值",maxLength:12},on:{blur:function(t){return e.validateInput("meter",12)}},model:{value:e.localmeter,callback:function(t){e.localmeter=t},expression:"localmeter"}})],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"硬件版本:","validate-status":e.errors.hardVersion?"error":"",help:e.errors.hardVersion}},[t("a-input",{attrs:{placeholder:"最大长度4",maxLength:4},on:{blur:function(t){return e.validateInput("hardVersion",4,!1)}},model:{value:e.localhardVersion,callback:function(t){e.localhardVersion=t},expression:"localhardVersion"}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"厂商代码:","validate-status":e.errors.vendorCode?"error":"",help:e.errors.vendorCode}},[t("a-input",{attrs:{placeholder:"最大长度4",maxLength:4},on:{blur:function(t){return e.validateInput("vendorCode",4,!1)}},model:{value:e.localvendorCode,callback:function(t){e.localvendorCode=t},expression:"localvendorCode"}})],1)],1)],1),t("a-row",{attrs:{gutter:24}},[t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"硬件日期:","validate-status":e.errors.hardDate?"error":"",help:e.errors.hardDate}},[t("a-input",{attrs:{placeholder:"最大长度6",maxLength:6},on:{blur:function(t){return e.validateInput("hardDate",6,!1)}},model:{value:e.localhardDate,callback:function(t){e.localhardDate=t},expression:"localhardDate"}})],1)],1),t("a-col",{attrs:{span:8}},[t("a-form-item",{staticStyle:{display:"flex","align-items":"center",gap:"8px"},attrs:{label:"厂商扩展:","validate-status":e.errors.vendorExt?"error":"",help:e.errors.vendorExt}},[t("a-input",{attrs:{placeholder:"最大长度8",maxLength:8},on:{blur:function(t){return e.validateInput("vendorExt",8,!1)}},model:{value:e.localvendorExt,callback:function(t){e.localvendorExt=t},expression:"localvendorExt"}})],1)],1),t("a-col",{staticStyle:{"text-align":"right"},attrs:{span:8}},[t("a-button",{style:{width:"120px",height:"40px",fontSize:"16px"},attrs:{type:"primary"},on:{click:e.handleUpdateSave}},[e._v(" 保存 ")])],1)],1)],1)],1)])],1)],1)},Qe=[],He=(a(37550),a(49148)),Ke=a.n(He),Ge={name:"UpdateDeviceInfo",props:{initialbarcode:{type:String,required:!0},initialaddress:{type:String,required:!0},initialmeter:{type:String,required:!0},initialhardVersion:{type:String,required:!0},initialhardDate:{type:String,required:!0},initialproduceDate:{type:String,required:!0},initialvendorCode:{type:String,required:!0},initialvendorExt:{type:String,required:!0},initialmode:{type:Boolean,required:!0}},data(){return{localbarcode:this.initialbarcode,localaddress:this.initialaddress,localmeter:this.initialmeter,localhardVersion:this.initialhardVersion,localhardDate:this.initialhardDate,localproduceDate:Ke()(this.initialproduceDate),localvendorCode:this.initialvendorCode,localvendorExt:this.initialvendorExt,localmode:this.initialmode,updateForm:this.$form.createForm(this),visible:!1,confirmLoading:!1,loading:!1,errors:{barcode:"",address:"",meter:"",hardVersion:"",hardDate:"",produceDate:"",vendorCode:"",vendorExt:""}}},created(){const e=["barcode","address","meter","hardVersion","hardDate","produceDate","vendorCode","vendorExt","isMode"];e.forEach((e=>{this.$parent.$on(`initial-${e}-updated`,(t=>{const a="isMode"===e?"mode":e;this[`local${a}`]="produceDate"===e?Ke()(t):t}))}))},methods:{validateInput(e,t,a=!0){const s=this[`local${e}`];s?a&&!/^\d+$/.test(s)?this.errors[e]="只允许输入数字":Array.isArray(t)?t.includes(s.length)?this.errors[e]="":this.errors[e]=`请输入${t.join("或")}位数字`:s.length!==t&&a?this.errors[e]=`请输入${t}位数字`:s.length>t&&!a?this.errors[e]=`最大长度为${t}`:this.errors[e]="":"barcode"===e&&(this.errors[e]="此字段不能为空")},validateDate(){this.localproduceDate?this.errors.produceDate="":this.errors.produceDate="请选择日期"},validateAllFields(){this.validateInput("barcode",[21,22]),this.validateInput("address",12),this.validateInput("meter",12),this.validateInput("hardVersion",4,!1),this.validateInput("hardDate",6,!1),this.validateInput("vendorCode",4,!1),this.validateInput("vendorExt",8,!1),this.validateDate()},handleUpdateSave(){if(this.validateAllFields(),Object.values(this.errors).some((e=>""!==e)))return void this.$message.error("请修正输入错误后再保存");if(!this.localbarcode)return void this.$message.error("资产编码为必填项");const e=this.localproduceDate.format("YYYY-MM-DD");O(this.localbarcode,this.localaddress,this.localmeter,this.localhardVersion,this.localhardDate,e,this.localvendorCode,this.localvendorExt,this.localmode).then((e=>{console.log(e),0===e.error_code&&(this.$message.success({content:"更新成功",duration:3}),this.$emit("child-message",e.error_code),this.handleCancel())})).catch((e=>{g.A.error({message:"系统提示",description:e.response.data.message,duration:4})}))},showModal(){this.visible=!0},handleCancel(){this.visible=!1,this.updateForm.resetFields(),Object.keys(this.errors).forEach((e=>this.errors[e]=""))},handleOk(){this.updateForm.submit()}}},Xe=Ge,Ze=(0,l.A)(Xe,Je,Qe,!1,null,"1faae1f8",null),et=Ze.exports,tt={components:{UpdateNumberInfo:et},data(){return{initialDate:null,barcode:"",address:"",meter:"",hardVersion:"",hardDate:"",produceDate:"",vendorCode:"",vendorExt:"",isMode:!1,locale:{lang:{placeholder:"请选择日期",yearPlaceholder:"年",monthPlaceholder:"月",dayPlaceholder:"日",weekPlaceholder:"周",rangePlaceholder:["开始日期","结束日期"],today:"今天",now:"此刻",ok:"确定",clear:"清除",prevYear:"去年",nextYear:"明年",prevMonth:"上月",nextMonth:"下月",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",yearFormat:"YYYY年",monthFormat:"MM月",dateFormat:"YYYY-MM-DD",dayFormat:"D日",dateTimeFormat:"YYYY-MM-DD HH:mm:ss",timeFormat:"HH:mm:ss",secondFormat:"ss秒",meridiem:"上午/下午",am:"上午",pm:"下午"},timePickerLocale:{placeholder:"请选择时间"}},power:"",board:"",whole:"",queryForm:this.$form.createForm(this),addForm:this.$form.createForm(this),mode:!1,columns:[{title:"资产条码",dataIndex:"assetCode",width:180},{title:"通讯地址",dataIndex:"address",width:180},{title:"表号",dataIndex:"meterNo",width:120},{title:"硬件版本",dataIndex:"hardwareVersion",width:100},{title:"硬件日期",dataIndex:"hardwareDate",width:100},{title:"生产日期",dataIndex:"productionDate",width:120},{title:"厂商代码",dataIndex:"manufacturerCode",width:100},{title:"厂商扩展",dataIndex:"manufacturerExt",width:100},{title:"安全模式",dataIndex:"mode",scopedSlots:{customRender:"mode"},width:100},{title:"操作",key:"operation",scopedSlots:{customRender:"operation"},width:100}],tableData:[],pagination:{current:1,pageSize:10,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>t[0]+"-"+t[1]+" 共"+e+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},myMoment:""}},mounted(){this.initialDate=this.getValidInitialDate(),this.fetchData()},methods:{getValidInitialDate(){let e=Ke()().format("YYYY-MM-DD"),t=Ke()(e,"YYYY-MM-DD");return t.isValid()?t:null},fetchData(){this.queryForm.validateFields(((e,t)=>{if(!e){let e;void 0!==t.produceDate&&null!==t.produceDate&&(this.myMoment=t.produceDate,e=this.myMoment.format("YYYY-MM-DD")),F(t.barcode,t.address,t.meter,t.hardVersion,t.hardDate,e,t.vendorCode,t.vendorExt,this.pagination.current,this.pagination.pageSize).then((e=>{console.log(e),this.pagination.total=e.page_count*this.pagination.pageSize,this.tableData=e.records.map(((e,t)=>({key:t,assetCode:e.barcode,address:e.address,meterNo:e.meter,hardwareVersion:e.hardVersion,hardwareDate:e.hardDate,productionDate:e.produceDate,manufacturerCode:e.vendorCode,manufacturerExt:e.vendorExt,mode:e.mode})))}))}}))},handleTableChange(e){this.pagination=e,this.fetchData()},validateCount(e,t,a){if(void 0===t||null===t||""===t)return void a();const s=Number(t);isNaN(s)?a(new Error("请输入数字")):s%1===0?/^\d+$/.test(s.toString())?a():a(new Error("请输入纯数字，不能包含其他字符")):a(new Error("请输入整数，不能输入小数"))},validateAssetCode(e,t,a){if(!t)return void a();const s=Number(t);Number.isNaN(s)?a("请输入有效的 10 进制数字"):21!==t.length&&22!==t.length?a("只能输入 21 或 22 位数字"):a()},validateNumber(e,t,a){if("barcode"===e.field){if(!t)return void a();const e=Number(t);Number.isNaN(e)?a("请输入有效的 10 进制数字"):22!==t.toString().length?a("只能输入22 位数字"):a()}else if("address"===e.field||"meter"===e.field){if(!t)return void a();const e=Number(t);Number.isNaN(e)?a("请输入有效的 10 进制数字"):12!==t.toString().length?a("只能输入 12 位数字"):a()}else if("hardDate"===e.field){if(!t)return void a();const e=Number(t);Number.isNaN(e)?a("请输入有效的 10 进制数字"):t.toString().length>6?a("最大长度 6 位"):a()}else a()},handleQuery(e){e.preventDefault(),this.fetchData()},handleAddSave(){this.addForm.validateFields(((e,t)=>{this.myMoment=t.produceDate;const a=this.myMoment.format("YYYY-MM-DD");console.log(a),e||P(t.barcode,t.address,t.meter,t.hardVersion,t.hardDate,a,t.vendorCode,t.vendorExt,this.mode,t.count).then((e=>{console.log(e),0===e.error_code?this.$message.success("保存成功"):this.$message.error(e.message)}))}))},handleEdit(e){try{this.barcode=e.assetCode,this.address=e.address,this.meter=e.meterNo,this.hardVersion=e.hardwareVersion,this.hardDate=e.hardwareDate,this.produceDate=e.productionDate,console.log(this.produceDate),this.vendorCode=e.manufacturerCode,this.vendorExt=e.manufacturerExt,this.isMode=e.mode,console.log(this.power),this.$emit("initial-barcode-updated",this.barcode),this.$emit("initial-address-updated",this.address),this.$emit("initial-meter-updated",this.meter),this.$emit("initial-hardVersion-updated",this.hardVersion),this.$emit("initial-hardDate-updated",this.hardDate),this.$emit("initial-produceDate-updated",this.produceDate),this.$emit("initial-vendorCode-updated",this.vendorCode),this.$emit("initial-vendorExt-updated",this.vendorExt),this.$emit("initial-isMode-updated",this.isMode),this.$refs.UpdateNumberInfo.visible=!0}catch(t){console.error("Error fetching initial status:",t)}}}},at=tt,st=(0,l.A)(at,We,Ue,!1,null,"4590a4a1",null),rt=st.exports,ot=function(){var e=this,t=e._self._c;return t("div",{staticClass:"binding-container"},[t("a-card",{staticClass:"binding-form",attrs:{bordered:!1}},[t("a-form",{attrs:{form:e.form,layout:"vertical"}},[t("a-row",{attrs:{gutter:16}},[t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"电源板条码："}},[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],attrs:{placeholder:"请输入电源板条码",maxLength:20},on:{blur:function(t){return e.checkStatus("power")}},model:{value:e.formData.powerCode,callback:function(t){e.$set(e.formData,"powerCode",t)},expression:"formData.powerCode"}}),e.errorMessage.power&&""!==this.formData.powerCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.errorMessage.power))]):e._e(),e.status.power?t("span",{class:["status-text",e.status.power.type]},[e._v(" "+e._s(e.status.power.message)+" ")]):e._e()],1)],1),t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"主板条码："}},[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],attrs:{placeholder:"请输入主板条码",maxLength:20},on:{blur:function(t){return e.checkStatus("board")}},model:{value:e.formData.boardCode,callback:function(t){e.$set(e.formData,"boardCode",t)},expression:"formData.boardCode"}}),e.errorMessage.board&&""!==this.formData.boardCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.errorMessage.board))]):e._e(),e.status.board?t("span",{class:["status-text",e.status.board.type]},[e._v(" "+e._s(e.status.board.message)+" ")]):e._e()],1)],1),t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"资产条码："}},[t("a-input",{directives:[{name:"enter-next-input",rawName:"v-enter-next-input"}],attrs:{placeholder:"请输入资产条码",maxLength:22},on:{blur:function(t){return e.checkStatus("whole")}},model:{value:e.formData.wholeCode,callback:function(t){e.$set(e.formData,"wholeCode",t)},expression:"formData.wholeCode"}}),e.errorMessage.whole&&""!==this.formData.wholeCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.errorMessage.whole))]):e._e(),e.status.whole?t("span",{class:["status-text",e.status.whole.type]},[e._v(" "+e._s(e.status.whole.message)+" ")]):e._e()],1)],1),t("a-button",{staticStyle:{display:"flex","align-items":"center","margin-top":"29px"},attrs:{type:"primary"},on:{click:e.handleSave}},[e._v("添加")])],1)],1)],1),t("a-card",{staticClass:"search-section",attrs:{bordered:!1}},[t("a-form",{attrs:{layout:"horizontal"}},[t("a-row",{staticStyle:{height:"100px","background-color":"#f0f9eb",display:"flex","align-items":"center"},attrs:{gutter:16}},[t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"电源板条码"}},[t("a-input",{directives:[{name:"enter-input",rawName:"v-enter-input"}],attrs:{placeholder:"请输入查询条码",maxLength:20},on:{blur:function(t){return e.validateNumber("power")}},model:{value:e.searchForm.powerCode,callback:function(t){e.$set(e.searchForm,"powerCode",t)},expression:"searchForm.powerCode"}}),e.error.power&&""!==this.searchForm.powerCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.error.power))]):e._e()],1)],1),t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"主板条码"}},[t("a-input",{directives:[{name:"enter-input",rawName:"v-enter-input"}],attrs:{placeholder:"请输入查询条码",maxLength:20},on:{blur:function(t){return e.validateNumber("board")}},model:{value:e.searchForm.boardCode,callback:function(t){e.$set(e.searchForm,"boardCode",t)},expression:"searchForm.boardCode"}}),e.error.board&&""!==this.searchForm.boardCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.error.board))]):e._e()],1)],1),t("a-col",{attrs:{span:5}},[t("a-form-item",{attrs:{label:"资产条码"}},[t("a-input",{directives:[{name:"enter-input",rawName:"v-enter-input"}],attrs:{placeholder:"请输入查询条码",maxLength:22},on:{blur:function(t){return e.validateNumber("whole")}},model:{value:e.searchForm.wholeCode,callback:function(t){e.$set(e.searchForm,"wholeCode",t)},expression:"searchForm.wholeCode"}}),e.error.whole&&""!==this.searchForm.wholeCode?t("p",{staticStyle:{color:"red"}},[e._v(e._s(e.error.whole))]):e._e()],1)],1),t("a-col",{attrs:{span:1}},[t("a-form-item",{attrs:{label:"操作"}},[t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"search"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1)],1),t("a-table",{staticStyle:{"margin-top":"20px"},attrs:{rowKey:"key",columns:e.columns,"data-source":e.tableData,pagination:e.detailIpagination,rowClassName:(e,t)=>t%2===1?"table-row-light":"table-row-dark"},on:{change:e.detailhandleTableChange},scopedSlots:e._u([{key:"operate",fn:function(a,s){return[t("a-button",{staticClass:"update-button",attrs:{type:"primary",icon:"edit"},on:{click:function(t){return e.handleUpdate(s)}}},[e._v(" 更新 ")]),t("a-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",icon:"delete"},on:{click:function(t){return e.handleDelete(s)}}},[e._v(" 删除 ")])]}}])})],1),t("UpdateAssemblyRelate",{ref:"UpdateAssemblyRelate",attrs:{initialPowerCode:e.power,initialBoardCode:e.board,initialWholeCode:e.whole},on:{"child-message":e.handleChildMessage}}),t("a-modal",{attrs:{title:e.modal.title,okText:e.modal.okText,cancelText:e.modal.cancelText},on:{ok:e.handleModalConfirm},model:{value:e.modal.visible,callback:function(t){e.$set(e.modal,"visible",t)},expression:"modal.visible"}},[t("p",[e._v(e._s(e.modal.content))])])],1)},it=[],nt=function(){var e=this,t=e._self._c;return t("div",[t("a-modal",{attrs:{title:"更新组装关系",visible:e.visible,"confirm-loading":e.confirmLoading,footer:null},on:{ok:e.handleOk,cancel:e.handleCancel}},[t("a-spin",{attrs:{spinning:e.loading}},[t("a-form",{attrs:{form:e.updateForm,layout:"vertical"},on:{submit:e.handleUpdate}},[t("a-row",{attrs:{gutter:16}},[t("a-col",{attrs:{span:24}},[t("a-form-item",{attrs:{label:"电源板条码","validate-status":e.powerBarcodeError?"error":"",help:e.powerBarcodeError||""}},[t("a-input",{attrs:{placeholder:"请输入条码",disabled:e.confirmLoading,maxLength:20},on:{blur:function(t){return e.validateBarcode("power")}},model:{value:e.localPower,callback:function(t){e.localPower=t},expression:"localPower"}})],1)],1),t("a-col",{attrs:{span:24}},[t("a-form-item",{attrs:{label:"主板条码","validate-status":e.boardBarcodeError?"error":"",help:e.boardBarcodeError||""}},[t("a-input",{attrs:{placeholder:"请输入条码",disabled:e.confirmLoading,maxLength:20},on:{blur:function(t){return e.validateBarcode("board")}},model:{value:e.localBoard,callback:function(t){e.localBoard=t},expression:"localBoard"}})],1)],1),t("a-col",{attrs:{span:24}},[t("a-form-item",{attrs:{label:"资产条码","validate-status":e.wholeBarcodeError?"error":"",help:e.wholeBarcodeError||""}},[t("a-input",{attrs:{placeholder:"请输入条码",disabled:!0,maxLength:22},model:{value:e.localWhole,callback:function(t){e.localWhole=t},expression:"localWhole"}})],1)],1),t("a-button",{attrs:{type:"primary","html-type":"submit",icon:"sync",loading:e.confirmLoading,block:""},on:{click:e.handleUpdate}},[e._v(" 更新 ")])],1)],1)],1)],1)],1)},lt=[],dt={name:"UpdateAssemblyRelate",props:{initialPowerCode:{type:String,required:!0},initialBoardCode:{type:String,required:!0},initialWholeCode:{type:String,required:!0}},data(){return{localPower:this.initialPowerCode,localBoard:this.initialBoardCode,localWhole:this.initialWholeCode,updateForm:this.$form.createForm(this),visible:!1,confirmLoading:!1,loading:!1,powerBarcodeError:"",boardBarcodeError:"",wholeBarcodeError:""}},created(){this.$parent.$on("initial-power-updated",(e=>{this.localPower=e})),this.$parent.$on("initial-board-updated",(e=>{this.localBoard=e})),this.$parent.$on("initial-whole-updated",(e=>{this.localWhole=e}))},methods:{validateBarcode(e){const t="power"===e?this.localPower:this.localBoard,a=`${e}BarcodeError`;t?/^\d{4,20}$/.test(t)?this[a]="":this[a]="请输入4-20位纯数字":this[a]="条码不能为空"},showModal(){this.visible=!0},handleCancel(){this.visible=!1,this.updateForm.resetFields(),this.powerBarcodeError="",this.boardBarcodeError="",this.wholeBarcodeError=""},handleUpdate(e){e.preventDefault(),this.validateBarcode("power"),this.validateBarcode("board"),this.powerBarcodeError||this.boardBarcodeError||(this.confirmLoading=!0,B(this.localPower,this.localBoard,this.localWhole).then((e=>{0===e.error_code&&(this.$message.success({content:"更新成功",duration:3}),this.$emit("child-message",e.error_code),this.handleCancel())})).catch((e=>{g.A.error({message:"系统提示",description:e.response.data.message,duration:4})})).finally((()=>{this.confirmLoading=!1})))},handleOk(){this.updateForm.submit()}}},ct=dt,ut=(0,l.A)(ct,nt,lt,!1,null,"72f86485",null),mt=ut.exports,ht={components:{UpdateAssemblyRelate:mt},data(){return{power:"",board:"",whole:"",error:{power:"",board:"",whole:""},errorMessage:{power:"",board:"",whole:""},detailIpagination:{current:1,pageSize:10,pageSizeOptions:["10","20","50","100"],showTotal:(e,t)=>t[0]+"-"+t[1]+" 共"+e+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},form:this.$form.createForm(this),formData:{powerCode:"",boardCode:"",wholeCode:""},status:{power:null,board:null,whole:null},searchForm:{powerCode:"",boardCode:"",wholeCode:""},columns:[{title:"电源板条码",dataIndex:"powerCode",key:"powerCode"},{title:"主板条码",dataIndex:"boardCode",key:"boardCode"},{title:"资产条码",dataIndex:"wholeCode",key:"wholeCode"},{title:"操作",key:"operate",scopedSlots:{customRender:"operate"}}],tableData:[],modal:{visible:!1,title:"",content:"",okText:"确定",cancelText:"取消",callback:null},isEditing:!1,editingRecord:null,result:{power:null,board:null,whole:null}}},mounted(){this.handleSearch()},methods:{detailhandleTableChange(e){this.detailIpagination=e,this.handleSearch()},handleChildMessage(e){0===e&&this.fetchData()},fetchData(){this.handleSearch()},validateNumber(e){const t=this.searchForm[`${e}Code`];t?"whole"===e?/^\d{22}$/.test(t)?this.error[e]="":this.error[e]="请输入22位数字":/^\d{4,20}$/.test(t)?this.error[e]="":this.error[e]="请输入4 ~ 20位数字":this.error[e]=""},async checkStatus(e){const t=this.formData[`${e}Code`];if(t)if("whole"===e)if(/^\d{22}$/.test(t)){this.errorMessage[e]="";try{A(t,e).then((t=>{console.log(t),this.result[e]=t.detail,this.status[e]={type:"已组装"===t.detail?"error":"success",message:t.detail}}))}catch(a){this.status[e]={type:"error",message:"查询失败"}}}else this.status[e]="",this.errorMessage[e]="请输入22位数字";else if(/^\d{4,20}$/.test(t)){this.errorMessage[e]="";try{A(t,e).then((t=>{console.log(t),this.result[e]=t.detail,this.status[e]={type:!0===t.result?"success":"error",message:t.detail}}))}catch(a){this.status[e]={type:"error",message:"查询失败"}}}else this.status[e]="",this.errorMessage[e]="请输入4 ~ 20位数字";else this.errorMessage[e]="请输入内容"},async handleSave(){this.checkStatus("power"),this.checkStatus("board"),this.checkStatus("whole");const e=Object.values(this.errorMessage).some((e=>""!==e));if(e)return void this.$message.error("请输入条码并且修改输入错误后再保存");const t=Object.values(this.status).some((e=>e&&"error"===e.type));t&&("已组装"===this.result.power||"已组装"===this.result.whole||"已组装"===this.result.board)?this.$message.error("存在已组装的设备，无法保存"):this.saveData()},async saveData(){try{M(this.formData.powerCode,this.formData.boardCode,this.formData.wholeCode).then((e=>{console.log(e),0===e.error_code&&(this.$message.success("保存成功"),this.resetForm(),this.tableData=[],N(this.formData.powerCode,this.formData.boardCode,this.formData.wholeCode,this.detailIpagination.current,this.detailIpagination.pageSize).then((e=>{this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize,console.log(e);let t=0;e.records.forEach((e=>{this.tableData.push({key:t++,powerCode:e.powerBarcode,boardCode:e.boardBarcode,wholeCode:e.wholeBarcode})}))})))}))}catch(e){this.$message.error("操作失败")}},async handleSearch(){this.validateNumber("power"),this.validateNumber("board"),this.validateNumber("whole");const e=Object.values(this.error).some((e=>""!==e));if(!e){this.tableData=[];try{const e=await N(this.searchForm.powerCode,this.searchForm.boardCode,this.searchForm.wholeCode,this.detailIpagination.current,this.detailIpagination.pageSize);this.detailIpagination.total=e.page_count*this.detailIpagination.pageSize;let t=0;e.records.forEach((e=>{this.tableData.push({key:t++,powerCode:e.powerBarcode,boardCode:e.boardBarcode,wholeCode:e.wholeBarcode})}))}catch(t){this.$message.error("查询失败")}}},handleUpdate(e){try{this.power=e.powerCode,this.board=e.boardCode,this.whole=e.wholeCode,console.log(this.power),this.$emit("initial-power-updated",this.power),this.$emit("initial-board-updated",this.board),this.$emit("initial-whole-updated",this.whole),this.$refs.UpdateAssemblyRelate.title="更新组装关系",this.$refs.UpdateAssemblyRelate.visible=!0}catch(t){this.$message.error("更新失败")}},handleDelete(e){this.showModal({title:"确认删除",content:"是否确认删除该记录？",callback:()=>this.deleteRecord(e)})},async deleteRecord(e){try{const t={Barcode:e.wholeCode};E(t).then((t=>{console.log(t),this.tableData=this.tableData.filter((t=>t.key!==e.key)),this.$message.success("删除成功"),this.handleSearch()}))}catch(t){this.$message.error("删除失败")}},showModal({title:e,content:t,callback:a}){this.modal={...this.modal,visible:!0,title:e,content:t,callback:a}},handleModalConfirm(){this.modal.visible=!1,this.modal.callback&&this.modal.callback()},resetForm(){this.formData={powerCode:"",boardCode:"",wholeCode:""},this.status={power:null,board:null,whole:null},this.isEditing=!1,this.editingRecord=null}}},pt=ht,ft=(0,l.A)(pt,ot,it,!1,null,"1d6a1794",null),gt=ft.exports,bt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"code-replacement-container"},[t("a-form",{attrs:{form:e.form},on:{submit:e.handleSubmit}},[t("div",{staticClass:"form-header"},[t("div",{staticClass:"input-group"},[t("span",[e._v("原资产编码：")]),t("a-input",{ref:"oldCodeInput",attrs:{maxLength:22,placeholder:"请扫描原资产编码"},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleOldCodeChange.apply(null,arguments)}},model:{value:e.oldCode,callback:function(t){e.oldCode=t},expression:"oldCode"}})],1),t("div",{staticClass:"input-group"},[t("span",[e._v("更换资产编码：")]),t("a-input",{ref:"newCodeInput",attrs:{maxLength:22,placeholder:"请扫描新资产编码",disabled:!e.oldCodeValid},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleNewCodeChange.apply(null,arguments)}},model:{value:e.newCode,callback:function(t){e.newCode=t},expression:"newCode"}})],1),t("a-button",{attrs:{type:"primary",disabled:!e.isSubmitEnabled},on:{click:e.handleSubmit}},[e._v(" 确定 ")])],1),t("div",{staticClass:"info-display"},[t("div",{staticClass:"info-box"},[t("h3",[e._v("待更换资产")]),e.oldAssetInfo?t("div",[t("p",[e._v("资产编码: "+e._s(e.oldAssetInfo.barcode))]),t("p",[e._v("通讯地址: "+e._s(e.oldAssetInfo.address))]),t("p",[e._v("表号: "+e._s(e.oldAssetInfo.meter))]),t("p",[e._v("硬件版本: "+e._s(e.oldAssetInfo.hardVersion))]),t("p",[e._v("硬件日期: "+e._s(e.oldAssetInfo.hardDate))]),t("p",[e._v("生产日期: "+e._s(e.oldAssetInfo.produceDate))]),t("p",[e._v("厂商编码: "+e._s(e.oldAssetInfo.vendorCode))]),t("p",[e._v("厂商扩展: "+e._s(e.oldAssetInfo.vendorExt))]),t("p",[e._v("安全模式: "+e._s(e.oldAssetInfo.mode))])]):e._e()]),t("div",{staticClass:"info-box"},[t("h3",[e._v("更换资产信息")]),e.newAssetInfo?t("div",[t("p",[e._v("资产编码: "+e._s(e.newAssetInfo.barcode))]),t("p",[e._v("通讯地址: "+e._s(e.newAssetInfo.address))]),t("p",[e._v("表号: "+e._s(e.newAssetInfo.meter))]),t("p",[e._v("硬件版本: "+e._s(e.newAssetInfo.hardVersion))]),t("p",[e._v("硬件日期: "+e._s(e.newAssetInfo.hardDate))]),t("p",[e._v("生产日期: "+e._s(e.newAssetInfo.produceDate))]),t("p",[e._v("厂商编码: "+e._s(e.newAssetInfo.vendorCode))]),t("p",[e._v("厂商扩展: "+e._s(e.newAssetInfo.vendorExt))]),t("p",[e._v("安全模式: "+e._s(e.newAssetInfo.mode))])]):e._e()])]),t("a-modal",{attrs:{visible:e.loading,closable:!1,maskClosable:!1,footer:null,centered:""}},[t("div",{staticClass:"loading-content"},[t("a-spin"),t("p",[e._v("正在执行更换操作，预计需要50秒...")]),t("p",[e._v("剩余时间："+e._s(e.remainingTime)+"秒")])],1)])],1)],1)},vt=[],yt={data(){return{form:this.$form.createForm(this),oldCode:"",newCode:"",oldCodeValid:!1,loading:!1,remainingTime:60,timer:null,oldAssetInfo:null,newAssetInfo:null}},computed:{isSubmitEnabled(){return this.oldCodeValid&&this.newCode&&this.oldAssetInfo&&this.newAssetInfo&&!this.loading}},methods:{validateAssetCode(e){return/^\d{22}$/.test(e)},async handleOldCodeChange(){if(!this.validateAssetCode(this.oldCode))return this.$message.error("请输入22位的纯数字"),void(this.oldCodeValid=!1);try{const e=await this.fetchAssetInfo(this.oldCode);e.isUsed?(this.oldAssetInfo=e,this.oldCodeValid=!0,this.$nextTick((()=>{this.$refs.newCodeInput.focus()}))):(this.$message.error("原资产编码未使用"),this.oldCodeValid=!1)}catch(e){this.$message.error("获取资产信息失败"),this.oldCodeValid=!1}},async handleNewCodeChange(){if(this.validateAssetCode(this.newCode))try{const e=await this.fetchAssetInfo(this.newCode);e.isUsed?(this.$message.error("新资产编码已被使用"),this.newAssetInfo=null):this.newAssetInfo=e}catch(e){this.$message.error("获取资产信息失败"),this.newAssetInfo=null}else this.$message.error("请输入22位的纯数字")},async handleSubmit(e){if(e&&e.preventDefault(),this.isSubmitEnabled){this.loading=!0,this.remainingTime=60,this.timer=setInterval((()=>{this.remainingTime--,this.remainingTime<=0&&(clearInterval(this.timer),this.loading=!1,this.$message.error("操作超时"))}),1e3);try{const e=await this.replaceAssetCode({oldCode:this.oldCode,newCode:this.newCode});clearInterval(this.timer),this.loading=!1,e.success?(this.$message.success("更换成功"),this.resetForm()):this.$message.error(e.message||"更换失败")}catch(t){console.error("更换资产编码时出现错误：",t),clearInterval(this.timer),this.loading=!1,this.$message.error("操作失败")}}},resetForm(){this.oldCode="",this.newCode="",this.oldCodeValid=!1,this.oldAssetInfo=null,this.newAssetInfo=null,this.$nextTick((()=>{this.$refs.oldCodeInput.focus()}))},async fetchAssetInfo(e){try{const t=await z(e);return console.log(t),await new Promise((e=>{setTimeout((()=>{e()}),500)})),{barcode:t.barcode,address:t.address,meter:t.meter,hardVersion:t.hardVersion,hardDate:t.hardDate,produceDate:t.produceDate,vendorCode:t.vendorCode,vendorExt:t.vendorExt,mode:t.mode,isUsed:t.used}}catch(t){throw console.error("检查资产时出现错误：",t),t}},async replaceAssetCode(e){try{const t=await W(e.oldCode,e.newCode);return console.log(t),0===t.error_code?{success:!0}:{success:!1,message:t.message||"更换失败"}}catch(t){throw console.error("更换资产编码时出现错误：",t),t}}},beforeDestroy(){this.timer&&clearInterval(this.timer)}},kt=yt,St=(0,l.A)(kt,bt,vt,!1,null,"3d07d92b",null),wt=St.exports;s.Ay.use(m.Ay);const Ct=new m.Ay({mode:"history",base:"/work/",routes:[{path:"/",redirect:"/DataManage",name:"数据管理",component:le},{path:"/Home",component:H,name:"主页",children:[{path:"/BoardLevelTest",name:"板级测试",component:ae},{path:"/DataManage",name:"数据管理",component:le},{path:"/HomePage",name:"首页",component:pe},{path:"/PowerBoardTest",name:"电源板测试",component:_e},{path:"/SystemSetting",name:"系统设置",component:Ne},{path:"/WholeMachineReTest",name:"整机复测",component:Oe},{path:"/WholeMachineTest",name:"整机测试",component:ze},{path:"/WriteNumberConfiguration",name:"写号配置",component:rt},{path:"/CompleteMachineAssembly",name:"整机组装",component:gt},{path:"/ChangeAssetCoding",name:"更换资产编码",component:wt}]}]});var It=Ct;s.Ay.directive("enterNextInput",{inserted:function(e){e.addEventListener("keypress",(function(e){window.isDirectiveEnter=!1,e=e||window.event;let t="number"===typeof e.charCode?e.charCode:e.keyCode;if(13===t){e.preventDefault();var a=document.activeElement;if(a){var s=new Event("keyup");s.keyCode=13,a.dispatchEvent(s)}window.isDirectiveEnter=!0;for(var r=document.getElementsByTagName("input"),o=0;o<r.length;o++)if(r[o]===document.activeElement){if(o===r.length-1)return;return void r[o+1].focus()}}}))}}),s.Ay.directive("enterInput",{inserted:function(e){e.addEventListener("keypress",(function(e){e=e||window.event;let t="number"===typeof e.charCode?e.charCode:e.keyCode;if(13===t){e.preventDefault(),e.stopPropagation();for(var a=document.getElementsByTagName("input"),s=0;s<a.length;s++)if(a[s]===document.activeElement){if(s===a.length-1)return;return void a[s+1].focus()}}}))}});const xt=new s.Ay;s.Ay.use(u.Ay),s.Ay.config.productionTip=!1,new s.Ay({router:It,render:e=>e(c)}).$mount("#app")}},t={};function a(s){var r=t[s];if(void 0!==r)return r.exports;var o=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(o.exports,o,o.exports,a),o.loaded=!0,o.exports}a.m=e,function(){var e=[];a.O=function(t,s,r,o){if(!s){var i=1/0;for(c=0;c<e.length;c++){s=e[c][0],r=e[c][1],o=e[c][2];for(var n=!0,l=0;l<s.length;l++)(!1&o||i>=o)&&Object.keys(a.O).every((function(e){return a.O[e](s[l])}))?s.splice(l--,1):(n=!1,o<i&&(i=o));if(n){e.splice(c--,1);var d=r();void 0!==d&&(t=d)}}return t}o=o||0;for(var c=e.length;c>0&&e[c-1][2]>o;c--)e[c]=e[c-1];e[c]=[s,r,o]}}(),function(){a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,{a:t}),t}}(),function(){a.d=function(e,t){for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){var e={524:0};a.O.j=function(t){return 0===e[t]};var t=function(t,s){var r,o,i=s[0],n=s[1],l=s[2],d=0;if(i.some((function(t){return 0!==e[t]}))){for(r in n)a.o(n,r)&&(a.m[r]=n[r]);if(l)var c=l(a)}for(t&&t(s);d<i.length;d++)o=i[d],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(c)},s=self["webpackChunkant_design_vue"]=self["webpackChunkant_design_vue"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=a.O(void 0,[504],(function(){return a(55927)}));s=a.O(s)})();
//# sourceMappingURL=app.4ed9ed72.js.map