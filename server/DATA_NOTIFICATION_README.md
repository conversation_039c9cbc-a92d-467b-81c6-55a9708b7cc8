# 数据通知机制

本文档描述了在 `TestPlan` 中实现的数据通知机制，该机制允许等待接收到数据的通知，并支持100秒的超时。

## 功能概述

数据通知机制提供以下功能：

1. **等待数据通知**：阻塞等待直到接收到数据或超时（100秒）
2. **发送数据通知**：当接收到数据时触发通知
3. **超时处理**：100秒后自动超时返回
4. **状态检查**：检查是否正在等待数据
5. **资源清理**：清空通知通道和停止等待

## API 接口

### WaitForDataNotification()

等待数据通知，超时时间为100秒。

```go
func (tp *TestPlan) WaitForDataNotification() ([]byte, bool)
```

**返回值：**
- `[]byte`: 接收到的数据（如果超时则为 nil）
- `bool`: 是否超时（true 表示超时，false 表示正常接收到数据）

**示例：**
```go
data, timeout := testPlan.WaitForDataNotification()
if timeout {
    log.Warn().Msg("等待数据超时")
} else {
    log.Info().Str("数据", hex.EncodeToString(data)).Msg("接收到数据")
}
```

### NotifyDataReceived(data []byte)

通知数据已接收。

```go
func (tp *TestPlan) NotifyDataReceived(data []byte)
```

**参数：**
- `data`: 接收到的数据

**示例：**
```go
receivedData := []byte("S1234567890\r\n")
testPlan.NotifyDataReceived(receivedData)
```

### IsWaitingForData()

检查是否正在等待数据。

```go
func (tp *TestPlan) IsWaitingForData() bool
```

**返回值：**
- `bool`: true 表示正在等待数据，false 表示没有在等待

**示例：**
```go
if testPlan.IsWaitingForData() {
    log.Info().Msg("正在等待数据")
}
```

### ClearDataNotifications()

清空数据通知通道中的所有数据。

```go
func (tp *TestPlan) ClearDataNotifications()
```

**示例：**
```go
testPlan.ClearDataNotifications()
```

## 使用场景

### 基本使用

```go
// 创建测试方案
commArgs := &base.CommArgs{
    WorkstationID: 1,
    TestType:      "whole",
}
testPlan := NewTestPlan(commArgs)
defer testPlan.Deinit()

// 在一个协程中等待数据
go func() {
    data, timeout := testPlan.WaitForDataNotification()
    if timeout {
        log.Warn().Msg("等待超时")
    } else {
        log.Info().Str("数据", string(data)).Msg("接收到数据")
    }
}()

// 在另一个地方接收到数据后发送通知
receivedData := []byte("some data")
testPlan.NotifyDataReceived(receivedData)
```

### 集成到现有回调中

数据通知机制已经集成到 `barcodeScan` 回调函数中：

```go
func (tp *TestPlan) barcodeScan(data []byte, newoff int) (ret int) {
    // 触发数据通知
    if newoff < len(data) {
        tp.NotifyDataReceived(data[newoff:])
    }
    
    // 原有的条码处理逻辑...
}
```

### 超时处理

```go
data, timeout := testPlan.WaitForDataNotification()
if timeout {
    // 处理超时情况
    log.Warn().Msg("100秒内未接收到数据")
    return errors.New("数据接收超时")
}

// 处理接收到的数据
processData(data)
```

## 技术实现

### 核心组件

1. **数据通道**：`dataNotifyChan chan []byte` - 缓冲通道，容量为10
2. **等待状态**：`waitingForData bool` - 标记是否正在等待数据
3. **互斥锁**：`dataWaitMutex sync.Mutex` - 保护状态变量的并发访问

### 超时机制

使用 Go 的 `select` 语句和 `time.After` 实现超时：

```go
select {
case data := <-tp.dataNotifyChan:
    return data, false
case <-time.After(100 * time.Second):
    return nil, true
}
```

### 线程安全

- 使用互斥锁保护 `waitingForData` 状态
- 数据复制避免数据竞争
- 非阻塞发送避免死锁

## 注意事项

1. **单一等待者**：同时只能有一个协程等待数据通知
2. **数据复制**：通知的数据会被复制，避免原始数据被修改
3. **通道容量**：数据通知通道有10个缓冲位置，避免发送阻塞
4. **资源清理**：在 `Deinit()` 中会自动清理资源
5. **超时时间**：固定为100秒，不可配置

## 测试

运行测试：

```bash
go test -v ./server -run TestWaitForDataNotification
```

运行所有相关测试：

```bash
go test -v ./server -run "TestWaitForDataNotification|TestNotifyDataReceived|TestClearDataNotifications"
```

运行性能测试：

```bash
go test -bench=BenchmarkWaitForDataNotification ./server
```

## 示例代码

查看 `data_notification_example.go` 文件获取完整的使用示例。

运行示例：

```go
import "pvFactoryBackend/server"

func main() {
    server.RunAllExamples()
}
```
