package server

import (
	"pvFactoryBackend/model/table"
	"strconv"

	"github.com/rs/zerolog/log"
	"github.com/xuri/excelize/v2"
)

type Exporter struct {
	file     *excelize.File
	filename string
}

// 创建excel文件
func (e *Exporter) New(filename string) error {
	e.Save()

	e.filename = filename

	f := excelize.NewFile()
	e.file = f

	// 创建sheet
	index, err := e.file.NewSheet("list")
	if err != nil {
		return err
	}
	f.SetActiveSheet(index)

	// 创建样式
	style, err := e.file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Bold: true,
			Size: 14,
		},
	})
	if err != nil {
		return err
	}

	// 添加表头
	// 条码部分
	err = e.file.MergeCell("list", "A1", "A2")
	if err != nil {
		return err
	}
	err = e.file.SetColWidth("list", "A", "A", 11)
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "A1", "电源板条码")
	if err != nil {
		return err
	}
	err = e.file.MergeCell("list", "B1", "B2")
	if err != nil {
		return err
	}
	err = e.file.SetColWidth("list", "B", "B", 13)
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "B1", "主板条码")
	if err != nil {
		return err
	}
	err = e.file.MergeCell("list", "C1", "C2")
	if err != nil {
		return err
	}
	err = e.file.SetColWidth("list", "C", "C", 24)
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "C1", "资产编码")
	if err != nil {
		return err
	}

	// 电源板部分
	err = e.file.SetCellStr("list", "D1", "电源板")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "D2", "性能")
	if err != nil {
		return err
	}

	// 主板部分
	err = e.file.MergeCell("list", "E1", "P1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "E1", "主板")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "E2", "RS485-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "F2", "指示灯")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "G2", "版本号")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "H2", "Flash")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "I2", "蓝牙")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "J2", "Esam")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "K2", "RS485-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "L2", "扩展1-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "M2", "扩展1-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "N2", "扩展2-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "O2", "扩展2-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "P2", "载波")
	if err != nil {
		return err
	}

	// 整机测试部分
	err = e.file.MergeCell("list", "Q1", "AE1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Q1", "整机测试")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Q2", "RS485-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "R2", "版本号校验")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "S2", "指示灯")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "T2", "设置时间")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "U2", "蓝牙")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "V2", "Esam")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "W2", "RS485-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "X2", "扩展1-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Y2", "扩展1-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Z2", "扩展2-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AA2", "扩展2-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AB2", "载波")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AC2", "写号")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AD2", "检查写号")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AE2", "校准")
	if err != nil {
		return err
	}

	// 整机复测部分
	err = e.file.MergeCell("list", "AF1", "AR1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AF1", "整机复测")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AF2", "RS485-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AG2", "版本号校验")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AH2", "指示灯")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AI2", "蓝牙")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AJ2", "Esam")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AK2", "RS485-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AL2", "扩展1-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AM2", "扩展1-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AN2", "扩展2-1")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AO2", "扩展2-2")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AP2", "载波")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AQ2", "检查写号")
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AR2", "检查时间")
	if err != nil {
		return err
	}

	// 设置表头样式
	err = e.file.SetCellStyle("list", "A1", "AR2", style)
	if err != nil {
		return err
	}

	return nil
}

// 添加行数据
// row: 行号，从0开始
// data: 行数据
func (e *Exporter) AddRow(row int, data map[string]interface{}) error {
	var err error
	// 条码
	err = e.file.SetCellStr("list", "A"+strconv.Itoa(row+3), data["powerBarcode"].(string))
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "B"+strconv.Itoa(row+3), data["boardBarcode"].(string))
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "C"+strconv.Itoa(row+3), data["wholeBarcode"].(string))
	if err != nil {
		return err
	}

	// 主板
	err = e.file.SetCellStr("list", "E"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardTTL])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "F"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardLED])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "G"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardVersion])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "H"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardFlash])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "J"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardESAM])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "K"+strconv.Itoa(row+3), data["boardItem"].(map[string]string)[table.BoardRs485])
	if err != nil {
		return err
	}

	// 整机测试
	err = e.file.SetCellStr("list", "Q"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeRs4851])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "R"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeVersion])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "S"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeLED])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "T"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeSetTime])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "U"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeBT])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "V"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeESAM])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "W"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeRs4852])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "X"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeExt11])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Y"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeExt12])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "Z"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeExt21])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AA"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeExt22])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AB"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeCarrier])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AC"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeSetCode])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AD"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeCheckCode])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AE"+strconv.Itoa(row+3), data["wholeItem"].(map[string]string)[table.WholeCalibration])
	if err != nil {
		return err
	}

	// 整机复测
	err = e.file.SetCellStr("list", "AF"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeRs4851])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AG"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeVersion])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AH"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeLED])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AI"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeBT])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AJ"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeESAM])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AK"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeRs4852])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AL"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeExt11])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AM"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeExt12])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AN"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeExt21])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AO"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeExt22])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AP"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeCarrier])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AQ"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeCheckCode])
	if err != nil {
		return err
	}
	err = e.file.SetCellStr("list", "AR"+strconv.Itoa(row+3), data["rewholeItem"].(map[string]string)[table.RewholeCheckTime])
	if err != nil {
		return err
	}

	return nil
}

// 保存excel文件
func (e *Exporter) Save() error {
	var err error
	if e.file != nil {
		err = e.file.SaveAs(e.filename)
		if err != nil {
			log.Err(err).Msg("保存excel文件失败")
		}
		e.file.Close()
		e.file = nil
	}
	return err
}
