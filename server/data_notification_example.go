package server

import (
	"encoding/hex"
	"fmt"
	"pvFactoryBackend/base"
	"time"

	"github.com/rs/zerolog/log"
)

// DataNotificationExample 数据通知机制使用示例
func DataNotificationExample() {
	// 创建测试方案实例
	commArgs := &base.CommArgs{
		WorkstationID: 1,
		TestType:      "whole",
	}
	
	testPlan := NewTestPlan(commArgs)
	
	// 模拟启动一个等待数据的协程
	go func() {
		log.Info().Msg("开始等待数据通知...")
		
		// 等待数据通知，超时时间100秒
		data, timeout := testPlan.WaitForDataNotification()
		
		if timeout {
			log.Warn().Msg("等待数据通知超时")
		} else {
			log.Info().
				Str("数据", hex.EncodeToString(data)).
				Int("长度", len(data)).
				Msg("成功接收到数据通知")
		}
	}()
	
	// 等待一段时间，确保等待协程已启动
	time.Sleep(1 * time.Second)
	
	// 检查是否正在等待数据
	if testPlan.IsWaitingForData() {
		log.Info().Msg("确认正在等待数据")
	}
	
	// 模拟接收到数据后发送通知
	go func() {
		// 等待5秒后发送数据通知
		time.Sleep(5 * time.Second)
		
		testData := []byte("S1234567890\r\n")
		log.Info().
			Str("数据", hex.EncodeToString(testData)).
			Msg("模拟发送数据通知")
		
		testPlan.NotifyDataReceived(testData)
	}()
	
	// 等待足够的时间让示例完成
	time.Sleep(10 * time.Second)
	
	// 清理资源
	testPlan.Deinit()
	
	log.Info().Msg("数据通知机制示例完成")
}

// TimeoutExample 超时示例
func TimeoutExample() {
	log.Info().Msg("开始超时示例...")
	
	commArgs := &base.CommArgs{
		WorkstationID: 2,
		TestType:      "whole",
	}
	
	testPlan := NewTestPlan(commArgs)
	defer testPlan.Deinit()
	
	// 等待数据通知，但不发送任何数据，应该会超时
	data, timeout := testPlan.WaitForDataNotification()
	
	if timeout {
		log.Info().Msg("超时示例：成功触发超时")
	} else {
		log.Error().
			Str("数据", hex.EncodeToString(data)).
			Msg("超时示例：意外接收到数据")
	}
}

// MultipleNotificationsExample 多次通知示例
func MultipleNotificationsExample() {
	log.Info().Msg("开始多次通知示例...")
	
	commArgs := &base.CommArgs{
		WorkstationID: 3,
		TestType:      "whole",
	}
	
	testPlan := NewTestPlan(commArgs)
	defer testPlan.Deinit()
	
	// 启动等待协程
	go func() {
		for i := 0; i < 3; i++ {
			log.Info().Int("轮次", i+1).Msg("开始等待数据通知")
			
			data, timeout := testPlan.WaitForDataNotification()
			
			if timeout {
				log.Warn().Int("轮次", i+1).Msg("等待超时")
				break
			} else {
				log.Info().
					Int("轮次", i+1).
					Str("数据", hex.EncodeToString(data)).
					Msg("接收到数据")
			}
			
			// 短暂等待
			time.Sleep(1 * time.Second)
		}
	}()
	
	// 发送多次数据通知
	go func() {
		testDataList := [][]byte{
			[]byte("S1111111111\r\n"),
			[]byte("S2222222222\r\n"),
			[]byte("S3333333333\r\n"),
		}
		
		for i, testData := range testDataList {
			time.Sleep(2 * time.Second)
			
			log.Info().
				Int("发送轮次", i+1).
				Str("数据", hex.EncodeToString(testData)).
				Msg("发送数据通知")
			
			testPlan.NotifyDataReceived(testData)
		}
	}()
	
	// 等待示例完成
	time.Sleep(15 * time.Second)
	
	log.Info().Msg("多次通知示例完成")
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	fmt.Println("=== 数据通知机制示例 ===")
	
	// 基本示例
	fmt.Println("\n1. 基本数据通知示例")
	DataNotificationExample()
	
	// 超时示例
	fmt.Println("\n2. 超时示例")
	TimeoutExample()
	
	// 多次通知示例
	fmt.Println("\n3. 多次通知示例")
	MultipleNotificationsExample()
	
	fmt.Println("\n=== 所有示例完成 ===")
}
