package server

import (
	"archive/zip"
	"encoding/hex"
	"errors"
	"fmt"
	"pvFactoryBackend/base"
	"pvFactoryBackend/communication"
	"pvFactoryBackend/device"
	"pvFactoryBackend/model"
	"pvFactoryBackend/model/table"
	"regexp"
	"runtime/debug"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/rs/zerolog/log"
	"go.bug.st/serial"
)

var btMutex = &sync.Mutex{}

// TestPlan 测试方案运行类
type TestPlan struct {
	// 测试方案类型
	plan string
	// 测试方案对应的通讯参数
	commArgs *base.CommArgs
	// 测试项目列表
	testItems []string

	// 连接的信号引脚通讯
	comSignalPin *device.Dlt698Helper
	// 连接的RS485通讯
	comRs485 *device.ModbusHelper
	// 条码扫描头
	portBarcode *communication.SerialLink
	// 串口处理
	comGpio *device.GpioHelper

	// GPIO运行协程
	goGpioId int64
	// 保存上次扫描的条码内容
	lastBarcode []string

	// 上送消息的通道
	send chan *EventMessage
	// ws通讯连接
	wsConn *websocket.Conn

	// 程序配置信息，用于获取校验信息
	config *base.Config
}

// NewTestPlan 新建测试方案
// plan 测试方案类型
// commArgs 通讯参数
// testItems 测试项目列表
func NewTestPlan(commArgs *base.CommArgs) *TestPlan {
	commArgs.Barcode = ""
	return &TestPlan{
		commArgs:    commArgs,
		lastBarcode: make([]string, 3),
	}
}

// 重新配置测试方案
func (tp *TestPlan) ResetPlan(ws *websocket.Conn, plan string, testItems []string) {
	tp.commArgs.TestType = plan
	tp.commArgs.Barcode = ""
	tp.plan = plan
	tp.testItems = testItems
	tp.wsConn = ws
}

// Init 初始化测试方案，开始监听GPIO或条码变化，并开启各串口通讯
func (tp *TestPlan) Init(send chan *EventMessage, config *base.Config) error {
	var comSignalPin *device.Dlt698Helper
	var comRs485 *device.ModbusHelper
	var portBarcode *communication.SerialLink
	var comGpio *device.GpioHelper

	// 初始化主通讯口（数据交互模组信号引脚）
	if tp.commArgs.SignalPinPort != "" {
		// TODO 主通讯串口参数待确定
		portSignalPin := communication.NewSerialLink(tp.commArgs.SignalPinPort)
		portSignalPin.Baud = 115200
		portSignalPin.Parity = serial.EvenParity
		comSignalPin = device.NewDlt698Helper()
		err := comSignalPin.Init(portSignalPin)
		if err != nil {
			log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("通讯口", tp.commArgs.SignalPinPort).Msg("初始化 SignalPin 失败")
			return err
		}
		log.Debug().Str("通讯口", tp.commArgs.SignalPinPort).Msg("初始化 SignalPin 成功")
	}

	// 初始化RS485（同逆变器通讯下行 485）
	if tp.commArgs.RS485Port != "" {
		portRs485 := communication.NewSerialLink(tp.commArgs.RS485Port)
		portRs485.Baud = 9600
		portRs485.Parity = serial.NoParity
		comRs485 = device.NewModbusHelper(0xFF)
		err := comRs485.Init(portRs485)
		if err != nil {
			if comSignalPin != nil {
				comSignalPin.Deinit()
			}
			log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("通讯口", tp.commArgs.RS485Port).Msg("初始化 RS485 失败")
			return err
		}
		log.Debug().Str("通讯口", tp.commArgs.RS485Port).Msg("初始化 RS485 成功")
	}

	// 监听GPIO变化
	if tp.commArgs.PowerOnGPIO != 0 {
		comGpio = device.NewGpioHelper()
		comGpio.Init()
		comGpio.StartInterrupt([]uint{tp.commArgs.PowerOnGPIO})
		comGpio.GpioChangedHandler = tp.gpioChange
	}

	// 监听条码扫描头变化
	if tp.commArgs.BarcodeScannerPort != "" {
		portBarcode = communication.NewSerialLink(tp.commArgs.BarcodeScannerPort)
		portBarcode.Baud = 115200
		portBarcode.Parity = serial.NoParity
		portBarcode.SetOnReceive(tp.barcodeScan)
	}

	// 保存通讯接口
	tp.comSignalPin = comSignalPin
	tp.comRs485 = comRs485
	tp.comGpio = comGpio
	tp.portBarcode = portBarcode

	// 保存通讯连接
	tp.send = send
	tp.config = config

	return nil
}

// Deinit 关闭测试方案，关闭各串口通讯
func (tp *TestPlan) Deinit() error {
	tp.goGpioId = 0

	// 关闭各串口通讯
	if tp.comSignalPin != nil {
		tp.comSignalPin.Deinit()
	}
	if tp.comRs485 != nil {
		tp.comRs485.Deinit()
	}
	if tp.comGpio != nil {
		tp.comGpio.Deinit()
	}
	if tp.portBarcode != nil {
		tp.portBarcode.Close()
	}

	return nil
}

// gpioChange 监听GPIO变化
func (tp *TestPlan) gpioChange(pin uint, value uint) {
	log.Debug().Str("测试方案", tp.plan).Str("条码", tp.commArgs.Barcode).Uint("GPIO", pin).Uint("Value", value).Msg("GPIO变化")

	if pin != tp.commArgs.PowerOnGPIO {
		return
	}

	// 检查测试方案是否有效
	if tp.plan != table.TypeMainStr && tp.plan != table.TypeWholeStr && tp.plan != table.TypeRewholeStr {
		log.Warn().Str("测试方案", tp.plan).Msg("测试方案无效")
		return
	}

	if value == 1 {
		// 未得到条码时，不启动测试
		if len(tp.commArgs.Barcode) < 1 {
			log.Warn().Str("测试方案", tp.plan).Msg("未得到条码，不启动测试")
			return
		}
		// 等待2秒
		time.Sleep(2 * time.Second)

		// 启动测试流程
		goGpioId := time.Now().UnixNano()
		tp.goGpioId = goGpioId
		go tp.doTest(goGpioId)
	} else {
		tp.goGpioId = 0
	}
}

// barcodeScan 监听条码扫描头变化
// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
func (tp *TestPlan) barcodeScan(data []byte, newoff int) (ret int) {
	log.Debug().Str("测试方案", tp.plan).Str("条码", hex.EncodeToString(data[newoff:])).Msg("扫描到条码")

	// 仅整机测试才会生效，否则清除接收到的数据
	if tp.plan != table.TypeWholeStr && tp.plan != table.TypeRewholeStr {
		return len(data)
	}

	// 读取条码内容
	sdata := string(data[newoff:])
	reg, err := regexp.Compile(`S([^\r\n]+)\r\n`)
	if err != nil {
		log.Err(err).Msg("正则表达式编译失败")
		return 0
	}

	// 查找条码
	matches := reg.FindIndex(data)
	if len(matches) < 4 {
		return 0
	}
	barcode := string(sdata[matches[2]:matches[3]])
	ret = matches[2]

	// 获得新条码，旧条码依次向后移位，起到len(tp.lastBarcode)次扫描到条码执行测试流程的效果
	tp.lastBarcode[2] = tp.lastBarcode[1]
	tp.lastBarcode[1] = tp.lastBarcode[0]
	tp.lastBarcode[0] = tp.commArgs.Barcode
	tp.commArgs.Barcode = barcode
	// 若条码内容发生变化，则启动测试流程
	if tp.commArgs.Barcode == tp.lastBarcode[1] && barcode != tp.lastBarcode[2] {
		goGpioId := time.Now().UnixNano()
		tp.goGpioId = goGpioId
		go tp.doTest(goGpioId)
	}

	return ret
}

// doTest 启动测试方案
func (tp *TestPlan) doTest(goId int64) {
	defer func() {
		tp.goGpioId = 0
		if r := recover(); r != nil {
			log.Error().Str("Stack", string(debug.Stack())).Msgf("TestPlan.DoTest 程序运行异常: %v", r)
		}
	}()

	// 当出现错误时只记录第一个错误
	var firstErr error

	// 初始化地址为广播地址
	tp.comSignalPin.SetAddr("AAAAAAAAAAAA")

	// 发送开始测试消息
	if tp.send != nil {
		tp.send <- &EventMessage{
			Conn:    tp.wsConn,
			MsgType: 20,
			Message: gin.H{
				"type":    "startTest",
				"devices": tp.commArgs.WorkstationID,
				"barcode": tp.commArgs.Barcode,
			},
		}
	}
	// 写入记录方案条码
	recordId, err := model.DefaultDB.ResetTestPlan(tp.plan, tp.commArgs.Barcode)
	if err != nil {
		log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg("记录方案条码失败")
		if tp.send != nil {
			tp.send <- &EventMessage{
				Conn:    tp.wsConn,
				MsgType: 20,
				Message: gin.H{
					"type":    "failTest",
					"devices": tp.commArgs.WorkstationID,
					"barcode": tp.commArgs.Barcode,
					"detail":  err.Error(),
				},
			}
		}
		tp.commArgs.Barcode = ""
		return
	}

	// 循环测试方案的所有项目
	log.Debug().Int64("更新ID", tp.goGpioId).Int64("入口ID", goId).Msg("开始测试方案")
	for i := 0; tp.goGpioId == goId && i < len(tp.testItems); i++ {
		if tp.send != nil {
			tp.send <- &EventMessage{
				Conn:    tp.wsConn,
				MsgType: 20,
				Message: gin.H{
					"type":    "startItem",
					"devices": tp.commArgs.WorkstationID,
					"barcode": tp.commArgs.Barcode,
					"item":    tp.testItems[i],
				},
			}
		}

		// 记录测试时是否存在错误
		var err error
		// 测试时返回的详细信息
		var detail = ""

		switch tp.testItems[i] {
		// RS485-1
		case table.BoardTTL:
			fallthrough
		case table.WholeRs4851:
			fallthrough
		case table.RewholeRs4851:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			detail, err = tp.comSignalPin.ReadAddress(30 * time.Second)
			if err == nil && len(detail) >= 12 {
				tp.comSignalPin.SetAddr(detail)
			}

		// 指示灯
		case table.BoardLED:
			fallthrough
		case table.WholeLED:
			fallthrough
		case table.RewholeLED:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			err = tp.comSignalPin.SetLedStatus(true)

		// 设置时间
		case table.WholeSetTime:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			now := time.Now()
			detail = now.Format("2006-01-02 15:04:05")
			err = tp.comSignalPin.SetTime(now)

		// 版本号校验
		case table.BoardVersion:
			fallthrough
		case table.WholeVersion:
			fallthrough
		case table.RewholeVersion:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			_, _, detail, _, _, _, err = tp.comSignalPin.ReadBoardInfo()
			// 比较版本信息
			if err == nil && detail != tp.config.Validator.SoftVersion {
				err = errors.New("版本号不匹配: " + detail + " != " + tp.config.Validator.SoftVersion)
			}

		// Flash存储
		case table.BoardFlash:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 执行发送文件操作
			err = tp.comSignalPin.UpdateFile(tp.config.InverterResource + "!" + "init/ivcfg/0.bin")

		// 板级蓝牙
		// case table.BoardBT:
		// 	if tp.comSignalPin == nil {
		// 		detail = "未配置 RS485-1 通讯"
		// 		err = errors.New(detail)
		// 		log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
		// 		break
		// 	}
		// 	// 读取蓝牙状态及MAC地址
		// 	var btStatus bool
		// 	btStatus, detail, err = tp.comSignalPin.ReadBtStatus()
		// 	if err == nil && !btStatus {
		// 		err = errors.New("蓝牙通讯失败")
		// 	}

		// 整机蓝牙
		// case table.WholeBT:
		// 	fallthrough
		// case table.RewholeBT:
		// 	// 查询板级蓝牙测试成功之后的MAC地址
		// 	detail, err = model.DefaultDB.SelectMainTestResult(tp.commArgs.Barcode, table.BoardBT)
		// 	if err != nil {
		// 		log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg("查询板级蓝牙测试结果失败")
		// 		break
		// 	}
		// 	if len(detail) < 1 {
		// 		detail = "板级蓝牙MAC地址不存在"
		// 		log.Warn().Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg(detail)
		// 		err = errors.New(detail)
		// 		break
		// 	}

		// 	// 执行蓝牙测试，每个工装及工装测试都需要锁定蓝牙测试锁
		// 	err = tp.execBluetoothTest(detail)

		// ESAM
		case table.BoardESAM:
			fallthrough
		case table.WholeESAM:
			fallthrough
		case table.RewholeESAM:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 读取ESAM序列号
			detail, err = tp.comSignalPin.ReadEsamSn()

		// RS485
		case table.BoardRs485:
			fallthrough
		case table.WholeRs4852:
			fallthrough
		case table.RewholeRs4852:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 通过 RS485-1 发送读取逆变器电压指令
			var voltage []float32
			voltage, err = tp.comSignalPin.ReadVoltage(4)
			// 发送测试结果
			if err == nil {
				for i := 0; i < len(voltage); i++ {
					if i > 0 {
						detail += ", "
					}
					detail += fmt.Sprintf("%.2fV ", voltage[i])
				}
			}

		// 扩展1-1
		// case table.BoardExt11:
		// 	fallthrough
		// case table.WholeExt11:
		// 	fallthrough
		// case table.RewholeExt11:
		// 	if tp.comSignalPin == nil {
		// 		detail = "未配置 RS485-1 通讯"
		// 		err = errors.New(detail)
		// 		log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
		// 		break
		// 	}
		// 	// 通过 RS485-1 发送读取逆变器电压指令
		// 	var voltage []float32
		// 	voltage, err = tp.comSignalPin.ReadVoltage(2)
		// 	// 发送测试结果
		// 	if err == nil {
		// 		for i := 0; i < len(voltage); i++ {
		// 			if i > 0 {
		// 				detail += ", "
		// 			}
		// 			detail += fmt.Sprintf("%.2fV ", voltage[i])
		// 		}
		// 	}

		// 整机载波
		case table.WholeCarrier:
			fallthrough
		case table.RewholeCarrier:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 判断载波是否有通讯
			var status bool
			status, err = tp.comSignalPin.ReadStaStatus()
			if err == nil && !status {
				err = errors.New("整机载波无通讯")
			}

		// 写号
		case table.WholeSetCode:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 根据设备资产编号查询写号信息
			var codeData []table.WriteCode
			codeData, _, err = model.DefaultDB.SelectCode(tp.commArgs.Barcode, "", "", "", "", "", "", "", 1, 20)
			if err != nil {
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("查询写号信息失败")
				break
			}
			// 没有查询到写号信息
			if len(codeData) < 1 {
				err = errors.New("没有查询到写号信息")
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("没有查询到写号信息")
				break
			}
			// 执行写号操作
			err = tp.WriteCode(codeData[0], true)

		// 检查写号
		case table.WholeCheckCode:
			fallthrough
		case table.RewholeCheckCode:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 根据设备资产编号查询写号信息
			var codeData []table.WriteCode
			codeData, _, err = model.DefaultDB.SelectCode(tp.commArgs.Barcode, "", "", "", "", "", "", "", 1, 20)
			if err != nil {
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("查询写号信息失败")
				break
			}
			// 没有查询到写号信息
			if len(codeData) < 1 {
				err = errors.New("没有查询到写号信息")
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("没有查询到写号信息")
				break
			}
			// 读取当前设备写号信息
			var code *table.WriteCode
			code, err = tp.readCode()
			if err != nil {
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("读取写号信息失败")
				break
			}

			// 显示写号信息比较
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].Barcode).Str("已写入", code.Barcode).Bool("一致", code.Barcode == codeData[0].Barcode).Msg("比较资产条码")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].Address).Str("已写入", code.Address).Bool("一致", code.Address == codeData[0].Address).Msg("比较设备地址")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].MeterNumber).Str("已写入", code.MeterNumber).Bool("一致", code.MeterNumber == codeData[0].MeterNumber).Msg("比较电表号")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].HardVersion).Str("已写入", code.HardVersion).Bool("一致", code.HardVersion == codeData[0].HardVersion).Msg("比较硬件版本")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].HardDate).Str("已写入", code.HardDate).Bool("一致", code.HardDate == codeData[0].HardDate).Msg("比较硬件日期")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Time("数据库", *codeData[0].ProduceDate).Time("已写入", *code.ProduceDate).Bool("一致", *code.ProduceDate == *codeData[0].ProduceDate).Msg("比较生产日期")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Bool("数据库", codeData[0].SafeMode).Bool("已写入", code.SafeMode).Bool("一致", code.SafeMode == codeData[0].SafeMode).Msg("比较安全模式")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].VendorCode).Str("已写入", code.VendorCode).Bool("一致", code.VendorCode == codeData[0].VendorCode).Msg("比较厂商代码")
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("数据库", codeData[0].VendorExt).Str("已写入", code.VendorExt).Bool("一致", code.VendorExt == codeData[0].VendorExt).Msg("比较厂商扩展")

			// 检查写号信息是否一致
			if code.Barcode != codeData[0].Barcode || code.Address != codeData[0].Address || code.MeterNumber != codeData[0].MeterNumber ||
				code.HardVersion != codeData[0].HardVersion || code.HardDate != codeData[0].HardDate || code.ProduceDate.Format("2006-01-02") != codeData[0].ProduceDate.Format("2006-01-02") ||
				code.SafeMode != codeData[0].SafeMode || code.VendorCode != codeData[0].VendorCode || code.VendorExt != codeData[0].VendorExt {
				err = errors.New("写号信息不一致")
			}

		// 检查时间
		case table.RewholeCheckTime:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 读取设备时间
			var now *time.Time
			now, err = tp.comSignalPin.ReadTime()
			// 校验时间差，是否在10分钟内
			if err == nil {
				diff := time.Since(*now)
				detail = diff.String()
				if diff.Seconds() > 600 || diff.Seconds() < -600 {
					err = errors.New("时间差超过10分钟")
				}
			}

		// 校准
		case table.WholeCalibration:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// TODO 通过标准电源获取电压
			err = tp.comSignalPin.MeterCalibration(220)
			if err != nil {
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("校准失败")
				break
			}

			// 读取电压，判断与标准电压是否一致
			var voltage []float32
			voltage, err = tp.comSignalPin.ReadVoltage(0)
			if err != nil {
				log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Msg("读取校准后电压失败")
				break
			}

			// 发送测试结果
			for i := 0; i < len(voltage); i++ {
				if i > 0 {
					detail += ", "
				}
				detail += fmt.Sprintf("%.2fV ", voltage[i])
			}
			// 计算偏差百分比
			if len(voltage) > 0 {
				diff := (voltage[0] - 220.0) / 220.0 * 100
				if diff > 1 || diff < -1 {
					err = errors.New("校准后电压偏差超过1%")
				}
			}
			log.Debug().Int("工位编号", tp.commArgs.WorkstationID).Str("资产条码", tp.commArgs.Barcode).Str("校准后电压", detail).Msg("校准结果")

		// 文件系统格式化
		case table.NonNormalFormatFS:
			if tp.comSignalPin == nil {
				detail = "未配置 RS485-1 通讯"
				err = errors.New(detail)
				log.Warn().Str("测试项目", tp.testItems[i]).Msg(detail)
				break
			}
			// 格式化文件系统
			err = tp.comSignalPin.FormatFS()

		default:
			log.Warn().Int("工位编号", tp.commArgs.WorkstationID).Str("测试项", tp.testItems[i]).Msg("未知测试项目")
			continue
		}

		// 记录首次出现的错误
		if err != nil && firstErr == nil {
			firstErr = err
		}
		// 发送测试结果
		tp.sendTestResult(recordId, err, tp.testItems[i], detail)
	}
	// 返回结果
	if firstErr != nil {
		log.Err(firstErr).Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg("测试失败")
		if tp.send != nil {
			tp.send <- &EventMessage{
				Conn:    tp.wsConn,
				MsgType: 20,
				Message: gin.H{
					"type":    "failTest",
					"devices": tp.commArgs.WorkstationID,
					"barcode": tp.commArgs.Barcode,
					"detail":  firstErr.Error(),
				},
			}
		}
	} else {
		log.Info().Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg("测试成功")
		tp.send <- &EventMessage{
			Conn:    tp.wsConn,
			MsgType: 20,
			Message: gin.H{
				"type":    "finishTest",
				"devices": tp.commArgs.WorkstationID,
				"barcode": tp.commArgs.Barcode,
			},
		}
	}
	// 更新测试方案结果
	err = model.DefaultDB.UpdateTestPlan(tp.plan, tp.commArgs.Barcode, firstErr == nil)
	if err != nil {
		log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("条码", tp.commArgs.Barcode).Msg("更新测试方案结果失败")
	}

	// 清空条码，防止重复测试
	tp.commArgs.Barcode = ""
}

// IsRunning 正在运行测试方案
func (tp *TestPlan) IsRunning() bool {
	return tp.goGpioId > 0
}

// sendTestResult 发送测试项结果
// recordId 测试方案记录ID
// err 上一步执行的错误类型
// itemName 测试项名称
// detail 测试项详情。测试成功是可取关键值，比如版本号校验测试项，这里可返回读取到的版本号；测试失败为失败可能的原因
func (tp *TestPlan) sendTestResult(recordId uint, err error, itemName string, detail string) error {
	if err != nil {
		log.Warn().Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("测试项", itemName).Msg("测试失败")
	} else {
		log.Info().Int("工位编号", tp.commArgs.WorkstationID).Str("测试项", itemName).Str("测试结果", detail).Msg("测试成功")
	}

	// 发送测试结果消息
	if tp.send != nil {
		if err != nil {
			tp.send <- &EventMessage{
				Conn:    tp.wsConn,
				MsgType: 20,
				Message: gin.H{
					"type":    "failItem",
					"devices": tp.commArgs.WorkstationID,
					"barcode": tp.commArgs.Barcode,
					"item":    itemName,
					"detail":  detail + ": " + err.Error(),
				},
			}
		} else {
			tp.send <- &EventMessage{
				Conn:    tp.wsConn,
				MsgType: 20,
				Message: gin.H{
					"type":    "finishItem",
					"devices": tp.commArgs.WorkstationID,
					"barcode": tp.commArgs.Barcode,
					"item":    itemName,
					"detail":  detail,
				},
			}
		}
	}
	// 写入测试记录
	err = model.DefaultDB.AddTestResult(recordId, itemName, detail, err == nil)
	if err != nil {
		log.Warn().Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("测试项", itemName).Msg("写入记录失败")
	}
	return err
}

// writeCode 执行写号操作
func (tp *TestPlan) WriteCode(codeData table.WriteCode, withpv bool) error {
	// 写入资产编码
	err := tp.comSignalPin.WriteAssetCode(codeData.Barcode)
	if err != nil {
		log.Err(err).Msg("写入资产编码失败")
		return err
	}
	log.Info().Msg("写入资产编码成功")

	// 写入通讯地址
	err = tp.comSignalPin.WriteAddress(codeData.Address)
	if err != nil {
		log.Err(err).Msg("写入通讯地址失败")
		return err
	}
	log.Info().Msg("写入通讯地址成功")

	// 写入电表号
	err = tp.comSignalPin.WriteMeterId(codeData.MeterNumber)
	if err != nil {
		log.Err(err).Msg("写入电表号失败")
		return err
	}
	log.Info().Msg("写入电表号成功")

	// 写入板子版本信息
	err = tp.comSignalPin.WriteBoardInfo(codeData.HardVersion, codeData.HardDate, codeData.VendorCode, codeData.VendorExt)
	if err != nil {
		log.Err(err).Msg("写入板子版本信息失败")
		return err
	}
	log.Info().Msg("写入板子版本信息成功")

	// 写入生产日期
	err = tp.comSignalPin.WriteProduceDate(codeData.ProduceDate)
	if err != nil {
		log.Err(err).Msg("写入生产日期失败")
		return err
	}
	log.Info().Msg("写入生产日期成功")

	// 写入安全模式
	err = tp.comSignalPin.WriteSafeMode(codeData.SafeMode)
	if err != nil {
		log.Err(err).Msg("写入安全模式失败")
		return err
	}
	log.Info().Msg("写入安全模式成功")

	// 列出所有指定目录中的文件名
	if withpv {
		files := tp.getFilesFromZip(tp.config.InverterResource)
		for _, file := range files {
			log.Debug().Str("文件名", file).Msg("文件名")
			// 执行发送文件操作
			err := tp.comSignalPin.UpdateFile(file)
			if err != nil {
				log.Err(err).Msg("逆变器信息传输失败")
				return err
			}
			log.Info().Str("文件名", file).Msg("传输成功")
		}
		log.Info().Msg("逆变器信息文件传输成功")
	}
	return nil
}

// ReadAssetCode 读取资产编码
func (tp *TestPlan) ReadAssetCode() (string, error) {
	return tp.comSignalPin.ReadAssetCode()
}

// readCode 读取写号信息
func (tp *TestPlan) readCode() (*table.WriteCode, error) {
	code := &table.WriteCode{}

	// 读取资产编码
	asset, err := tp.comSignalPin.ReadAssetCode()
	if err != nil {
		log.Err(err).Msg("读取资产编码失败")
		return nil, err
	}
	log.Info().Msgf("资产编码：%s", asset)
	code.Barcode = asset

	// 读取通讯地址
	addr, err := tp.comSignalPin.ReadAddress(0)
	if err != nil {
		log.Err(err).Msg("读取通讯地址失败")
		return nil, err
	}
	log.Info().Msgf("通讯地址：%s", addr)
	code.Address = addr

	// 读取电表号
	meter, err := tp.comSignalPin.ReadMeterId()
	if err != nil {
		log.Err(err).Msg("读取电表号失败")
		return nil, err
	}
	log.Info().Msgf("电表号：%s", meter)
	code.MeterNumber = meter

	// 读取板子版本信息
	hardVersion, hardDate, _, _, vendorCode, vendorExt, err := tp.comSignalPin.ReadBoardInfo()
	if err != nil {
		log.Err(err).Msg("读取板子版本信息失败")
		return nil, err
	}
	log.Info().Msgf("硬件版本：%s", hardVersion)
	log.Info().Msgf("硬件日期：%s", hardDate)
	log.Info().Msgf("厂商代码：%s", vendorCode)
	log.Info().Msgf("厂商扩展信息：%s", vendorExt)
	code.HardVersion = hardVersion
	code.HardDate = hardDate
	code.VendorCode = vendorCode
	code.VendorExt = vendorExt

	// 读取生产日期
	date, err := tp.comSignalPin.ReadProduceDate()
	if err != nil {
		log.Err(err).Msg("读取生产日期失败")
		return nil, err
	}
	log.Info().Msgf("生产日期：%s", date)
	code.ProduceDate = date

	// 读取安全模式
	mode, err := tp.comSignalPin.ReadSafeMode()
	if err != nil {
		log.Err(err).Msg("读取安全模式失败")
		return nil, err
	}
	log.Info().Msgf("安全模式：%t", mode)
	code.SafeMode = mode

	return code, nil
}

// getFilesFromZip 从指定zip文件中的获得文件名列表
func (tp *TestPlan) getFilesFromZip(zipFile string) []string {
	// 打开zip文件
	zipReader, err := zip.OpenReader(zipFile)
	if err != nil {
		log.Err(err).Str("zip文件", zipFile).Msg("打开zip文件失败")
		return nil
	}
	defer zipReader.Close()

	// 遍历zip文件中的文件
	var list []string
	for _, file := range zipReader.File {
		if !file.FileInfo().IsDir() {
			list = append(list, zipFile+"!"+file.Name)
		}
	}
	return list
}

// 执行整机蓝牙测试
func (tp *TestPlan) execBluetoothTest(mac string) error {
	// 蓝牙测试锁
	btMutex.Lock()
	defer btMutex.Unlock()

	// 因目前读取出的MAC地址为经典蓝牙地址(DC开头)，需要转换为BLE地址(DD开头)
	if mac[0:2] == "dc" {
		mac = "dd" + mac[2:]
	}

	// 根据MAC地址连接蓝牙设备
	portbt := communication.NewBleLink(mac)
	err := portbt.Init()
	if err != nil {
		log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Msg("初始化蓝牙适配器失败")
		return err
	}

	combt := device.NewDlt698Helper()
	err = combt.Init(portbt)
	if err != nil {
		log.Err(err).Int("工位编号", tp.commArgs.WorkstationID).Str("MAC", mac).Msg("初始化蓝牙连接失败")
		return err
	}
	defer combt.Deinit()
	_, err = combt.ReadAddress(0)
	return err
}
