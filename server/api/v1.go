package api

import (
	"fmt"
	"net/http"
	"os"
	"path"
	"pvFactoryBackend/base"
	"pvFactoryBackend/model"
	"pvFactoryBackend/model/table"
	"pvFactoryBackend/server"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// API API类
type API struct {
	config *base.Config
}

// NewAPI 创建API实例
func NewAPI(config *base.Config) *API {
	return &API{
		config: config,
	}
}

// 为21位国网资产条码添加校验码
func (d *API) addCs(value string) string {
	if len(value) > 21 {
		value = value[:21]
	} else if len(value) < 21 {
		value = strings.Repeat("0", 21-len(value)) + value
	}

	sum := 0
	for i, b := range value {
		if b < '0' || b > '9' {
			b = '0'
		}
		if i%2 == 0 {
			sum += int(b-'0') * 3
		} else {
			sum += int(b - '0')
		}
	}
	return value + fmt.Sprintf("%d", (10-sum%10)%10)
}

// GetInfo 获取后端信息
func (a *API) GetInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"error_code": 0,
		"message":    "成功",
		"name":       a.config.DeviceName,
		"version":    base.VERSION,
	})
}

// GetPower 查询电源板测试结果
func (a *API) GetPower(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error_code": 0xF1,
		"message":    "不支持的测试类型操作",
	})
}

// PostPower 添加电源板测试记录
func (a *API) PostPower(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error_code": 0xF1,
		"message":    "不支持的测试类型操作",
	})
}

// PutPower 修改电源板测试记录
func (a *API) PutPower(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error_code": 0xF1,
		"message":    "不支持的测试类型操作",
	})
}

// GetBarcode 查询条码状态
func (a *API) GetBarcode(c *gin.Context) {
	// 获取参数
	btype := c.Query("type")
	if btype != table.TypeMainStr && btype != table.TypeWholeStr && btype != table.TypeRewholeStr {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误: type 为空或内容不正确",
		})
		return
	}
	barcode := c.Query("barcode")
	if barcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误: barcode 不能为空",
		})
		return
	}

	// 根据不同的测试类型，判条码长度是否合法
	if (btype == table.TypeMainStr) && (len(barcode) < 4 || len(barcode) > 20) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： barcode 长度必须为 4 到 20 位",
		})
		return
	} else if btype == table.TypeWholeStr || btype == table.TypeRewholeStr {
		if len(barcode) != 22 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error_code": 19,
				"message":    "请求参数解析错误： barcode 长度必须为 22 位",
			})
			return
		} else if a.addCs(barcode) != barcode {
			c.JSON(http.StatusBadRequest, gin.H{
				"error_code": 19,
				"message":    "请求参数解析错误： 整机条码校验失败",
			})
			return
		}
	}

	// 查询条码组装状态
	result, err := model.DefaultDB.SelectBarcodeAssembled(btype, barcode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询条码组装状态失败: " + err.Error(),
		})
		return
	}
	var detail string
	switch result {
	case 1:
		detail = "未组装"
	case 2:
		detail = "已组装"
	default:
		detail = "未知状态"
	}
	if result != 1 {
		c.JSON(http.StatusOK, gin.H{
			"error_code": 0,
			"message":    "成功",
			"result":     false,
			"detail":     detail,
		})
		return
	}

	// 查询条码检测状态
	result, err = model.DefaultDB.SelectBarcodePlan(btype, barcode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询条码检测状态失败: " + err.Error(),
		})
		return
	}
	switch result {
	case 1:
		detail = "未检测"
	case 2:
		detail = "检测不合格"
	case 3:
		detail = "检测通过"
	default:
		detail = "未知状态"
	}

	c.JSON(http.StatusOK, gin.H{
		"error_code": 0,
		"message":    "成功",
		"result":     result == 3,
		"detail":     detail,
	})
}

// GetAssemble 查询组装关系
func (a *API) GetAssemble(c *gin.Context) {
	// 获取参数
	powerBarcode := c.Query("powerBarcode")
	boardBarcode := c.Query("boardBarcode")
	wholeBarcode := c.Query("wholeBarcode")
	page := c.DefaultQuery("page", "1")
	count := c.DefaultQuery("count", "20")

	// 转换参数格式
	npage, err := strconv.Atoi(page)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： page 必须为整数",
		})
		return
	}
	ncount, err := strconv.Atoi(count)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： count 必须为整数",
		})
		return
	}
	// 判断数量是否合法
	if ncount > 100 {
		ncount = 100
	} else if ncount < 1 {
		ncount = 20
	}
	// 判断条码是否合法
	if len(powerBarcode) != 0 && (len(powerBarcode) < 4 || len(powerBarcode) > 20) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 电源板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(boardBarcode) != 0 && (len(boardBarcode) < 4 || len(boardBarcode) > 20) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 主板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(wholeBarcode) != 0 && len(wholeBarcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 查询组装关系
	records, total, err := model.DefaultDB.SelectAssemble(powerBarcode, boardBarcode, wholeBarcode, npage, ncount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询组装关系失败: " + err.Error(),
		})
		return
	}

	// 组织返回的结果
	resqs := make([]struct {
		PowerBarcode string `json:"powerBarcode"`
		BoardBarcode string `json:"boardBarcode"`
		WholeBarcode string `json:"wholeBarcode"`
	}, len(records))
	for i, record := range records {
		resqs[i].PowerBarcode = record.PowerBarcode
		resqs[i].BoardBarcode = record.BoardBarcode
		resqs[i].WholeBarcode = record.WholeBarcode
	}

	c.JSON(http.StatusOK, gin.H{
		"error_code": 0,
		"message":    "成功",
		"page":       npage,
		"page_count": (int(total) + ncount - 1) / ncount,
		"records":    resqs,
	})
}

// PostAssemble 添加组装关系
func (a *API) PostAssemble(c *gin.Context) {
	var req struct {
		PowerBarcode string `json:"powerBarcode" binding:"required"`
		BoardBarcode string `json:"boardBarcode" binding:"required"`
		WholeBarcode string `json:"wholeBarcode" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}
	// 判断条码是否合法
	if len(req.PowerBarcode) < 4 || len(req.PowerBarcode) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 电源板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(req.BoardBarcode) < 4 || len(req.BoardBarcode) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 主板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(req.WholeBarcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 保存组装关系
	if err := model.DefaultDB.AddAssemble(req.PowerBarcode, req.BoardBarcode, req.WholeBarcode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 2,
			"message":    "添加组装关系失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// PutAssemble 修改组装关系
func (a *API) PutAssemble(c *gin.Context) {
	var req struct {
		PowerBarcode string `json:"powerBarcode" binding:"required"`
		BoardBarcode string `json:"boardBarcode" binding:"required"`
		WholeBarcode string `json:"wholeBarcode" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}
	// 判断条码是否合法
	if len(req.PowerBarcode) < 4 || len(req.PowerBarcode) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 电源板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(req.BoardBarcode) < 4 || len(req.BoardBarcode) > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 主板条码 长度必须为 4 到 20 位",
		})
		return
	}
	if len(req.WholeBarcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 更新组装关系
	if err := model.DefaultDB.UpdateAssemble(req.PowerBarcode, req.BoardBarcode, req.WholeBarcode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 3,
			"message":    "更新组装关系失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// DeleteAssemble 删除组装关系
func (a *API) DeleteAssemble(c *gin.Context) {
	var req struct {
		Barcode string `json:"barcode" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}
	// 判断条码是否合法
	if len(req.Barcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 删除组装关系
	if err := model.DefaultDB.DeleteAssemble(req.Barcode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 5,
			"message":    "删除组装关系失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// GetCodeUsed 查询资产编码信息
func (a *API) GetCodeUsed(c *gin.Context) {
	// 获取参数
	barcode := c.Query("barcode")
	if barcode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误: 资产编码 不能为空",
		})
		return
	}

	// 查询组装信息，判断是否已使用
	_, asCount, _ := model.DefaultDB.SelectAssemble("", "", barcode, 1, 1)

	// 查询写号信息
	records, _, err := model.DefaultDB.SelectCode(barcode, "", "", "", "", "", "", "", 1, 1)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询写号信息失败: " + err.Error(),
		})
		return
	}

	// 组织返回的结果
	if len(records) < 1 {
		c.JSON(http.StatusOK, gin.H{
			"error_code":  0,
			"message":     "成功",
			"used":        asCount > 0,
			"barcode":     barcode,
			"address":     "",
			"meter":       "",
			"hardVersion": "",
			"hardDate":    "",
			"produceDate": "",
			"vendorCode":  "",
			"vendorExt":   "",
			"mode":        false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"error_code":  0,
		"message":     "成功",
		"used":        asCount > 0,
		"barcode":     barcode,
		"address":     records[0].Address,
		"meter":       records[0].MeterNumber,
		"hardVersion": records[0].HardVersion,
		"hardDate":    records[0].HardDate,
		"produceDate": records[0].ProduceDate.Format("2006-01-02"),
		"vendorCode":  records[0].VendorCode,
		"vendorExt":   records[0].VendorExt,
		"mode":        records[0].SafeMode,
	})
}

// PutCodeUsed 修改资产编码信息
func (a *API) PutCodeUsed(c *gin.Context, ws *server.Event) {
	var req struct {
		Barcode    string `json:"barcode" binding:"required"`
		NewBarcode string `json:"newBarcode" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}

	// 查询旧资产编码绑定信息，必须已绑定
	_, asCount, _ := model.DefaultDB.SelectAssemble("", "", req.Barcode, 1, 1)
	if asCount < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 18,
			"message":    "该资产编码未组装",
		})
		return
	}

	// 查询旧资产编码绑定信息，必须未绑定
	_, asCount, err := model.DefaultDB.SelectAssemble("", "", req.NewBarcode, 1, 1)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 18,
			"message":    "查询组装关系失败: " + err.Error(),
		})
		return
	}
	if asCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 18,
			"message":    "新资产编码已有组装关系，请更换",
		})
		return
	}

	// 查询写号信息
	records, _, err := model.DefaultDB.SelectCode(req.NewBarcode, "", "", "", "", "", "", "", 1, 1)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询写号信息失败: " + err.Error(),
		})
		return
	}
	if len(records) < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 18,
			"message":    "新资产编码没有写号信息，请先添加写号信息",
		})
		return
	}

	if len(ws.PlanRunning) < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 22,
			"message":    "未找到可测试用的工位配置",
		})
		return
	}

	// 读取当前工位安装的设备资产编码是否为旧资产编码
	assetCode, err := ws.PlanRunning[0].ReadAssetCode()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 22,
			"message":    "读取当前工位安装的设备资产编码失败: " + err.Error(),
		})
		return
	}

	// 判断资产编码是否匹配
	if assetCode != req.Barcode {
		c.JSON(http.StatusNotFound, gin.H{
			"error_code": 22,
			"message":    "待修改设备的资产编码与原资产编码不匹配",
		})
		return
	}

	// 执行写号
	err = ws.PlanRunning[0].WriteCode(records[0], false)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 21,
			"message":    "资产编码更换失败，写号失败: " + err.Error(),
		})
		return
	}

	// 修改绑定信息
	err = model.DefaultDB.UpdateWholeBarcode(req.Barcode, req.NewBarcode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 20,
			"message":    "修改资产编码失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// GetCode 查询写号信息
func (a *API) GetCode(c *gin.Context) {
	// 获取参数
	barcode := c.Query("barcode")
	address := c.Query("address")
	meter := c.Query("meter")
	hardVersion := c.Query("hardVersion")
	hardDate := c.Query("hardDate")
	produceDate := c.Query("produceDate")
	vendorCode := c.Query("vendorCode")
	vendorExt := c.Query("vendorExt")
	page := c.DefaultQuery("page", "1")
	count := c.DefaultQuery("count", "20")

	// 转换参数格式
	npage, err := strconv.Atoi(page)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： page 必须为整数",
		})
		return
	}
	ncount, err := strconv.Atoi(count)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： count 必须为整数",
		})
		return
	}
	// 判断数量是否合法
	if ncount > 100 {
		ncount = 100
	} else if ncount < 1 {
		ncount = 20
	}
	// 判断条码是否合法
	if len(barcode) != 0 && len(barcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 查询写号信息
	records, total, err := model.DefaultDB.SelectCode(barcode, address, meter, hardVersion, hardDate, produceDate, vendorCode, vendorExt, npage, ncount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询写号信息失败: " + err.Error(),
		})
		return
	}

	// 组织返回的结果
	resqs := make([]struct {
		Barcode     string `json:"barcode"`
		Address     string `json:"address"`
		Meter       string `json:"meter"`
		HardVersion string `json:"hardVersion"`
		HardDate    string `json:"hardDate"`
		ProduceDate string `json:"produceDate"`
		VendorCode  string `json:"vendorCode"`
		VendorExt   string `json:"vendorExt"`
		Mode        bool   `json:"mode"`
	}, len(records))
	for i, record := range records {
		resqs[i].Barcode = record.Barcode
		resqs[i].Address = record.Address
		resqs[i].Meter = record.MeterNumber
		resqs[i].HardVersion = record.HardVersion
		resqs[i].HardDate = record.HardDate
		resqs[i].ProduceDate = record.ProduceDate.Format("2006-01-02")
		resqs[i].VendorCode = record.VendorCode
		resqs[i].VendorExt = record.VendorExt
		resqs[i].Mode = record.SafeMode
	}

	c.JSON(http.StatusOK, gin.H{
		"error_code": 0,
		"message":    "成功",
		"page":       npage,
		"page_count": (int(total) + ncount - 1) / ncount,
		"records":    resqs,
	})
}

// PostCode 添加写号信息
func (a *API) PostCode(c *gin.Context) {
	var req struct {
		Barcode     string `json:"barcode" binding:"required"`
		Address     string `json:"address" binding:"required"`
		Meter       string `json:"meter" binding:"required"`
		HardVersion string `json:"hardVersion"`
		HardDate    string `json:"hardDate"`
		ProduceDate string `json:"produceDate"`
		VendorCode  string `json:"vendorCode"`
		VendorExt   string `json:"vendorExt"`
		Mode        bool   `json:"mode"`
		Count       int    `json:"count" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}

	// 转换日期格式
	var pDate *time.Time
	if req.ProduceDate != "" {
		req.ProduceDate = strings.Replace(req.ProduceDate, "-", "", -1)
		req.ProduceDate = strings.Replace(req.ProduceDate, "/", "", -1)
		date, err := time.Parse("20060102", req.ProduceDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error_code": 1,
				"message":    "请求参数解析错误： produceDate 格式错误",
			})
			return
		}
		pDate = &date
	} else {
		date := time.Now()
		pDate = &date
	}
	// 判断数量是否合法
	if req.Count > 10000 {
		req.Count = 10000
	} else if req.Count < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： count 数量格式错误",
		})
		return
	}
	// 判断条码是否合法
	if len(req.Barcode) != 21 && len(req.Barcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 21 或 22 位",
		})
		return
	}

	// 保存写号信息
	if err := model.DefaultDB.AddCode(req.Barcode, req.Address, req.Meter, req.HardVersion, req.HardDate, req.VendorCode, req.VendorExt, req.Mode, pDate, req.Count); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 2,
			"message":    "添加写号信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// PutCode 修改写号信息
func (a *API) PutCode(c *gin.Context) {
	var req struct {
		Barcode     string `json:"barcode" binding:"required"`
		Address     string `json:"address"`
		Meter       string `json:"meter"`
		HardVersion string `json:"hardVersion"`
		HardDate    string `json:"hardDate"`
		ProduceDate string `json:"produceDate"`
		VendorCode  string `json:"vendorCode"`
		VendorExt   string `json:"vendorExt"`
		Mode        bool   `json:"mode"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}

	// 转换日期格式
	var pDate *time.Time
	if req.ProduceDate != "" {
		req.ProduceDate = strings.Replace(req.ProduceDate, "-", "", -1)
		req.ProduceDate = strings.Replace(req.ProduceDate, "/", "", -1)
		date, err := time.Parse("20060102", req.ProduceDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error_code": 1,
				"message":    "请求参数解析错误： produceDate 格式错误",
			})
			return
		}
		pDate = &date
	}
	// 判断条码是否合法
	if len(req.Barcode) != 22 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 更新写号信息
	if err := model.DefaultDB.UpdateCode(req.Barcode, req.Address, req.Meter, req.HardVersion, req.HardDate, req.VendorCode, req.VendorExt, req.Mode, pDate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 3,
			"message":    "更新写号信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
}

// GetRecord 数据查询与导出
func (a *API) GetRecord(c *gin.Context) {
	// 获取参数
	barcode := c.Query("barcode")
	btype := c.Query("type")
	var typeCode byte
	if btype == table.TypeMainStr {
		typeCode = table.TypeMain
	} else if btype == table.TypeWholeStr {
		typeCode = table.TypeWhole
	} else if btype == table.TypeRewholeStr {
		typeCode = table.TypeRewhole
	} else {
		barcode = ""
	}
	start := c.Query("start")
	end := c.Query("end")
	status := c.Query("status")
	page := c.DefaultQuery("page", "1")
	count := c.DefaultQuery("count", "20")
	download := c.DefaultQuery("download", "false") == "true"

	// 转换日期格式
	var startTime *time.Time
	nstart, err := strconv.Atoi(start)
	if err == nil && nstart > 0 {
		t := time.Unix(int64(nstart), 0)
		startTime = &t
	}

	var endTime *time.Time
	nend, err := strconv.Atoi(end)
	if err == nil && nend > 0 {
		t := time.Unix(int64(nend), 0)
		endTime = &t
	}

	// 转换参数格式
	npage, err := strconv.Atoi(page)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： page 必须为整数",
		})
		return
	}
	ncount, err := strconv.Atoi(count)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： count 必须为整数",
		})
		return
	}
	nstatus, err := strconv.Atoi(status)
	if err != nil || nstatus < 0 || nstatus > 3 {
		nstatus = 0
	}
	// 判断数量是否合法
	if ncount > 100 {
		ncount = 100
	} else if ncount < 1 {
		ncount = 20
	}
	// 判断条码是否合法
	if (typeCode == table.TypeMain) && (len(barcode) != 0 && len(barcode) < 4 || len(barcode) > 20) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 条码 长度必须为 4 到 20 位",
		})
		return
	} else if (typeCode == table.TypeWhole || typeCode == table.TypeRewhole) && (len(barcode) != 0 && len(barcode) != 22) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 19,
			"message":    "请求参数解析错误： 整机条码 长度必须为 22 位",
		})
		return
	}

	// 查询数据
	records, total, err := model.DefaultDB.SelectRecord(typeCode, barcode, byte(nstatus), startTime, endTime, npage, ncount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error_code": 4,
			"message":    "查询数据失败: " + err.Error(),
		})
		return
	}

	// 组织返回的结果
	resqs := make([]map[string]interface{}, len(records))
	for i, record := range records {
		resqs[i] = make(map[string]interface{})
		resqs[i]["powerBarcode"] = record.PowerBarcode
		resqs[i]["boardBarcode"] = record.BoardBarcode
		resqs[i]["wholeBarcode"] = record.WholeBarcode
		resqs[i]["powerItem"] = make(map[string]string)
		resqs[i]["boardItem"] = make(map[string]string)
		resqs[i]["wholeItem"] = make(map[string]string)
		resqs[i]["rewholeItem"] = make(map[string]string)
		for _, item := range record.PowerPlan.InfoTestItem {
			switch item.Name {
			case table.BoardPower:
				if item.Result {
					resqs[i]["powerItem"].(map[string]string)[item.Name] = "合格"
				} else {
					resqs[i]["powerItem"].(map[string]string)[item.Name] = "不合格"
				}
			}
		}
		for _, item := range record.BoardPlan.InfoTestItem {
			switch item.Name {
			case table.BoardTTL:
				fallthrough
			case table.BoardLED:
				fallthrough
			case table.BoardVersion:
				fallthrough
			case table.BoardFlash:
				fallthrough
			case table.BoardESAM:
				fallthrough
			case table.BoardRs485:
				if item.Result {
					resqs[i]["boardItem"].(map[string]string)[item.Name] = "合格"
				} else {
					resqs[i]["boardItem"].(map[string]string)[item.Name] = "不合格"
				}
			}
		}
		for _, item := range record.WholePlan.InfoTestItem {
			switch item.Name {
			case table.WholeRs4851:
				fallthrough
			case table.WholeVersion:
				fallthrough
			case table.WholeLED:
				fallthrough
			case table.WholeSetTime:
				fallthrough
			case table.WholeBT:
				fallthrough
			case table.WholeESAM:
				fallthrough
			case table.WholeRs4852:
				fallthrough
			case table.WholeExt11:
				fallthrough
			case table.WholeExt12:
				fallthrough
			case table.WholeExt21:
				fallthrough
			case table.WholeExt22:
				fallthrough
			case table.WholeCarrier:
				fallthrough
			case table.WholeSetCode:
				fallthrough
			case table.WholeCheckCode:
				fallthrough
			case table.WholeCalibration:
				if item.Result {
					resqs[i]["wholeItem"].(map[string]string)[item.Name] = "合格"
				} else {
					resqs[i]["wholeItem"].(map[string]string)[item.Name] = "不合格"
				}
			}
		}
		for _, item := range record.WholePlan.InfoTestItem {
			switch item.Name {
			case table.RewholeRs4851:
				fallthrough
			case table.RewholeVersion:
				fallthrough
			case table.RewholeLED:
				fallthrough
			case table.RewholeBT:
				fallthrough
			case table.RewholeESAM:
				fallthrough
			case table.RewholeRs4852:
				fallthrough
			case table.RewholeExt11:
				fallthrough
			case table.RewholeExt12:
				fallthrough
			case table.RewholeExt21:
				fallthrough
			case table.RewholeExt22:
				fallthrough
			case table.RewholeCarrier:
				fallthrough
			case table.RewholeCheckCode:
				fallthrough
			case table.RewholeCheckTime:
				if item.Result {
					resqs[i]["rewholeItem"].(map[string]string)[item.Name] = "合格"
				} else {
					resqs[i]["rewholeItem"].(map[string]string)[item.Name] = "不合格"
				}
			}
		}
	}

	if download {

		// 根据时间生成位于临时目录的临时文件名
		filename := path.Join(os.TempDir(), fmt.Sprintf("pvExport_%d.xlsx", time.Now().UnixMilli()))
		// 生成excel文件
		xlsx := server.Exporter{}
		err := xlsx.New(filename)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error_code": 6,
				"message":    "生成excel文件失败: " + err.Error(),
			})
			return
		}
		// 写入数据
		for i, record := range resqs {
			err = xlsx.AddRow(i, record)
			if err != nil {
				log.Warn().Err(err).Msg("写入行数据失败")
			}
		}
		// 保存文件
		err = xlsx.Save()
		if err != nil {
			log.Err(err).Msg("关闭excel文件失败")
		}

		log.Debug().Msgf("生成文件 %s 成功", filename)
		c.File(filename)
	} else {
		c.JSON(http.StatusOK, gin.H{
			"error_code": 0,
			"message":    "成功",
			"page":       npage,
			"page_count": (int(total) + ncount - 1) / ncount,
			"records":    resqs,
		})
	}
}

// GetConfigWhole 获取整机光伏模块校验参数
func (a *API) GetConfigWhole(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"error_code":   0,
		"message":      "成功",
		"soft_version": a.config.Validator.SoftVersion,
	})
}

// PutConfigWhole 设置整机光伏模块校验参数
func (a *API) PutConfigWhole(c *gin.Context) {
	var req struct {
		SoftVersion string `json:"soft_version" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error_code": 1,
			"message":    "请求参数解析错误： " + err.Error(),
		})
		return
	}
	a.config.Validator.SoftVersion = req.SoftVersion
	c.JSON(http.StatusAccepted, gin.H{
		"error_code": 0,
		"message":    "成功",
	})
	// 保存配置
	a.config.Save()
}

// GetTestItems 获取测试项列表
func (a *API) GetTestItems(c *gin.Context) {
	type TestItem struct {
		Name string `json:"name"`
		Item string `json:"item"`
	}

	type TestPlan struct {
		Name  string     `json:"name"`
		Plan  string     `json:"plan"`
		Items []TestItem `json:"items"`
	}

	plans := []TestPlan{
		{
			Name: "主板测试",
			Plan: table.TypeMainStr,
			Items: []TestItem{
				{
					Name: "TTL通讯测试",
					Item: table.BoardTTL,
				},
				{
					Name: "开启指示灯",
					Item: table.BoardLED,
				},
				{
					Name: "版本号校验",
					Item: table.BoardVersion,
				},
				{
					Name: "Flash存储",
					Item: table.BoardFlash,
				},
				{
					Name: "ESAM测试",
					Item: table.BoardESAM,
				},
				{
					Name: "RS485通讯测试",
					Item: table.BoardRs485,
				},
			},
		},
		// {
		// 	Name: "整机测试",
		// 	Plan: table.TypeWholeStr,
		// 	Items: []TestItem{
		// 		{
		// 			Name: "RS485-1通讯测试",
		// 			Item: table.WholeRs4851,
		// 		},
		// 		{
		// 			Name: "版本号校验",
		// 			Item: table.WholeVersion,
		// 		},
		// 		{
		// 			Name: "开启指示灯",
		// 			Item: table.WholeLED,
		// 		},
		// 		{
		// 			Name: "设置时钟",
		// 			Item: table.WholeSetTime,
		// 		},
		// 		{
		// 			Name: "蓝牙测试",
		// 			Item: table.WholeBT,
		// 		},
		// 		{
		// 			Name: "ESAM测试",
		// 			Item: table.WholeESAM,
		// 		},
		// 		{
		// 			Name: "RS485-2通讯测试",
		// 			Item: table.WholeRs4852,
		// 		},
		// 		{
		// 			Name: "RJ45I-1通讯测试",
		// 			Item: table.WholeExt11,
		// 		},
		// 		{
		// 			Name: "RJ45I-2通讯测试",
		// 			Item: table.WholeExt12,
		// 		},
		// 		{
		// 			Name: "RJ45II-1通讯测试",
		// 			Item: table.WholeExt21,
		// 		},
		// 		{
		// 			Name: "RJ45II-2通讯测试",
		// 			Item: table.WholeExt22,
		// 		},
		// 		{
		// 			Name: "载波通讯测试",
		// 			Item: table.WholeCarrier,
		// 		},
		// 		{
		// 			Name: "写号",
		// 			Item: table.WholeSetCode,
		// 		},
		// 		{
		// 			Name: "检查写号",
		// 			Item: table.WholeCheckCode,
		// 		},
		// 		{
		// 			Name: "校准",
		// 			Item: table.WholeCalibration,
		// 		},
		// 	},
		// },
		// {
		// 	Name: "整机复测",
		// 	Plan: table.TypeRewholeStr,
		// 	Items: []TestItem{
		// 		{
		// 			Name: "RS485-1通讯测试",
		// 			Item: table.RewholeRs4851,
		// 		},
		// 		{
		// 			Name: "版本号校验",
		// 			Item: table.RewholeVersion,
		// 		},
		// 		{
		// 			Name: "开启指示灯",
		// 			Item: table.RewholeLED,
		// 		},
		// 		{
		// 			Name: "蓝牙测试",
		// 			Item: table.RewholeBT,
		// 		},
		// 		{
		// 			Name: "ESAM测试",
		// 			Item: table.RewholeESAM,
		// 		},
		// 		{
		// 			Name: "RS485-2通讯测试",
		// 			Item: table.RewholeRs4852,
		// 		},
		// 		{
		// 			Name: "RJ45I-1通讯测试",
		// 			Item: table.RewholeExt11,
		// 		},
		// 		{
		// 			Name: "RJ45I-2通讯测试",
		// 			Item: table.RewholeExt12,
		// 		},
		// 		{
		// 			Name: "RJ45II-1通讯测试",
		// 			Item: table.RewholeExt21,
		// 		},
		// 		{
		// 			Name: "RJ45II-2通讯测试",
		// 			Item: table.RewholeExt22,
		// 		},
		// 		{
		// 			Name: "载波通讯测试",
		// 			Item: table.RewholeCarrier,
		// 		},
		// 		{
		// 			Name: "检查写号",
		// 			Item: table.RewholeCheckCode,
		// 		},
		// 		{
		// 			Name: "检查时间",
		// 			Item: table.RewholeCheckTime,
		// 		},
		// 	},
		// },
	}

	c.JSON(http.StatusOK, gin.H{
		"error_code": 0,
		"message":    "成功",
		"plans":      plans,
	})
}
