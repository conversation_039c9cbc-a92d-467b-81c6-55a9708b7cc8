package protocal

import (
	"encoding/binary"
	"errors"
	"fmt"

	"github.com/sigurn/crc16"
)

// Modbus modbus通讯协议
// 协议格式：
//
// 地址码: 1字节，0xFF表示广播地址
// 功能码: 1字节，0x01表示读线圈，0x02表示读离散输入，0x03表示读保持寄存器，0x04表示读输入寄存器，0x05表示写单个线圈，0x06表示写单个寄存器，0x0F表示写多个寄存器，0x10表示写多个保持寄存器
// 数据区: 0~240字节，根据功能码不同，数据长度不同
// CRC校验码: 2字节，采用CRC-16-MODBUS算法
type Modbus struct {
	// 地址码
	SlaveID byte
	// 功能码
	Control byte
	// 数据区
	data []byte
	// CRC表
	crctable *crc16.Table
}

// NewBodbus 创建Modbus协议包
func NewModbus() *Modbus {
	return &Modbus{}
}

// CalcChecksum 计算CRC16校验值
func (m *Modbus) CalcChecksum(data []byte) uint16 {
	// 计算表数据
	if m.crctable == nil {
		m.crctable = crc16.MakeTable(crc16.CRC16_MODBUS)
	}

	return crc16.Checksum(data, m.crctable)
}

// SetData 设置数据区
func (m *Modbus) SetData(data []byte) {
	if len(m.data) != len(data) {
		m.data = make([]byte, len(data))
	}
	copy(m.data, data)
}

// GetData 获取数据区
func (m *Modbus) GetData() []byte {
	data := make([]byte, len(m.data))

	copy(data, m.data)
	return data
}

// Encode 编码Modbus通讯协议
func (m *Modbus) Encode() []byte {
	// 地址码+功能码+数据区+CRC校验码
	data := make([]byte, len(m.data)+4)
	data[0] = m.SlaveID
	data[1] = m.Control
	copy(data[2:], m.data)
	crc := m.CalcChecksum(data[:len(m.data)+2])
	data[len(m.data)+2] = uint8(crc)
	data[len(m.data)+3] = uint8(crc >> 8)
	return data
}

// Decode 解码Modbus通讯协议
// 返回值: int 解码字节数，error 错误信息
func (m *Modbus) Decode(data []byte) (int, error) {
	var retLen int = 0
	var retErr error = errors.New("Modbus: length not enough")
	// 解析报文
	for start := 0; start < len(data)-3; start++ {
		// 判断校验位是否正确
		checksum := m.CalcChecksum(data[start : len(data)-2])
		if checksum != binary.LittleEndian.Uint16(data[len(data)-2:]) {
			retLen = 0
			retErr = fmt.Errorf("Modbus: checksum error, checksum:%04X, need:%04X", checksum, binary.LittleEndian.Uint16(data[len(data)-2:]))
			continue
		}

		// 解析地址码
		m.SlaveID = data[start]
		// 解析功能码
		m.Control = data[start+1]
		// 解析数据区
		m.SetData(data[start+2 : len(data)-2])
		retLen = len(data)
		retErr = nil
		break
	}

	return retLen, retErr
}
