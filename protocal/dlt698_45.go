package protocal

import (
	"encoding/binary"
	"errors"
	"fmt"

	"github.com/sigurn/crc16"
)

// Dlt698_45 面向对象通讯协议
// 协议格式：
//
// 起始字符：1字节，固定为0x68
// 报文长度：2字节，报文总长度，不包含起始字符与结束字符的所有内容字节数
//
//	bit0 ~ bit13: 长度值
//	bit14: 长度单位，0：字节，1：千字节
//
// 控制域：1字节
//
//	bit0 ~ bit2: 功能码，1: 链路管理，3: 用户数据
//	bit3: 扰码标志，0：无扰码，1：有扰码，加扰码时链路用户数据按字节加 33H
//	bit4: 保留位
//	bit5: 分帧标志，0：单帧，1：分帧
//	bit6: 启动标志，0：由服务器发起，1：由客户机发起
//	bit7: 传输方向，0：由客户机发出，1：由服务器发出
//
// 地址域：2+N字节，由1字节服务器地址特征+0或1字节逻辑地址+N字节服务器地址+1字节客户机地址组成
//
//	1字节服务器地址特征组成
//	      bit0 ~ bit3: 地址长度，表示服务器地址字节数，若包含逻辑地址，长度应为服务器地址字节数+1
//	      bit4 ~ bit5: 逻辑地址，bit5为0表示逻辑地址由bit4决定，bit5为1表示服务器地址长度中包含1字节逻辑地址
//	      bit6 ~ bit7: 地址类型，0：单地址，1：通配地址，2：组地址，3：广播地址
//	0或1字节逻辑地址，取值范围: 2~255，当bit5为1时有效
//	N字节服务器地址，BCD码表示，若长度为奇数，则在最后一个字节的bit0~bit3补 FH
//	1字节客户机地址，0表示不关注客户机地址
//
// 帧头校验：2字节，从报文长度到地址域所有数据的校验码
// 链路用户数据：N字节，APDU或APDU片断
// 帧校验：2字节，从报文长度到链路用户数据所有数据的校验码
// 结束字符：1字节，固定为 0x16
type Dlt698_45 struct {
	// 起始标记
	startSign uint8
	// 控制码
	Control uint8
	// 地址域
	address []byte
	// 帧头校验
	hcs uint16
	// 链路用户数据
	content []byte
	// 帧校验
	fcs uint16
	// 结束标记
	endSign uint8
	// CRC表
	crctable *crc16.Table
}

// NewDlt698_45 新建面向对象通讯协议
func NewDlt698_45() *Dlt698_45 {
	return &Dlt698_45{
		startSign: 0x68,
		endSign:   0x16,
	}
}

// CalcChecksum 计算CRC16校验值
func (d *Dlt698_45) CalcChecksum(data []byte) uint16 {
	// 计算表数据
	if d.crctable == nil {
		d.crctable = crc16.MakeTable(crc16.CRC16_X_25)
	}

	return crc16.Checksum(data, d.crctable)
}

// SetAddress 设置地址域
// serverAddress 服务器地址，BCD码表示
//
//	当仅有一个字节AAH时，表示广播地址
//	当有字节内包含AH时，表示通配地址
//	其它情况为单地址
//
// logicAddress 逻辑地址，0表示不包含逻辑地址
// clientAddress 客户机地址，0表示不关注客户机地址
func (d *Dlt698_45) SetAddress(serverAddress []byte, logicAddress uint8, clientAddress uint8) {
	// 服务器地址特征
	addressFeature := uint8(0)
	// 判断地址类型
	if len(serverAddress) == 1 && serverAddress[0] == 0xAA { // 广播地址
		addressFeature |= 0xC0
	} else {
		// 查找地址中是否包含AH
		for _, b := range serverAddress {
			if b&0xA0 == 0xA0 || b&0x0A == 0x0A { // 通配地址
				addressFeature |= 0x40
				break
			}
		}
	}
	// 地址长度
	addressLen := len(serverAddress)
	if logicAddress > 1 {
		addressLen++
		addressFeature |= 0x20
	} else if logicAddress == 1 {
		addressFeature |= 0x10
	}
	addressFeature |= uint8((addressLen - 1) & 0x0F)

	// 写入地址域
	d.address = make([]byte, 2+addressLen)
	d.address[0] = addressFeature
	if logicAddress > 1 {
		d.address[1] = uint8(logicAddress)
		// 服务器地址
		copy(d.address[2:], serverAddress)
	} else {
		// 服务器地址
		copy(d.address[1:], serverAddress)
	}
	// 客户机地址
	d.address[len(d.address)-1] = clientAddress
}

// SetContent 设置链路用户数据，将依据控制域中的扰码标志自动处理
func (d *Dlt698_45) SetContent(content []byte) {
	if len(d.content) != len(content) {
		d.content = make([]byte, len(content))
	}

	// 处理扰码
	if d.Control&0x08 == 0x08 { // 存在扰码
		for i := 0; i < len(content); i++ {
			d.content[i] = content[i] + 0x33
		}
	} else {
		copy(d.content, content)
	}
}

// GetContent 获取链路用户数据
func (d *Dlt698_45) GetContent() []byte {
	content := make([]byte, len(d.content))

	// 处理扰码
	if d.Control&0x08 == 0x08 { // 存在扰码
		for i := 0; i < len(content); i++ {
			content[i] = d.content[i] - 0x33
		}
	} else {
		copy(content, d.content)
	}
	return content
}

// Encode 编码面向对象通讯协议
func (d *Dlt698_45) Encode() []byte {
	data := make([]byte, len(d.address)+len(d.content)+9)
	data[0] = d.startSign
	data[1] = uint8(len(data) - 2)
	data[2] = uint8((len(data) - 2) >> 8)
	data[3] = d.Control
	if len(d.address) > 0 {
		copy(data[4:], d.address)
	}
	// 头校验
	d.hcs = d.CalcChecksum(data[1 : 4+len(d.address)])
	data[4+len(d.address)] = uint8(d.hcs)
	data[5+len(d.address)] = uint8(d.hcs >> 8)
	if len(d.content) > 0 {
		copy(data[6+len(d.address):], d.content)
	}
	// 帧校验
	d.fcs = d.CalcChecksum(data[1 : 6+len(d.address)+len(d.content)])
	data[6+len(d.address)+len(d.content)] = uint8(d.fcs)
	data[7+len(d.address)+len(d.content)] = uint8(d.fcs >> 8)
	data[len(data)-1] = d.endSign
	return data
}

// Decode 解码面向对象通讯协议
// 返回值：int 解码字节数，即解码成功的字节数；error 错误信息
func (d *Dlt698_45) Decode(data []byte) (int, error) {
	var retLen int = 0
	var retErr error = errors.New("Dlt698_45: head not found")
	// 查找报文头
	for start := 0; start < len(data)-10; start++ {
		if data[start] != 0x68 {
			continue
		}

		// 获取报文内容长度
		length := int((uint16(data[start+2]) << 8) | uint16(data[start+1]))
		// 判断报文总长度是否符合数据长度
		if start+length+2 > len(data) {
			retLen = 0
			retErr = errors.New("Dlt698_45: length not enough")
			continue
		}

		// 获得地址域长度
		addressLen := int(data[start+4]&0x0F) + 1
		// 判断地址域长度是否符合报文长度
		if start+addressLen+11 > len(data) {
			retLen = 0
			retErr = errors.New("Dlt698_45: address length not enough")
			continue
		}
		// 判断报文内容长度与地址域长度的关系
		if length < addressLen+9 {
			retLen = 0
			retErr = errors.New("Dlt698_45: length error")
			continue
		}

		// 判断报文尾是否正确
		if data[start+length+1] != 0x16 {
			retLen = 0
			retErr = errors.New("Dlt698_45: tail error")
			continue
		}

		// 判断校验位是否正确
		checksum := d.CalcChecksum(data[start+1 : start+length-1])
		if checksum != binary.LittleEndian.Uint16(data[start+length-1:]) {
			retLen = 0
			retErr = fmt.Errorf("Dlt698_45: checksum error, checksum:%04X, need:%04X", binary.LittleEndian.Uint16(data[start+length-1:]), checksum)
			continue
		}

		// 解析控制域
		d.Control = data[start+3]

		// 解析地址域
		d.address = make([]byte, addressLen+2)
		copy(d.address, data[start+4:start+6+addressLen])

		// 解析帧头校验
		d.hcs = binary.LittleEndian.Uint16(data[start+4+len(d.address) : start+6+len(d.address)])

		// 解析链路用户数据
		d.content = make([]byte, length-len(d.address)-7)
		copy(d.content, data[start+6+len(d.address):start+length-1])

		// 解析帧校验
		d.fcs = binary.LittleEndian.Uint16(data[start+length-1:])

		retLen = start + length + 2
		retErr = nil
		break
	}

	return retLen, retErr
}
