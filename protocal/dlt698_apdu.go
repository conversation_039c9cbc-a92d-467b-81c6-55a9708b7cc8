package protocal

import (
	"archive/zip"
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"os"
	"path"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/sigurn/crc16"
)

// Dlt698_apdu 面向对象通讯协议数据域解析
type Dlt698_apdu struct {
	// 服务序号
	Serial uint8
	// 待传输文件内容
	fileData []byte
	// 传输块大小
	blockSize uint16
	// CRC表
	crctable *crc16.Table
}

// NewDlt698_apdu 构造函数
// blockSize 传输块大小
func NewDlt698_apdu() *Dlt698_apdu {
	return &Dlt698_apdu{
		blockSize: 512,
	}
}

// CalcChecksum 计算CRC16校验值
func (d *Dlt698_apdu) CalcChecksum(data []byte) uint16 {
	// 计算表数据
	if d.crctable == nil {
		d.crctable = crc16.MakeTable(crc16.CRC16_X_25)
	}

	return crc16.Checksum(data, d.crctable)
}

// GetBlockCount 获取文件块数
func (d *Dlt698_apdu) GetBlockCount() int {
	return (len(d.fileData) + int(d.blockSize) - 1) / int(d.blockSize)
}

// readFromZip 读取压缩文件中的文件
func (d *Dlt698_apdu) readFromZip(zipFile string, filename string) (string, int64, error) {
	// 打开压缩文件
	zipReader, err := zip.OpenReader(zipFile)
	if err != nil {
		return "", 0, err
	}
	defer zipReader.Close()
	// 解压文件
	f, err := zipReader.Open(filename)
	if err != nil {
		return "", 0, err
	}
	defer f.Close()
	// 读取文件内容
	fi, err := f.Stat()
	if err != nil {
		return "", 0, err
	}
	fileData := make([]byte, fi.Size())
	_, err = f.Read(fileData)
	if err != nil && err != io.EOF {
		return "", 0, err
	}
	d.fileData = fileData
	return fi.Name(), fi.Size(), nil
}

// getLengthNonFix 读取非固定长度数据
// pos: 长度数据开始的位置
// 返回值: 长度值, 数据开始位置, 错误信息
func (d *Dlt698_apdu) getLengthNonFix(data []byte, pos int) (int, int, error) {
	datalen := 0
	if len(data) < pos+1 {
		return 0, 0, errors.New("无长度信息")
	}

	// 单字节长度
	if data[pos]&0x80 == 0 {
		datalen = int(data[pos])
		// 判断数据长度
		if len(data) < pos+1+datalen {
			return datalen, pos + 1, errors.New("数据长度不足")
		}
		// 获取数据
		return datalen, pos + 1, nil
	}

	// 多字节长度
	msize := data[pos] & 0x7F
	if len(data) < pos+1+int(msize) {
		return 0, 0, errors.New("长度信息不足")
	}
	for size := 0; size < int(msize); size++ {
		datalen = (datalen << 8) | int(data[pos+1+size])
	}
	// 判断数据长度
	if len(data) < pos+1+int(msize)+datalen {
		return datalen, pos + 1 + int(msize), errors.New("数据长度不足")
	}
	// 获取数据
	return datalen, pos + 1 + int(msize), nil
}

// FileUpdateStart 启动文件上传
func (d *Dlt698_apdu) FileUpdateStart(filepath string) ([]byte, error) {
	datas := make([]byte, 0, 128)

	var filesize int64
	var filename string
	var tofilename string
	// 判断源文件是否有压缩文件
	if strings.Contains(filepath, "!") {
		paths := strings.Split(filepath, "!")
		// 打开压缩文件
		var err error
		filename, filesize, err = d.readFromZip(paths[0], paths[1])
		if err != nil {
			return nil, err
		}
		tofilename = "/" + strings.ReplaceAll(path.Dir(paths[1]), "\\", "/")
		log.Debug().Str("filename", filename).Int64("filesize", filesize).Str("tofilename", tofilename).Msg("从压缩文件读取文件内容")
	} else {
		// 读取文件内容
		fileData, err := os.ReadFile(filepath)
		if err != nil {
			return nil, err
		}
		d.fileData = fileData

		// 读取文件大小
		fi, err := os.Stat(filepath)
		if err != nil {
			return nil, err
		}
		filesize = fi.Size()
		// 读取文件名
		filename = fi.Name()
		tofilename = "/" + strings.ReplaceAll(path.Dir(filepath), "\\", "/")
		log.Debug().Str("filename", filename).Int64("filesize", filesize).Str("tofilename", tofilename).Msg("从目录中读取文件内容")
	}

	// 组织APDU
	// 操作一个对象方法
	datas = append(datas, 0x07)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OMD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xF1)
	datas = append(datas, 0x07)
	datas = append(datas, 0x00)
	// 3项数据的结构体
	datas = append(datas, 0x02)
	datas = append(datas, 0x03)
	// 文件信息，6项数据的结构体
	datas = append(datas, 0x02)
	datas = append(datas, 0x06)
	// 源文件，字符串
	datas = append(datas, 0x0A)
	datas = append(datas, uint8(len(filename)))
	datas = append(datas, filename...)
	// 目标文件，字符串
	toname := []byte(tofilename)
	datas = append(datas, 0x0A)
	datas = append(datas, uint8(len(toname)))
	datas = append(datas, toname...)
	// 文件大小，4字节
	datas = append(datas, 0x06)
	datas = append(datas, uint8(filesize>>24))
	datas = append(datas, uint8(filesize>>16))
	datas = append(datas, uint8(filesize>>8))
	datas = append(datas, uint8(filesize))
	// 文件属性，可读写执行
	datas = append(datas, 0x04)
	datas = append(datas, 0x03)
	datas = append(datas, 0xE0)
	// 文件版本
	datas = append(datas, 0x0A)
	datas = append(datas, 0x04)
	datas = append(datas, 0x30)
	datas = append(datas, 0x30)
	datas = append(datas, 0x30)
	datas = append(datas, 0x30)
	// 文件类别，当前设备文件
	datas = append(datas, 0x16)
	datas = append(datas, 0x00)

	// 传输块大小
	datas = append(datas, 0x12)
	datas = append(datas, uint8(d.blockSize>>8))
	datas = append(datas, uint8(d.blockSize))
	// 校验，2项数据的结构体
	datas = append(datas, 0x02)
	datas = append(datas, 0x02)
	// 校验类型，CRC16
	datas = append(datas, 0x16)
	datas = append(datas, 0x00)
	// 校验值，2字节
	datas = append(datas, 0x09)
	datas = append(datas, 0x02)
	// 校验计算
	crc := d.CalcChecksum(d.fileData)
	datas = append(datas, uint8(crc>>8))
	datas = append(datas, uint8(crc))
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// FileUpdateStartResponse 启动文件上传响应解析
func (d *Dlt698_apdu) FileUpdateStartResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("启动文件上传响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x87 || data[1] != 0x01 {
		return errors.New("启动文件上传响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0xF1 || data[5] != 0x07 || data[6] != 0x00 {
		return errors.New("启动文件上传响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("启动文件上传响应: 执行失败, %02X", data[7])
	}
	return nil
}

// FileUpdatePart 分块文件上传
// id: 块序号，从0开始
func (d *Dlt698_apdu) FileUpdatePart(id int) ([]byte, error) {
	datas := make([]byte, 0, 640)

	// 组织APDU
	// 操作一个对象方法
	datas = append(datas, 0x07)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OMD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xF1)
	datas = append(datas, 0x08)
	datas = append(datas, 0x00)
	// 2项数据的结构体
	datas = append(datas, 0x02)
	datas = append(datas, 0x02)
	// 块序号
	datas = append(datas, 0x12)
	datas = append(datas, uint8(id>>8))
	datas = append(datas, uint8(id))
	// 块数据
	datas = append(datas, 0x09)
	datas = append(datas, 0x82)
	dlen := int(d.blockSize)
	if (id+1)*int(d.blockSize) >= len(d.fileData) {
		dlen = len(d.fileData) - id*int(d.blockSize)
	}
	datas = append(datas, uint8(dlen>>8))
	datas = append(datas, uint8(dlen))
	datas = append(datas, d.fileData[id*int(d.blockSize):id*int(d.blockSize)+dlen]...)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// FileUpdatePartResponse 分块文件上传响应解析
func (d *Dlt698_apdu) FileUpdatePartResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("分块文件上传响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x87 || data[1] != 0x01 {
		return errors.New("分块文件上传响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0xF1 || data[5] != 0x08 || data[6] != 0x00 {
		return errors.New("分块文件上传响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("分块文件上传响应: 执行失败, %02X", data[7])
	}
	return nil
}

// FileUpdateStatus 分块文件上传状态
func (d *Dlt698_apdu) FileUpdateStatus() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xF1)
	datas = append(datas, 0x04)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// FileUpdateStatusResponse 分块文件上传状态响应解析
// 返回结果数据
func (d *Dlt698_apdu) FileUpdateStatusResponse(data []byte) ([]byte, error) {
	// 判断数据长度
	if len(data) < 10 {
		return nil, errors.New("分块文件上传状态响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("分块文件上传状态响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0xF1 || data[5] != 0x04 || data[6] != 0x00 {
		return nil, errors.New("分块文件上传状态响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("分块文件上传状态响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x04 {
		return nil, errors.New("分块文件上传状态响应: 结果数据类型错误")
	}
	// 获取数据长度
	datalen, pos, err := d.getLengthNonFix(data, 9)
	if err != nil {
		return nil, errors.New("分块文件上传状态响应: " + err.Error())
	}
	return data[pos : pos+datalen], nil
}

// WriteAddress 写入通讯地址
func (d *Dlt698_apdu) WriteAddress(address []byte) ([]byte, error) {
	datas := make([]byte, 0, len(address)+10)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x41)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x09)
	datas = append(datas, uint8(len(address)))
	datas = append(datas, address...)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteAddressResponse 写入通讯地址响应解析
func (d *Dlt698_apdu) WriteAddressResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入通讯地址响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入通讯地址响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0x41 || data[5] != 0x02 || data[6] != 0x00 {
		return errors.New("写入通讯地址响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入通讯地址响应: 执行失败, %02X", data[7])
	}
	return nil
}

// WriteMeterId 写入电表号
func (d *Dlt698_apdu) WriteMeterId(number []byte) ([]byte, error) {
	datas := make([]byte, 0, len(number)+10)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x42)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x09)
	datas = append(datas, uint8(len(number)))
	datas = append(datas, number...)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteMeterIdResponse 写入电表号响应解析
func (d *Dlt698_apdu) WriteMeterIdResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入电表号响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入电表号响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0x42 || data[5] != 0x02 || data[6] != 0x00 {
		return errors.New("写入电表号响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入电表号响应: 执行失败, %02X", data[7])
	}
	return nil
}

// WriteAssetCode 写入资产管理编号
func (d *Dlt698_apdu) WriteAssetCode(code string) ([]byte, error) {
	datas := make([]byte, 0, len(code)+10)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x43)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x0A)
	datas = append(datas, uint8(len(code)))
	datas = append(datas, []byte(code)...)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteAssetCodeResponse 写入资产管理编号响应解析
func (d *Dlt698_apdu) WriteAssetCodeResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入资产管理编号响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入资产管理编号响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0x43 || data[5] != 0x02 || data[6] != 0x00 {
		return errors.New("写入资产管理编号响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入资产管理编号响应: 执行失败, %02X", data[7])
	}
	return nil
}

// WriteBoardInfo 写入板子版本信息
func (d *Dlt698_apdu) WriteBoardInfo(hardVersion, hardDate, vendorCode, vendorExt string) ([]byte, error) {
	datas := make([]byte, 0, 128)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x03)
	datas = append(datas, 0x00)
	// 6项数据的结构体
	datas = append(datas, 0x02)
	datas = append(datas, 0x06)
	// 厂商代码，字符串
	datas = append(datas, 0x0A)
	datas = append(datas, 0x04)
	tmp := make([]byte, 4)
	copy(tmp, []byte(vendorCode))
	datas = append(datas, tmp...)
	// 软件版本号，留空
	datas = append(datas, 0x0A)
	datas = append(datas, 0x00)
	// 软件版本日期，留空
	datas = append(datas, 0x0A)
	datas = append(datas, 0x00)
	// 硬件版本号，字符串
	datas = append(datas, 0x0A)
	datas = append(datas, 0x04)
	tmp = make([]byte, 4)
	copy(tmp, []byte(hardVersion))
	datas = append(datas, tmp...)
	// 硬件版本日期，字符串
	datas = append(datas, 0x0A)
	datas = append(datas, 0x06)
	tmp = make([]byte, 6)
	copy(tmp, []byte(hardDate))
	datas = append(datas, tmp...)
	// 厂商扩展信息，字符串
	datas = append(datas, 0x0A)
	datas = append(datas, 0x08)
	tmp = make([]byte, 8)
	copy(tmp, []byte(vendorExt))
	datas = append(datas, tmp...)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteBoardInfoResponse 写入板子版本信息响应解析
func (d *Dlt698_apdu) WriteBoardInfoResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入板子版本信息响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入板子版本信息响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x03 || data[6] != 0x00 {
		return errors.New("写入板子版本信息响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入板子版本信息响应: 执行失败, %02X", data[7])
	}
	return nil
}

// WriteProduceDate 写入生产日期
func (d *Dlt698_apdu) WriteProduceDate(date *time.Time) ([]byte, error) {
	datas := make([]byte, 0, 16)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x04)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x1C)
	log.Debug().Uint("year", uint(date.Year())).Uint8("month", uint8(date.Month())).Uint8("day", uint8(date.Day())).Msgf("date: %v", date)
	// 年
	datas = binary.BigEndian.AppendUint16(datas, uint16(date.Year()))
	// 月日时分秒
	datas = append(datas, byte(date.Month()), byte(date.Day()), 0, 0, 0)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteProduceDateResponse 写入生产日期响应解析
func (d *Dlt698_apdu) WriteProduceDateResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入生产日期响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入生产日期响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x04 || data[6] != 0x00 {
		return errors.New("写入生产日期响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入生产日期响应: 执行失败, %02X", data[7])
	}
	return nil
}

// WriteSafeMode 写入安全模式，是否开机自动启用安全模式
func (d *Dlt698_apdu) WriteSafeMode(enable bool) ([]byte, error) {
	datas := make([]byte, 0, 10)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xF2)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x16)
	if enable {
		datas = append(datas, 0x01)
	} else {
		datas = append(datas, 0x00)
	}
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// WriteSafeModeResponse 写入安全模式响应解析
func (d *Dlt698_apdu) WriteSafeModeResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("写入安全模式响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("写入安全模式响应: 操作类型错误")
	}
	// 解析 OAD
	if data[3] != 0xFF || data[4] != 0xF2 || data[5] != 0x02 || data[6] != 0x00 {
		return errors.New("写入安全模式响应: OAD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("写入安全模式响应: 执行失败, %02X", data[7])
	}
	return nil
}

// ReadAddress 读取通讯地址
func (d *Dlt698_apdu) ReadAddress() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x41)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadAddressResponse 读取通讯地址响应解析
// 返回通讯地址
func (d *Dlt698_apdu) ReadAddressResponse(data []byte) ([]byte, error) {
	// 判断数据长度
	if len(data) < 11 {
		return nil, errors.New("读取通讯地址响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("读取通讯地址响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x41 || data[5] != 0x02 || data[6] != 0x00 {
		return nil, errors.New("读取通讯地址响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("读取通讯地址响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x09 {
		return nil, errors.New("读取通讯地址响应: 结果数据类型错误")
	}
	// 获取数据长度
	datalen, pos, err := d.getLengthNonFix(data, 9)
	if err != nil {
		return nil, errors.New("读取通讯地址响应: " + err.Error())
	}
	return data[pos : pos+datalen], nil
}

// ReadMeterId 读取电表号
func (d *Dlt698_apdu) ReadMeterId() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x42)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadMeterIdResponse 读取电表号响应解析
// 返回电表号
func (d *Dlt698_apdu) ReadMeterIdResponse(data []byte) ([]byte, error) {
	// 判断数据长度
	if len(data) < 11 {
		return nil, errors.New("读取电表号响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("读取电表号响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x42 || data[5] != 0x02 || data[6] != 0x00 {
		return nil, errors.New("读取电表号响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("读取电表号响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x09 {
		return nil, errors.New("读取电表号响应: 结果数据类型错误")
	}
	// 获取数据长度
	datalen, pos, err := d.getLengthNonFix(data, 9)
	if err != nil {
		return nil, errors.New("读取电表号响应: " + err.Error())
	}
	return data[pos : pos+datalen], nil
}

// ReadAssetCode 读取资产管理编号
func (d *Dlt698_apdu) ReadAssetCode() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x43)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadAssetCodeResponse 读取资产管理编号响应解析
// 返回资产管理编号
func (d *Dlt698_apdu) ReadAssetCodeResponse(data []byte) (string, error) {
	// 判断数据长度
	if len(data) < 11 {
		return "", errors.New("读取资产管理编号响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return "", errors.New("读取资产管理编号响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x43 || data[5] != 0x02 || data[6] != 0x00 {
		return "", errors.New("读取资产管理编号响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return "", fmt.Errorf("读取资产管理编号响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x0A {
		return "", errors.New("读取资产管理编号响应: 结果数据类型错误")
	}
	// 获取数据长度
	datalen, pos, err := d.getLengthNonFix(data, 9)
	if err != nil {
		return "", errors.New("读取资产管理编号响应: " + err.Error())
	}
	return string(data[pos : pos+datalen]), nil
}

// ReadBoardInfo 读取板子版本信息
func (d *Dlt698_apdu) ReadBoardInfo() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x03)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadBoardInfoResponse 读取板子版本信息响应解析
// 返回 硬件版本号、硬件版本日期、软件版本、软件日期、厂商代码、厂家扩展信息
func (d *Dlt698_apdu) ReadBoardInfoResponse(data []byte) (string, string, string, string, string, string, error) {
	// 判断数据长度
	if len(data) < 11 {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x03 || data[6] != 0x00 {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return "", "", "", "", "", "", fmt.Errorf("读取板子版本信息响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x02 || data[9] != 0x06 {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 结果数据类型错误")
	}
	pos := 10
	// 厂家商码为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 厂商代码类型错误")
	}
	// 获取厂商代码 数据长度
	datalen, pos, err := d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取厂商代码长度: " + err.Error())
	}
	// 读取厂商代码
	vendorCode := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	pos += datalen
	// 软件版本号为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 软件版本号类型错误")
	}
	// 跳过软件版本号
	datalen, pos, err = d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取软件版本号长度: " + err.Error())
	}
	// 读取软件版本号
	softVersion := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	pos += datalen
	// 软件版本日期为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 软件版本日期类型错误")
	}
	// 跳过软件版本日期
	datalen, pos, err = d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取软件版本日期长度: " + err.Error())
	}
	// 读取软件版本日期
	softDate := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	pos += datalen
	// 硬件版本号为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 硬件版本号类型错误")
	}
	// 获取硬件版本号 数据长度
	datalen, pos, err = d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取硬件版本号长度: " + err.Error())
	}
	// 读取硬件版本号
	hardVersion := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	pos += datalen
	// 硬件版本日期为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 硬件版本日期类型错误")
	}
	// 获取硬件版本日期 数据长度
	datalen, pos, err = d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取硬件版本日期长度: " + err.Error())
	}
	// 读取硬件版本日期
	hardDate := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	pos += datalen
	// 厂家扩展信息为字符串，第1个字节为0x0A
	if data[pos] != 0x0A {
		return "", "", "", "", "", "", errors.New("读取板子版本信息响应: 厂家扩展信息类型错误")
	}
	// 获取厂家扩展信息 数据长度
	datalen, pos, err = d.getLengthNonFix(data, pos+1)
	if err != nil {
		return "", "", "", "", "", "", errors.New("获取厂家扩展信息长度: " + err.Error())
	}
	// 读取厂家扩展信息
	vendorExt := string(bytes.Trim(data[pos:pos+datalen], "\x00\xFF"))

	return hardVersion, hardDate, softVersion, softDate, vendorCode, vendorExt, nil
}

// ReadProduceDate 读取生产日期
func (d *Dlt698_apdu) ReadProduceDate() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x04)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadProduceDateResponse 读取生产日期响应解析
// 返回生产日期
func (d *Dlt698_apdu) ReadProduceDateResponse(data []byte) (*time.Time, error) {
	// 判断数据长度
	if len(data) < 11 {
		return nil, errors.New("读取生产日期响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("读取生产日期响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x04 || data[6] != 0x00 {
		return nil, errors.New("读取生产日期响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("读取生产日期响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x1C {
		return nil, errors.New("读取生产日期响应: 结果数据类型错误")
	}
	// 判断数据长度
	if len(data) < 18 {
		return nil, errors.New("读取生产日期响应: 响应长度不足")
	}
	// 转换日期
	date, err := time.Parse("20060102", fmt.Sprintf("%04d%02d%02d", binary.BigEndian.Uint16(data[9:11]), data[11], data[12]))
	if err != nil {
		return nil, errors.New("读取生产日期响应: 日期转换错误, " + err.Error())
	}

	return &date, nil
}

// ReadSafeMode 读取安全模式
func (d *Dlt698_apdu) ReadSafeMode() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xF1)
	datas = append(datas, 0x01)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadSafeModeResponse 读取安全模式响应解析
// 返回安全模式
func (d *Dlt698_apdu) ReadSafeModeResponse(data []byte) (bool, error) {
	// 判断数据长度
	if len(data) < 11 {
		return false, errors.New("读取安全模式响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return false, errors.New("读取安全模式响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xF1 || data[4] != 0x01 || data[5] != 0x02 || data[6] != 0x00 {
		return false, errors.New("读取安全模式响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return false, fmt.Errorf("读取安全模式响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x16 {
		return false, errors.New("读取安全模式响应: 结果数据类型错误")
	}
	return data[9] == 0x01, nil
}

// SetLedStatus 设置指示灯状态
// status: true 表示启用，false 表示禁用
func (d *Dlt698_apdu) SetLedStatus(status bool) ([]byte, error) {
	datas := make([]byte, 0, 10)

	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x07)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xFE)
	datas = append(datas, 0x7F)
	datas = append(datas, 0x00)
	// 数据内容
	datas = append(datas, 0x16)
	if status {
		datas = append(datas, 0x01)
	} else {
		datas = append(datas, 0x00)
	}
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// SetLedStatusResponse 设置指示灯状态响应解析
func (d *Dlt698_apdu) SetLedStatusResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("设置指示灯状态响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x87 || data[1] != 0x01 {
		return errors.New("设置指示灯状态响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0xFE || data[5] != 0x7F || data[6] != 0x00 {
		return errors.New("设置指示灯状态响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("设置指示灯状态响应: 执行失败, %02X", data[7])
	}
	return nil
}

// ReadEsamSn 读取ESAM序列号
func (d *Dlt698_apdu) ReadEsamSn() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xF1)
	datas = append(datas, 0x00)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadEsamSnResponse 读取ESAM序列号响应解析
// 返回ESAM序列号
func (d *Dlt698_apdu) ReadEsamSnResponse(data []byte) (string, error) {
	// 判断数据长度
	if len(data) < 11 {
		return "", errors.New("读取ESAM序列号响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return "", errors.New("读取ESAM序列号响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xF1 || data[4] != 0x00 || data[5] != 0x02 || data[6] != 0x00 {
		return "", errors.New("读取ESAM序列号响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return "", fmt.Errorf("读取ESAM序列号响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x09 {
		return "", errors.New("读取ESAM序列号响应: 结果数据类型错误")
	}
	// 读取ESAM序列号
	datalen, pos, err := d.getLengthNonFix(data, 9)
	if err != nil {
		return "", errors.New("读取ESAM序列号长度: " + err.Error())
	}
	esamSn := hex.EncodeToString(data[pos : pos+datalen])

	return esamSn, nil
}

// ReadVoltage 读取电压
func (d *Dlt698_apdu) ReadVoltage() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0x20)
	datas = append(datas, 0x00)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadVoltageResponse 读取电压响应解析
// 返回电压值
func (d *Dlt698_apdu) ReadVoltageResponse(data []byte) ([]float32, error) {
	// 判断数据长度
	if len(data) < 12 {
		return nil, errors.New("读取电压响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("读取电压响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0x20 || data[4] != 0x00 || data[5] != 0x02 || data[6] != 0x00 {
		return nil, errors.New("读取电压响应: OMD 标识符错误")
	}

	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("读取电压响应: 执行失败, %02X", data[7])
	}

	// 解析结果数据
	if data[8] != 0x01 {
		return nil, errors.New("读取电压响应: 结果数据类型错误")
	}

	// 得到数据个数
	count := data[9]
	// 判断数据长度
	if len(data) < 10+int(count)*3 {
		return nil, errors.New("读取电压响应: 响应长度不足")
	}

	if count > 0 {
		vs := make([]float32, count)
		for i := 0; i < int(count); i++ {
			// 转换电压值
			voltage := binary.BigEndian.Uint16(data[11+i*3 : 13+i*3])
			vs[i] = float32(voltage) / 10.0
		}

		return vs, nil
	}

	return nil, nil
}

// MeterCalibration 电表校准
func (d *Dlt698_apdu) MeterCalibration(voltage float32) ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 操作一个对象方法
	datas = append(datas, 0x07)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OMD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x20)
	datas = append(datas, 0x80)
	datas = append(datas, 0x00)
	// 数据类型
	datas = append(datas, 0x12)
	// 数据内容
	datas = binary.BigEndian.AppendUint16(datas, uint16(voltage*10))
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// MeterCalibrationResponse 电表校准响应解析
func (d *Dlt698_apdu) MeterCalibrationResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("电表校准响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x87 || data[1] != 0x01 {
		return errors.New("电表校准响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x20 || data[5] != 0x80 || data[6] != 0x00 {
		return errors.New("电表校准响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("电表校准响应: 执行失败, %02X", data[7])
	}
	return nil
}

// ReadTime 读取时间
func (d *Dlt698_apdu) ReadTime() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0x40)
	datas = append(datas, 0x00)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadTimeResponse 读取时间响应解析
// 返回时间
func (d *Dlt698_apdu) ReadTimeResponse(data []byte) (*time.Time, error) {
	// 判断数据长度
	if len(data) < 16 {
		return nil, errors.New("读取时间响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return nil, errors.New("读取时间响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0x40 || data[4] != 0x00 || data[5] != 0x02 || data[6] != 0x00 {
		return nil, errors.New("读取时间响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return nil, fmt.Errorf("读取时间响应: 执行失败, %02X", data[7])
	}
	// 读取时间
	year := binary.BigEndian.Uint16(data[9:11])
	month := data[11]
	day := data[12]
	hour := data[13]
	minute := data[14]
	second := data[15]

	getTime := time.Date(int(year), time.Month(month), int(day), int(hour), int(minute), int(second), 0, time.Local)
	return &getTime, nil
}

// SetTime 设置时间
func (d *Dlt698_apdu) SetTime(t time.Time) ([]byte, error) {
	datas := make([]byte, 0, 16)
	// 组织APDU
	// 设置一个对象属性
	datas = append(datas, 0x06)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0x40)
	datas = append(datas, 0x00)
	datas = append(datas, 0x02)
	datas = append(datas, 0x00)
	// 数据类型
	datas = append(datas, 0x1c)
	// 数据内容
	datas = binary.BigEndian.AppendUint16(datas, uint16(t.Year()))
	datas = append(datas, uint8(t.Month()))
	datas = append(datas, uint8(t.Day()))
	datas = append(datas, uint8(t.Hour()))
	datas = append(datas, uint8(t.Minute()))
	datas = append(datas, uint8(t.Second()))
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// SetTimeResponse 设置时间响应解析
func (d *Dlt698_apdu) SetTimeResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("设置时间响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x86 || data[1] != 0x01 {
		return errors.New("设置时间响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0x40 || data[4] != 0x00 || data[5] != 0x02 || data[6] != 0x00 {
		return errors.New("设置时间响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("设置时间响应: 执行失败, %02X", data[7])
	}
	return nil
}

// FormatFS 文件系统格式化
func (d *Dlt698_apdu) FormatFS() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 操作一个对象方法
	datas = append(datas, 0x07)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0xF3)
	datas = append(datas, 0x80)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// FormatFSResponse 文件系统格式化响应解析
func (d *Dlt698_apdu) FormatFSResponse(data []byte) error {
	// 判断数据长度
	if len(data) < 8 {
		return errors.New("文件系统格式化响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x87 || data[1] != 0x01 {
		return errors.New("文件系统格式化响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0xF3 || data[5] != 0x80 || data[6] != 0x00 {
		return errors.New("文件系统格式化响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x00 {
		return fmt.Errorf("文件系统格式化响应: 执行失败, %02X", data[7])
	}
	return nil
}

// ReadStaStatus 读取STA通讯状态，是否已通过过
func (d *Dlt698_apdu) ReadStaStatus() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x3D)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadStaStatusResponse 读取STA通讯状态响应解析
func (d *Dlt698_apdu) ReadStaStatusResponse(data []byte) (bool, error) {
	// 判断数据长度
	if len(data) < 10 {
		return false, errors.New("读取STA通讯状态响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return false, errors.New("读取STA通讯状态响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x3D || data[6] != 0x00 {
		return false, errors.New("读取STA通讯状态响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return false, fmt.Errorf("读取STA通讯状态响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x16 {
		return false, errors.New("读取STA通讯状态响应: 结果数据类型错误")
	}
	// 解析通讯状态
	return data[9] == 0x01, nil
}

// ReadBtStatus 读取蓝牙状态及MAC地址
func (d *Dlt698_apdu) ReadBtStatus() ([]byte, error) {
	datas := make([]byte, 0, 8)

	// 组织APDU
	// 读取一个对象属性
	datas = append(datas, 0x05)
	datas = append(datas, 0x01)
	// 服务序号
	datas = append(datas, d.Serial&0x3F)
	d.Serial++
	// OAD 标识符
	datas = append(datas, 0xFF)
	datas = append(datas, 0x44)
	datas = append(datas, 0x3E)
	datas = append(datas, 0x00)
	// 时间标签，无
	datas = append(datas, 0x00)

	return datas, nil
}

// ReadBtStatusResponse 读取蓝牙状态及MAC地址响应解析
func (d *Dlt698_apdu) ReadBtStatusResponse(data []byte) (bool, string, error) {
	// 判断数据长度
	if len(data) < 16 {
		return false, "", errors.New("读取蓝牙状态响应: 响应长度错误")
	}
	// 解析操作类型
	if data[0] != 0x85 || data[1] != 0x01 {
		return false, "", errors.New("读取蓝牙状态响应: 操作类型错误")
	}
	// 解析 OMD
	if data[3] != 0xFF || data[4] != 0x44 || data[5] != 0x3E || data[6] != 0x00 {
		return false, "", errors.New("读取蓝牙状态响应: OMD 标识符错误")
	}
	// 解析执行状态
	if data[7] != 0x01 {
		return false, "", fmt.Errorf("读取蓝牙状态响应: 执行失败, %02X", data[7])
	}
	// 解析结果数据
	if data[8] != 0x02 || data[9] != 0x02 {
		return false, "", errors.New("读取蓝牙状态响应: 结果数据类型错误")
	}
	// 解析蓝牙状态
	if data[10] != 0x16 {
		return false, "", errors.New("读取蓝牙状态响应: 蓝牙状态类型错误")
	}
	if data[11] != 1 {
		return false, "", nil
	}
	// 解析MAC地址
	if data[12] != 0x09 {
		return true, "", errors.New("读取蓝牙状态响应: MAC地址类型错误")
	}
	datalen, pos, err := d.getLengthNonFix(data, 13)
	if err != nil {
		return true, "", errors.New("获取厂家扩展信息长度: " + err.Error())
	}
	return true, hex.EncodeToString(data[pos : pos+datalen]), nil
}
