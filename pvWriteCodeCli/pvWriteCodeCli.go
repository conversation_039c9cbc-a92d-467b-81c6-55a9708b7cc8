package main

import (
	"archive/zip"
	"flag"
	"fmt"
	"os"
	"pvFactoryBackend/base"
	"pvFactoryBackend/communication"
	"pvFactoryBackend/device"
	"runtime/debug"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"go.bug.st/serial"
)

const (
	VERSION = "1.0"
)

// Args 命令行参数处理
type PvArgs struct {
	// 通讯串口名称
	CommPort string
	// 通讯波特率
	CommBaudRate int

	// 读取资产编码
	ReadAssetCode bool
	// 读取通讯地址
	ReadAddress bool
	// 读取电表号
	ReadMeterID bool
	// 读取硬件版本
	ReadHardVersion bool
	// 读取硬件日期
	ReadHardDate bool
	// 读取厂商代码
	ReadVendorCode bool
	// 读取厂商扩展信息
	ReadVendorExt bool
	// 读取生产日期
	ReadProduceDate bool
	// 读取安全模式
	ReadSafeMode bool
	// 读取所有信息
	ReadAll bool

	// 写入资产编码
	AssetCode string
	// 写入通讯地址
	Address string
	// 写入电表号
	MeterID string
	// 写入硬件版本
	HardVersion string
	// 写入硬件日期
	HardDate string
	// 写入厂商代码
	VendorCode string
	// 写入厂商扩展信息
	VendorExt string
	// 写入生产日期
	ProduceDate string
	// 写入安全模式
	SafeMode string
	// 写入逆变器信息文件
	InverterInfo string

	// 调试模式
	Debug bool
	// 版本信息
	Version string
}

// Init 初始化参数
func (sels *PvArgs) Init() bool {
	flag.StringVar(&sels.CommPort, "c", "COM1", "指定通讯串口名称")
	flag.IntVar(&sels.CommBaudRate, "b", 9600, "指定通讯波特率")

	flag.BoolVar(&sels.ReadAssetCode, "rac", false, "读取资产编码")
	flag.BoolVar(&sels.ReadAddress, "ra", false, "读取通讯地址")
	flag.BoolVar(&sels.ReadMeterID, "rmi", false, "读取电表号")
	flag.BoolVar(&sels.ReadHardVersion, "rhv", false, "读取硬件版本")
	flag.BoolVar(&sels.ReadHardDate, "rhd", false, "读取硬件日期")
	flag.BoolVar(&sels.ReadVendorCode, "rvc", false, "读取厂商代码")
	flag.BoolVar(&sels.ReadVendorExt, "rve", false, "读取厂商扩展信息")
	flag.BoolVar(&sels.ReadProduceDate, "rpd", false, "读取生产日期")
	flag.BoolVar(&sels.ReadSafeMode, "rsm", false, "读取安全模式")

	flag.StringVar(&sels.AssetCode, "wac", "", "写入资产编码，22位10进制数值")
	flag.StringVar(&sels.Address, "wa", "", "写入通讯地址，12位10进制数值")
	flag.StringVar(&sels.MeterID, "wmi", "", "写入电表号，12位10进制数值")
	flag.StringVar(&sels.HardVersion, "whv", "", "写入硬件版本，不超过4个字符")
	flag.StringVar(&sels.HardDate, "whd", "", "写入硬件日期，不超过6个字符")
	flag.StringVar(&sels.VendorCode, "wvc", "", "写入厂商代码，不超过4个字符")
	flag.StringVar(&sels.VendorExt, "wve", "", "写入厂商扩展信息，不超过8个字符")
	flag.StringVar(&sels.ProduceDate, "wpd", "", "写入生产日期，格式: YYYYMMDD")
	flag.StringVar(&sels.SafeMode, "wsm", "", "写入安全模式，0或1，0表示关闭，1表示开启")
	flag.StringVar(&sels.InverterInfo, "wii", "", "写入逆变器信息文件，zip格式的逆变器信息文件路径")

	flag.BoolVar(&sels.ReadAll, "a", false, "读取所有信息")
	flag.BoolVar(&sels.Debug, "debug", false, "指定为调试模式，显示更多信息")

	flag.Usage = sels.usage
	flag.Parse()

	return flag.NArg() <= 0
}

func (a *PvArgs) usage() {
	fmt.Fprint(os.Stderr, os.Args[0], "\nversion: ", a.Version, "\n用法: ", os.Args[0], " [-debug]\n")
	flag.PrintDefaults()
}

func main() {
	defer func() {
		if r := recover(); r != nil {
			log.Fatal().Msgf("main 程序运行异常: %v", r)
			log.Fatal().Msgf("stacktrace from panic: %v", string(debug.Stack()))
		}
	}()

	// 命令参数
	args := &PvArgs{
		// 初始化版本号
		Version: VERSION,
	}
	// 识别命令行
	if !args.Init() {
		os.Exit(1)
	}

	// 读取配置文件
	config := base.NewConfig("")
	config.Load()

	// 判断是否为调试模式
	if args.Debug {
		config.Log.StdLevel = 0
	}
	// 初始化日志
	base.InitLog(config.Log)
	log.Debug().Str("服务版本", args.Version).Any("配置", config).Msg("读取配置成功")

	// 创建通讯对象
	comm := communication.NewSerialLink(args.CommPort)
	comm.Baud = args.CommBaudRate
	comm.Parity = serial.EvenParity

	dlt698 := device.NewDlt698Helper()
	err := dlt698.Init(comm)
	if err != nil {
		log.Err(err).Msg("初始化失败")
		return
	}

	// 读取所有信息
	if args.ReadAll {
		// 将其它读取信息的命令行参数置为true
		args.ReadAssetCode = true
		args.ReadAddress = true
		args.ReadMeterID = true
		args.ReadHardVersion = true
		args.ReadHardDate = true
		args.ReadVendorCode = true
		args.ReadVendorExt = true
		args.ReadProduceDate = true
		args.ReadSafeMode = true
	}

	// 读取资产编码
	if args.ReadAssetCode {
		asset, err := dlt698.ReadAssetCode()
		if err != nil {
			log.Err(err).Msg("读取资产编码失败")
			return
		}
		log.Info().Msgf("资产编码：%s", asset)
	}

	// 读取通讯地址
	if args.ReadAddress {
		addr, err := dlt698.ReadAddress(0)
		if err != nil {
			log.Err(err).Msg("读取通讯地址失败")
			return
		}
		log.Info().Msgf("通讯地址：%s", addr)
	}

	// 读取电表号
	if args.ReadMeterID {
		meter, err := dlt698.ReadMeterId()
		if err != nil {
			log.Err(err).Msg("读取电表号失败")
			return
		}
		log.Info().Msgf("电表号：%s", meter)
	}

	// 读取板子版本信息
	if args.ReadHardVersion || args.ReadHardDate || args.ReadVendorCode || args.ReadVendorExt {
		hardVersion, hardDate, _, _, vendorCode, vendorExt, err := dlt698.ReadBoardInfo()
		if err != nil {
			log.Err(err).Msg("读取板子版本信息失败")
			return
		}
		// 读取硬件版本
		if args.ReadHardVersion {
			log.Info().Msgf("硬件版本：%s", hardVersion)
		}
		// 读取硬件日期
		if args.ReadHardDate {
			log.Info().Msgf("硬件日期：%s", hardDate)
		}
		// 读取厂商代码
		if args.ReadVendorCode {
			log.Info().Msgf("厂商代码：%s", vendorCode)
		}
		// 读取厂商扩展信息
		if args.ReadVendorExt {
			log.Info().Msgf("厂商扩展信息：%s", vendorExt)
		}
	}

	// 读取生产日期
	if args.ReadProduceDate {
		date, err := dlt698.ReadProduceDate()
		if err != nil {
			log.Err(err).Msg("读取生产日期失败")
			return
		}
		log.Info().Msgf("生产日期：%s", date)
	}

	// 读取安全模式
	if args.ReadSafeMode {
		mode, err := dlt698.ReadSafeMode()
		if err != nil {
			log.Err(err).Msg("读取安全模式失败")
			return
		}
		log.Info().Msgf("安全模式：%t", mode)
	}

	// 写入资产编码
	if strings.Trim(args.AssetCode, " ") != "" {
		err = dlt698.WriteAssetCode(args.AssetCode)
		if err != nil {
			log.Err(err).Msg("写入资产编码失败")
			return
		}
		log.Info().Msg("写入资产编码成功")
	}

	// 写入通讯地址
	if strings.Trim(args.Address, " ") != "" {
		err = dlt698.WriteAddress(args.Address)
		if err != nil {
			log.Err(err).Msg("写入通讯地址失败")
			return
		}
		log.Info().Msg("写入通讯地址成功")
	}

	// 写入电表号
	if strings.Trim(args.MeterID, " ") != "" {
		err = dlt698.WriteMeterId(args.MeterID)
		if err != nil {
			log.Err(err).Msg("写入电表号失败")
			return
		}
		log.Info().Msg("写入电表号成功")
	}

	// 写入板子版本信息
	if strings.Trim(args.HardVersion, " ") != "" && strings.Trim(args.HardDate, " ") != "" &&
		strings.Trim(args.VendorCode, " ") != "" && strings.Trim(args.VendorExt, " ") != "" {
		err = dlt698.WriteBoardInfo(args.HardVersion, args.HardDate, args.VendorCode, args.VendorExt)
		if err != nil {
			log.Err(err).Msg("写入板子版本信息失败")
			return
		}
		log.Info().Msg("写入板子版本信息成功")
	} else {
		// 写入硬件版本
		if strings.Trim(args.HardVersion, " ") != "" {
			err = dlt698.WriteHardVersion(args.HardVersion)
			if err != nil {
				log.Err(err).Msg("写入硬件版本失败")
				return
			}
			log.Info().Msg("写入硬件版本成功")
		}

		// 写入硬件日期
		if strings.Trim(args.HardDate, " ") != "" {
			err = dlt698.WriteHardDate(args.HardDate)
			if err != nil {
				log.Err(err).Msg("写入硬件日期失败")
				return
			}
			log.Info().Msg("写入硬件日期成功")
		}

		// 写入厂商代码
		if strings.Trim(args.VendorCode, " ") != "" {
			err = dlt698.WriteVendorCode(args.VendorCode)
			if err != nil {
				log.Err(err).Msg("写入厂商代码失败")
				return
			}
			log.Info().Msg("写入厂商代码成功")
		}

		// 写入厂商扩展信息
		if strings.Trim(args.VendorExt, " ") != "" {
			err = dlt698.WriteVendorExt(args.VendorExt)
			if err != nil {
				log.Err(err).Msg("写入厂商扩展信息失败")
				return
			}
			log.Info().Msg("写入厂商扩展信息成功")
		}
	}

	// 写入生产日期
	if strings.Trim(args.ProduceDate, " ") != "" {
		// 转换日期格式
		args.ProduceDate = strings.Replace(args.ProduceDate, "-", "", -1)
		args.ProduceDate = strings.Replace(args.ProduceDate, "/", "", -1)
		date, err := time.Parse("20060102", args.ProduceDate)
		if err != nil {
			log.Err(err).Msg("解析生产日期失败，请使用格式: YYYYMMDD")
			return
		}

		err = dlt698.WriteProduceDate(&date)
		if err != nil {
			log.Err(err).Msg("写入生产日期失败")
			return
		}
		log.Info().Msg("写入生产日期成功")
	}

	// 写入安全模式
	if strings.Trim(args.SafeMode, " ") != "" {
		// 校验安全模式参数
		if args.SafeMode != "0" && args.SafeMode != "1" {
			log.Err(err).Msg("安全模式参数错误，请使用0或1")
			return
		}

		err = dlt698.WriteSafeMode(args.SafeMode == "1")
		if err != nil {
			log.Err(err).Msg("写入安全模式失败")
			return
		}
		log.Info().Msg("写入安全模式成功")
	}

	// 列出所有指定目录中的文件名
	if strings.Trim(args.InverterInfo, " ") != "" {
		files := getFilesFromZip(args.InverterInfo)
		for _, file := range files {
			log.Debug().Str("文件名", file).Msg("文件名")
			// 执行发送文件操作
			err := dlt698.UpdateFile(file)
			if err != nil {
				log.Err(err).Msg("逆变器信息传输失败")
				return
			}
			log.Info().Str("文件名", file).Msg("传输成功")
		}
		log.Info().Msg("逆变器信息文件传输成功")
	}

	dlt698.Deinit()
}

// getFilesFromZip 从指定zip文件中的获得文件名列表
func getFilesFromZip(zipFile string) []string {
	// 打开zip文件
	zipReader, err := zip.OpenReader(zipFile)
	if err != nil {
		log.Err(err).Str("zip文件", zipFile).Msg("打开zip文件失败")
		return nil
	}
	defer zipReader.Close()

	// 遍历zip文件中的文件
	var list []string
	for _, file := range zipReader.File {
		if !file.FileInfo().IsDir() {
			list = append(list, zipFile+"!"+file.Name)
		}
	}
	return list
}
