package main

import (
	"net/http"
	"os"
	"pvFactoryBackend/base"
	"pvFactoryBackend/model"
	"pvFactoryBackend/server"
	"pvFactoryBackend/server/api"
	"pvFactoryBackend/server/dist"
	"runtime/debug"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func setupRouter(config *base.Config) *gin.Engine {
	// 初始化数据库
	err := model.DefaultDB.InitDefaultDB(config)
	if err != nil {
		log.Fatal().Err(err).Fields(config.DB).Msg("初始化数据库失败")
	}
	// 自动迁移数据库
	err = model.DefaultDB.Migrate()
	if err != nil {
		log.Fatal().Err(err).Msg("自动迁移数据库失败")
	}

	// 创建一个默认的路由
	r := gin.Default()
	// 允许跨域请求
	r.Use(cors.Default())

	// 添加 WebSocket 服务
	ws := server.NewEvent(config)
	// 初始化服务，创建测试通道连接
	err = ws.Init()
	if err != nil {
		log.Fatal().Err(err).Msg("初始化 WebSocket 服务失败")
	}
	// 启用数据处理协程
	go ws.Listen()

	// 添加v1路由组
	v1 := r.Group("/api")
	{
		v1Api := api.NewAPI(config)
		// 首页接口
		// 1. 获取后端信息
		v1.GET("/info", v1Api.GetInfo)

		// 电源板测试接口
		// 1. 查询测试结果
		v1.GET("/power", v1Api.GetPower)
		// 添加电源板测试结果
		v1.POST("/power", v1Api.PostPower)
		// 修改电源板测试结果
		v1.PUT("/power", v1Api.PutPower)

		// 整机组装接口
		// 1. 查询条码状态
		v1.GET("/barcode", v1Api.GetBarcode)
		// 2. 查询组装关系
		v1.GET("/assemble", v1Api.GetAssemble)
		// 3. 添加组装关系
		v1.POST("/assemble", v1Api.PostAssemble)
		// 4. 修改组装关系
		v1.PUT("/assemble", v1Api.PutAssemble)
		// 5. 删除组装关系
		v1.DELETE("/assemble", v1Api.DeleteAssemble)

		// 更换资产编码接口
		// 1. 查询资产编码信息
		v1.GET("/codeused", v1Api.GetCodeUsed)
		// 2. 执行更换操作
		v1.PUT("/codeused", func(ctx *gin.Context) {
			v1Api.PutCodeUsed(ctx, ws)
		})

		// 写号配置接口
		// 1. 查询写号配置
		v1.GET("/code", v1Api.GetCode)
		// 2. 添加写号配置
		v1.POST("/code", v1Api.PostCode)
		// 3. 修改写号配置
		v1.PUT("/code", v1Api.PutCode)

		// 数据管理
		// 1. 数据查询与导出
		v1.GET("/record", v1Api.GetRecord)

		// 设置电源板合格标准
		// 1. 读取整机光伏模块校验参数
		v1.GET("/config/whole", v1Api.GetConfigWhole)
		// 2. 设置整机光伏模块校验参数
		v1.PUT("/config/whole", v1Api.PutConfigWhole)
		// 3. 获取测试项列表
		v1.GET("/config/testitems", v1Api.GetTestItems)
	}

	// 添加 WebSocket 服务
	r.GET("/echo", ws.Echo)

	// 添加静态文件服务
	r.StaticFS("/work", http.FS(dist.Static))
	r.GET("/", func(ctx *gin.Context) {
		ctx.Request.URL.Path = "/work"
		r.HandleContext(ctx)
	})

	return r
}

func main() {
	defer func() {
		if r := recover(); r != nil {
			log.Fatal().Str("Stack", string(debug.Stack())).Msgf("main 程序运行异常: %v", r)
		}
	}()

	// 命令参数
	args := &base.Args{
		// 初始化版本号
		Version: base.VERSION,
	}
	// 识别命令行
	if !args.Init() {
		os.Exit(1)
	}

	// 读取配置文件
	config := base.NewConfig(args.Config)
	config.Load()

	// 复制日志参数
	logcfg := config.Log
	// 判断是否为调试模式
	if args.Debug {
		logcfg.StdLevel = 0
	}
	// 初始化日志
	base.InitLog(logcfg)
	log.Debug().Str("服务版本", args.Version).Any("配置", config).Msg("读取配置成功")

	// 启动服务
	r := setupRouter(config)
	r.Run(config.ListenAddress)
}
