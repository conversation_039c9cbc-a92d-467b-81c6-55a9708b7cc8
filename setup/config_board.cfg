# 系统识别接口顺序
# [   11.381439] usb_ch9344 1-1.2.2:1.0: ttyCH9344USB from 0 - 7: ch9344 device attached.
# [   11.500891] usb_ch9344 2-1.2.2:1.0: ttyCH9344USB from 8 - 15: ch9344 device attached.
# [   11.824033] usb_ch9344 1-1.2.3:1.0: ttyCH9344USB from 16 - 23: ch9344 device attached.
# [   11.965551] usb_ch9344 2-1.2.3:1.0: ttyCH9344USB from 24 - 31: ch9344 device attached.
# [   11.991020] usb_ch9344 1-1.2.4:1.0: ttyCH9344USB from 32 - 39: ch9344 device attached.
# [   12.065880] usb_ch9344 2-1.2.4:1.0: ttyCH9344USB from 40 - 47: ch9344 device attached.

ListenAddress = "0.0.0.0:8080"
DeviceName = "10005011"
InverterResource = "init.zip"

[Validator]
  SoftVersion = "V1.4"

[[Comm]]
  WorkstationID = 1
  PowerOnGPIO = 85
  BarcodeScannerPort = ""
  RS4851Port = "/dev/ttyCH9344USB1"
  RS4852Port = "/dev/ttyCH9344USB0"
  Ext11Port = "/dev/ttyCH9344USB18"
  Ext12Port = "/dev/ttyCH9344USB19"
  Ext21Port = "/dev/ttyCH9344USB16"
  Ext22Port = "/dev/ttyCH9344USB17"
  CarrierPort = "/dev/ttyCH9344USB22"
  BluetoothPort = ""

[[Comm]]
  WorkstationID = 2
  PowerOnGPIO = 86
  BarcodeScannerPort = ""
  RS4851Port = "/dev/ttyCH9344USB3"
  RS4852Port = "/dev/ttyCH9344USB2"
  Ext11Port = "/dev/ttyCH9344USB34"
  Ext12Port = "/dev/ttyCH9344USB35"
  Ext21Port = "/dev/ttyCH9344USB32"
  Ext22Port = "/dev/ttyCH9344USB33"
  CarrierPort = "/dev/ttyCH9344USB38"
  BluetoothPort = ""

[[Comm]]
  WorkstationID = 3
  PowerOnGPIO = 106
  BarcodeScannerPort = ""
  RS4851Port = "/dev/ttyCH9344USB9"
  RS4852Port = "/dev/ttyCH9344USB8"
  Ext11Port = "/dev/ttyCH9344USB26"
  Ext12Port = "/dev/ttyCH9344USB27"
  Ext21Port = "/dev/ttyCH9344USB24"
  Ext22Port = "/dev/ttyCH9344USB25"
  CarrierPort = "/dev/ttyCH9344USB30"
  BluetoothPort = ""

[[Comm]]
  WorkstationID = 4
  PowerOnGPIO = 105
  BarcodeScannerPort = ""
  RS4851Port = "/dev/ttyCH9344USB11"
  RS4852Port = "/dev/ttyCH9344USB10"
  Ext11Port = "/dev/ttyCH9344USB42"
  Ext12Port = "/dev/ttyCH9344USB43"
  Ext21Port = "/dev/ttyCH9344USB40"
  Ext22Port = "/dev/ttyCH9344USB41"
  CarrierPort = "/dev/ttyCH9344USB46"
  BluetoothPort = ""

[DB]
  Type = "postgres"
  DSN = "***********************************************/pvfactory"

[Log]
  StdLevel = 0
  StdColor = true
  FileLevel = 0
  FileDir = "log"
  FileSize = 100
  FileCount = 0
  FileDays = 30
  FileCompress = true
