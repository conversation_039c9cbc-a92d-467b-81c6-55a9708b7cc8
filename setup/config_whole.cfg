# 系统识别接口顺序
# [   11.811786] usb_ch9344 2-1.2.2:1.0: ttyCH9344USB from 0 - 7: ch9344 device attached.
# [   11.885537] usb_ch9344 2-1.2.3:1.0: ttyCH9344USB from 8 - 15: ch9344 device attached.
# [   12.240743] usb_ch9344 2-1.2.4:1.0: ttyCH9344USB from 16 - 23: ch9344 device attached.

# 服务监听地址
ListenAddress = "0.0.0.0:8080"
# 工装名称
DeviceName = "10005021"
# 写号逆变器配置文件路径
InverterResource = "init.zip"

# 测试校验参数
[validator]
softversion = "V1.4"

# 工装通讯连接参数
[[comm]]
# 工位编号
WorkstationID = 1
# 板级测试上电触发的GPIO号
PowerOnGPIO = 106
# 条码扫描头对应的串口号
BarcodeScannerPort = ""
# II型光伏 RS485-1 对应的串口号
RS4851Port = "/dev/ttyCH9344USB1"
# II型光伏 RS485-2 对应的串口号
RS4852Port = "/dev/ttyCH9344USB0"
# II型光伏 扩展1-1 对应的串口号
Ext11Port = "/dev/ttyCH9344USB10"
# II型光伏 扩展1-2 对应的串口号
Ext12Port = "/dev/ttyCH9344USB11"
# II型光伏 扩展2-1 对应的串口号
Ext21Port = "/dev/ttyCH9344USB8"
# II型光伏 扩展2-2 对应的串口号
Ext22Port = "/dev/ttyCH9344USB9"
# II型光伏 载波 对应的串口号
CarrierPort = ""
# II型光伏 蓝牙 对应的串口号
BluetoothPort = ""

[[comm]]
WorkstationID = 2
PowerOnGPIO = 105
BarcodeScannerPort = ""
RS4851Port = "/dev/ttyCH9344USB3"
RS4852Port = "/dev/ttyCH9344USB2"
Ext11Port = "/dev/ttyCH9344USB18"
Ext12Port = "/dev/ttyCH9344USB19"
Ext21Port = "/dev/ttyCH9344USB16"
Ext22Port = "/dev/ttyCH9344USB17"
CarrierPort = ""
BluetoothPort = ""

# 数据库参数
[db]
# 数据库类型，目前支持 postgres, sqlite3
#Type = "postgres"
Type = "sqlite3"
# 数据库连接串
# sqlite3 连接串参考: https://www.sqlite.org/uri.html
# postgres 连接串参考: https://www.postgresql.org/docs/current/libpq-connect.html#LIBPQ-CONNSTRING
#DSN = "************************************************/pvfactory"
DSN = "demo.sqlite3"

# 日志参数
[log]
# 终端日志级别，-1：TraceLevel，0: DebugLevel，1: InfoLevel，2: WarnLevel，3: ErrorLevel，4: FatalLevel，5: PanicLevel，6: NoLevel，7: Disabled
stdlevel = 1
# 终端日志是否用彩色标示不同内容，在 windows 系统下可能会显示异常，建议关闭
stdcolor = true
# 文件日志级别，-1：TraceLevel，0: DebugLevel，1: InfoLevel，2: WarnLevel，3: ErrorLevel，4: FatalLevel，5: PanicLevel，6: NoLevel，7: Disabled
filelevel = 0
# 日志文件目录，不想保存到文件，则留空
filedir = "log"
# 用于日志文件分割，每个文件的最大大小，单位MB
filesize = 100
# 日志文件保留个数，0表示无限个数
filecount = 0
# 日志文件保留天数，0表示永久保留
filedays = 30
# 是否压缩日志文件，压缩后文件名会带上.gz后缀
filecompress = true
