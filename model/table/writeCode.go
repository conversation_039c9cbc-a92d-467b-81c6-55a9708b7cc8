package table

import (
	"time"

	"gorm.io/gorm"
)

// WriteCode 写号信息表
type WriteCode struct {
	gorm.Model
	// 资产编码
	Barcode string `gorm:"type:varchar(22);uniqueIndex"`
	// 通信地址
	Address string `gorm:"type:varchar(16);index"`
	// 表号
	MeterNumber string `gorm:"type:varchar(16);index"`
	// 硬件版本号
	HardVersion string `gorm:"type:varchar(4);index"`
	// 硬件版本日期
	HardDate string `gorm:"type:varchar(6);index"`
	// 厂商代码
	VendorCode string `gorm:"type:varchar(4);index"`
	// 厂商扩展信息
	VendorExt string `gorm:"type:varchar(8);index"`
	// 生产日期
	ProduceDate *time.Time `gorm:"type:date;index"`
	// 上电启用安全模式
	SafeMode bool `gorm:"default:false"`
}
