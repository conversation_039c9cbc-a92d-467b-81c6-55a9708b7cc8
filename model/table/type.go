package table

const (
	// 电源板测试项
	// 电源测试
	BoardPower = "boardPower"

	// 主板测试项
	// TTL 接口检测
	BoardTTL = "boardTTL"
	// 指示灯
	BoardLED = "boardLed"
	// 版本号校验
	BoardVersion = "boardVersion"
	// Flash存储
	BoardFlash = "boardFlash"
	// ESAM
	BoardESAM = "boardEsam"
	// RS485
	BoardRs485 = "boardRs485"

	// 整机测试项
	// RS485-1
	WholeRs4851 = "wholeRs4851"
	// 版本号校验
	WholeVersion = "wholeVersion"
	// 指示灯
	WholeLED = "wholeLed"
	// 设置时间
	WholeSetTime = "wholeSetTime"
	// 蓝牙
	WholeBT = "wholeBT"
	// ESAM
	WholeESAM = "wholeEsam"
	// RS485-2
	WholeRs4852 = "wholeRs4852"
	// 扩展1-1
	WholeExt11 = "wholeExt11"
	// 扩展1-2
	WholeExt12 = "wholeExt12"
	// 扩展2-1
	WholeExt21 = "wholeExt21"
	// 扩展2-2
	WholeExt22 = "wholeExt22"
	// 载波
	WholeCarrier = "wholeCarrier"
	// 写号
	WholeSetCode = "wholeSetCode"
	// 检查写号
	WholeCheckCode = "wholeCheckCode"
	// 校准
	WholeCalibration = "wholeCalibration"

	// 整机复测项
	// RS485-1
	RewholeRs4851 = "rewholeRs4851"
	// 版本号校验
	RewholeVersion = "rewholeVersion"
	// 指示灯
	RewholeLED = "rewholeLed"
	// 蓝牙
	RewholeBT = "rewholeBT"
	// ESAM
	RewholeESAM = "rewholeEsam"
	// RS485-2
	RewholeRs4852 = "rewholeRs4852"
	// 扩展1-1
	RewholeExt11 = "rewholeExt11"
	// 扩展1-2
	RewholeExt12 = "rewholeExt12"
	// 扩展2-1
	RewholeExt21 = "rewholeExt21"
	// 扩展2-2
	RewholeExt22 = "rewholeExt22"
	// 载波
	RewholeCarrier = "rewholeCarrier"
	// 检查写号
	RewholeCheckCode = "rewholeCheckCode"
	// 检查时间
	RewholeCheckTime = "rewholeCheckTime"

	// 非正常测试项
	// 文件系统格式化
	NonNormalFormatFS = "nonNormalFormatFS"
)

// 测试类型，字符串表示
const (
	// 主板测试
	TypeMainStr = "board"
	// 整机测试
	TypeWholeStr = "whole"
	// 整机复测
	TypeRewholeStr = "rewhole"
)

// 测试类型
const (
	// 主板测试
	TypeMain byte = iota + 1
	// 整机测试
	TypeWhole
	// 整机复测
	TypeRewhole
)

// 测试结果
const (
	// 未测试
	ResultNone byte = iota
	// 全部测完且全部合格
	ResultPass
	// 全部测完存在不合格
	ResultFail
	// 有未测试项
	ResultUnfinish
)
