package table

import "gorm.io/gorm"

// InfoPlan 测试记录表
type InfoPlan struct {
	gorm.Model
	// 测试类型，1:电源板，2:主板，3:整机，4:整机复测
	Type byte `gorm:"type:smallint;default:0"`
	// 条码，因整机条码与整机复测条码相同 ，为了区分，整机复测条码前面加上“R”
	Barcode string `gorm:"type:varchar(23);uniqueIndex"`
	// 测试状态，0:未测试，1:全部测完且全部合格，2:全部测完存在不合格，3:有未测试项
	Status byte `gorm:"type:smallint;default:0"`
	// 测试项 hasMany: InfoTestItem
	InfoTestItem []InfoTestItem `gorm:"foreignKey:RecordID"`
}
