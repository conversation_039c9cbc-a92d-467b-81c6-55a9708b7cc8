package table

import "gorm.io/gorm"

// InfoAssemble 组装关系表
type InfoAssemble struct {
	gorm.Model

	// 电源板条码
	PowerBarcode string `gorm:"type:varchar(22);not null;index"`
	// 主板条码
	BoardBarcode string `gorm:"type:varchar(22);not null;index"`
	// 整机条码，即资产编码
	WholeBarcode string `gorm:"type:varchar(22);not null;index"`

	// 电源板 belongsTo: InfoPlan
	PowerPlan InfoPlan `gorm:"foreignKey:PowerBarcode;references:Barcode"`
	// 主板 belongsTo: InfoPlan
	BoardPlan InfoPlan `gorm:"foreignKey:BoardBarcode;references:Barcode"`
	// 整机 belongsTo: InfoPlan
	WholePlan InfoPlan `gorm:"foreignKey:WholeBarcode;references:Barcode"`
}
