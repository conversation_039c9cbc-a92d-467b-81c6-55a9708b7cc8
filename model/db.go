package model

import (
	"errors"
	"fmt"
	"math/big"
	"pvFactoryBackend/base"
	"pvFactoryBackend/model/table"
	"strings"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

var DefaultDB = &DB{}

// DB 数据访问对象
type DB struct {
	*gorm.DB
	// 配置文件
	config *base.Config
}

// InitDefaultDB 初始化默认数据库
func (d *DB) InitDefaultDB(config *base.Config) error {
	var dialector gorm.Dialector

	d.config = config

	if config.DB.Type == "sqlite3" {
		dialector = sqlite.Open(config.DB.DSN)
	} else if config.DB.Type == "postgres" {
		dialector = postgres.Open(config.DB.DSN)
	} else {
		return errors.New("不支持的数据库类型")
	}

	db, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		return err
	}
	d.DB = db
	return nil
}

// Migrate 迁移数据库
func (d *DB) Migrate() error {
	return d.Debug().AutoMigrate(
		&table.InfoPlan{},
		&table.InfoAssemble{},
		&table.InfoTestItem{},
		&table.WriteCode{},
	)
}

// 为21位国网资产条码添加校验码
func (d *DB) addCs(value string) string {
	if len(value) > 21 {
		value = value[:21]
	} else if len(value) < 21 {
		value = strings.Repeat("0", 21-len(value)) + value
	}

	sum := 0
	for i, b := range value {
		if b < '0' || b > '9' {
			b = '0'
		}
		if i%2 == 0 {
			sum += int(b-'0') * 3
		} else {
			sum += int(b - '0')
		}
	}
	return value + fmt.Sprintf("%d", (10-sum%10)%10)
}

// SelectBarcodePlan 查询条码是否已检测
// plan 板类型
// barcode 条码
// 返回 条码状态[0:未知，1:未检测，2:检测不合格，3:检测合格]，错误信息
func (d *DB) SelectBarcodePlan(plan string, barcode string) (int, error) {
	var infoPower table.InfoPlan
	// 整机复测条码，前面加 R 字符
	if plan == table.TypeRewholeStr {
		barcode = "R" + barcode
	}

	typePlan := table.TypeMain
	if plan == table.TypeMainStr {
		typePlan = table.TypeMain
	} else if plan == table.TypeWholeStr {
		typePlan = table.TypeWhole
	} else if plan == table.TypeRewholeStr {
		typePlan = table.TypeRewhole
	} else {
		return 0, errors.New("不支持的测试类型")
	}

	err := d.Debug().Model(&table.InfoPlan{}).Where("type =?", typePlan).Where("barcode =?", barcode).First(&infoPower).Error
	if err != nil {
		return 1, nil
	}

	// 判断检测是否异常
	if infoPower.Status == table.ResultFail {
		return 2, nil
	}
	return 3, nil
}

// SelectBarcodeAssembled 查询条码是否已组装
// plan 板类型
// barcode 条码
// 返回 条码状态[0:未知，1:未组装，2:已组装]，错误信息
func (d *DB) SelectBarcodeAssembled(plan string, barcode string) (int, error) {
	// 查询组装记录
	db := d.Debug().Model(&table.InfoAssemble{})
	if plan == table.TypeMainStr {
		db = db.Where("board_barcode =?", barcode)
	} else if plan == table.TypeWholeStr || plan == table.TypeRewholeStr {
		db = db.Where("whole_barcode =?", barcode)
	} else {
		return 0, errors.New("不支持的条码类型")
	}

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return 0, err
	}
	if count > 0 {
		return 2, nil
	}

	return 1, nil
}

// SelectAssemble 查询组装记录
// barcode 组装条码
// 返回 组装记录，错误信息
func (d *DB) SelectAssemble(powerBarcode, boardBarcode, wholeBarcode string, page, count int) ([]table.InfoAssemble, int64, error) {
	var infoAssemble []table.InfoAssemble
	var err error
	var total int64

	// 限制查询结果数
	db := d.Debug().Model(&table.InfoAssemble{}).Order("id desc")

	if powerBarcode != "" {
		db.Where("power_barcode =?", powerBarcode)
	}

	if boardBarcode != "" {
		db.Where("board_barcode =?", boardBarcode)
	}

	if wholeBarcode != "" {
		db.Where("whole_barcode =?", wholeBarcode)
	}
	db.Count(&total)

	err = db.Limit(count).Offset((page - 1) * count).Find(&infoAssemble).Error
	if err != nil {
		return nil, 0, err
	}
	return infoAssemble, total, nil
}

// AddAssemble 添加组装记录
// boardBarcode 主板条码
// wholeBarcode 整机条码
func (d *DB) AddAssemble(powerBarcode, boardBarcode, wholeBarcode string) error {
	// 组织写入的组装记录
	infoAssemble := table.InfoAssemble{
		PowerBarcode: powerBarcode,
		BoardBarcode: boardBarcode,
		WholeBarcode: wholeBarcode,
	}

	// 判断条码是否存在
	var infoMain table.InfoPlan
	err := d.Debug().Model(&table.InfoPlan{}).Where("type =?", table.TypeMain).Where("barcode =?", boardBarcode).First(&infoMain).Error
	if err != nil {
		return errors.New("主板条码不存在")
	}

	// 判断整机条码是否已组装
	var count int64
	err = d.Debug().Model(&table.InfoAssemble{}).Where("whole_barcode =?", wholeBarcode).Count(&count).Error
	if err != nil {
		return errors.New("查询组装记录失败")
	}
	if count > 0 {
		return errors.New("该整机已组装")
	}

	// 判断是否已组装
	err = d.Debug().Model(&table.InfoAssemble{}).Where("power_barcode =?", powerBarcode).Or("board_barcode =?", boardBarcode).Where("whole_barcode !=?", wholeBarcode).Count(&count).Error
	if err != nil {
		return errors.New("查询组装记录失败")
	}
	if count > 0 {
		return errors.New("该电源板或主板已组装到其它整机")
	}

	return d.Transaction(func(tx *gorm.DB) error {
		// 查询整机条码是否已存在，若不存在，则创建新的整机条码
		var wholeInfo table.InfoPlan
		err = d.Debug().Model(&table.InfoPlan{}).Where("type =?", table.TypeWhole).Where("barcode =?", wholeBarcode).First(&wholeInfo).Error
		if err != nil {
			wholeInfo = table.InfoPlan{
				Barcode: wholeBarcode,
				Type:    table.TypeWhole,
			}
			err = d.Create(&wholeInfo).Error
			if err != nil {
				return errors.New("创建整机条码失败")
			}
		}

		// 写入数据库
		return d.Create(&infoAssemble).Error
	})
}

// UpdateAssemble 更新组装记录
// boardBarcode 主板条码
// wholeBarcode 整机条码
func (d *DB) UpdateAssemble(powerBarcode, boardBarcode, wholeBarcode string) error {
	// 组织写入的组装记录
	infoAssemble := table.InfoAssemble{}

	// 判断条码是否存在
	var infoMain table.InfoPlan
	err := d.Debug().Model(&table.InfoPlan{}).Where("type =?", table.TypeMain).Where("barcode =?", boardBarcode).First(&infoMain).Error
	if err != nil {
		return errors.New("主板条码不存在")
	}

	// 查询电源板及主板条码是否已组装
	err = d.Debug().Where("power_barcode =?", powerBarcode).Or("board_barcode =?", boardBarcode).Where("whole_barcode !=?", wholeBarcode).Find(&infoAssemble).Error
	if err != nil {
		return errors.New("电源板或主板条码已组装到其它整机")
	}

	// 写入数据库
	db := d.Model(&table.InfoAssemble{}).Where("whole_barcode =?", wholeBarcode).Updates(map[string]interface{}{
		"power_barcode": powerBarcode,
		"board_barcode": boardBarcode,
	})
	if db.Error != nil {
		return db.Error
	}

	if db.RowsAffected == 0 {
		return errors.New("资产条码不存在")
	}

	return nil
}

// 更新组装关系中的整机条码
// oldWholeBarcode 旧的整机条码
// newWholeBarcode 新的整机条码
func (d *DB) UpdateWholeBarcode(oldWholeBarcode, newWholeBarcode string) error {
	// 组织写入的组装记录
	infoAssemble := table.InfoAssemble{}

	// 查询旧的整机条码是否存在
	err := d.Debug().Where("whole_barcode =?", oldWholeBarcode).Find(&infoAssemble).Error
	if err != nil {
		return errors.New("该资产编码未组装")
	}

	// 新资产编码没有记录记录时，则创建
	var count int64
	err = d.Debug().Model(&table.InfoPlan{}).Where("type =?", table.TypeWhole).Where("barcode =?", newWholeBarcode).Count(&count).Error
	if err != nil {
		return errors.New("查询资产编码测试记录失败")
	}
	if count < 1 {
		wholeInfo := table.InfoPlan{
			Type:    table.TypeWhole,
			Barcode: newWholeBarcode,
			Status:  table.ResultNone,
		}
		err = d.Create(&wholeInfo).Error
		if err != nil {
			return errors.New("创建资产编码测试记录失败")
		}
	}

	// 写入数据库
	return d.Model(&table.InfoAssemble{}).Where("whole_barcode =?", oldWholeBarcode).Updates(map[string]interface{}{
		"whole_barcode": newWholeBarcode,
	}).Error
}

// DeleteAssemble 删除组装记录
// barcode 整机条码
func (d *DB) DeleteAssemble(barcode string) error {
	db := d.Debug().Where("whole_barcode =?", barcode).Delete(&table.InfoAssemble{})
	if db.Error != nil {
		return db.Error
	}

	if db.RowsAffected == 0 {
		return errors.New("资产条码不存在")
	}

	return nil
}

// SelectCode 查询写号信息
// barcode 条码
// address 地址
// meter 表号
// hardVersion 硬件版本
// hardDate 硬件日期
// produceDate 出厂日期
// vendorCode 厂商代码
// vendorExt 厂商扩展信息
// page 页码，查询指定页码的记录，从 1 开始
// count 每页记录数
// 返回 写号信息，错误信息
func (d *DB) SelectCode(barcode, address, meter, hardVersion, hardDate, produceDate, vendorCode, vendorExt string, page, count int) ([]table.WriteCode, int64, error) {
	var writeCode []table.WriteCode
	var err error
	var total int64

	// 限制查询结果数
	db := d.Debug().Model(&table.WriteCode{}).Order("id desc")

	if barcode != "" {
		db.Where("barcode =?", barcode)
	}

	if address != "" {
		db.Where("address =?", address)
	}

	if meter != "" {
		db.Where("meter_number =?", meter)
	}

	if hardVersion != "" {
		db.Where("hard_version =?", hardVersion)
	}

	if hardDate != "" {
		db.Where("hard_date =?", hardDate)
	}

	if produceDate != "" {
		db.Where("produce_date =?", produceDate)
	}

	if vendorCode != "" {
		db.Where("vendor_code =?", vendorCode)
	}

	if vendorExt != "" {
		db.Where("vendor_ext =?", vendorExt)
	}
	db.Count(&total)

	err = db.Limit(count).Offset((page - 1) * count).Find(&writeCode).Error
	if err != nil {
		return nil, 0, err
	}
	return writeCode, total, nil
}

// AddCode 添加写号信息
// barcode 条码
// address 地址
// meter 表号
// hardVersion 硬件版本
// hardDate 硬件日期
// produceDate 出厂日期
// vendorCode 厂商代码
// vendorExt 厂商扩展信息
// count 写号数量
func (d *DB) AddCode(barcode, address, meter, hardVersion, hardDate, vendorCode, vendorExt string, mode bool, produceDate *time.Time, count int) error {
	// 组织写入的写号信息
	writeCodes := []table.WriteCode{}

	// 记录各编码长度
	barcodeLen := len(barcode)
	addressLen := len(address)
	meterLen := len(meter)

	// 将条码转换成大数值
	if len(barcode) > 21 {
		barcode = barcode[:21]
	}
	bitBarcode, ret := new(big.Int).SetString(barcode, 10)
	if !ret {
		return errors.New("条码格式错误")
	}
	bitAddress, ret := new(big.Int).SetString(address, 10)
	if !ret {
		return errors.New("通讯地址格式错误")
	}
	bitMeter, ret := new(big.Int).SetString(meter, 10)
	if !ret {
		return errors.New("表号格式错误")
	}

	for i := 0; i < count; i++ {
		writeCodes = append(writeCodes, table.WriteCode{
			Barcode:     fmt.Sprintf("%0*s", barcodeLen, d.addCs(bitBarcode.Text(10))),
			Address:     fmt.Sprintf("%0*s", addressLen, bitAddress.Text(10)),
			MeterNumber: fmt.Sprintf("%0*s", meterLen, bitMeter.Text(10)),
			HardVersion: hardVersion,
			HardDate:    hardDate,
			ProduceDate: produceDate,
			VendorCode:  vendorCode,
			VendorExt:   vendorExt,
			SafeMode:    mode,
		})
		bitBarcode.Add(bitBarcode, big.NewInt(1))
		bitAddress.Add(bitAddress, big.NewInt(1))
		bitMeter.Add(bitMeter, big.NewInt(1))
	}
	// 写入数据库
	return d.Create(&writeCodes).Error
}

// UpdateCode 更新写号信息
// barcode 条码
// address 地址
// meter 表号
// hardVersion 硬件版本
// hardDate 硬件日期
// produceDate 出厂日期
// vendorCode 厂商代码
// vendorExt 厂商扩展信息
func (d *DB) UpdateCode(barcode, address, meter, hardVersion, hardDate, vendorCode, vendorExt string, mode bool, produceDate *time.Time) error {
	// 组织写入的写号信息
	writeCode := table.WriteCode{
		Address:     address,
		MeterNumber: meter,
		HardVersion: hardVersion,
		HardDate:    hardDate,
		ProduceDate: produceDate,
		VendorCode:  vendorCode,
		VendorExt:   vendorExt,
		SafeMode:    mode,
	}

	// 写入数据库
	db := d.Debug().Model(&table.WriteCode{}).Where("barcode =?", barcode).Updates(writeCode)
	if db.Error != nil {
		return db.Error
	}

	if db.RowsAffected == 0 {
		return errors.New("资产条码不存在")
	}

	return nil
}

// SelectRecord 数据查询
// typeCode 条码类型
// barcode 条码
// status 测试结果
// startTime 记录开始时间
// endTime 记录结束时间
// npage 页码，查询指定页码的记录，从 1 开始
// ncount 每页记录数
// 返回 数据记录，总记录数，错误信息
func (d *DB) SelectRecord(typeCode byte, barcode string, status byte, startTime, endTime *time.Time, npage, ncount int) ([]table.InfoAssemble, int64, error) {
	var records []table.InfoAssemble
	var err error
	var total int64

	// 限制查询结果数
	db := d.Debug().Model(&table.InfoAssemble{}).Order("info_assembles.id desc")

	// 时间范围
	if startTime != nil {
		db.Where("info_assembles.updated_at >= ?", startTime)
	}
	if endTime != nil {
		db.Where("info_assembles.updated_at <= ?", endTime)
	}

	// 指定条码
	if barcode != "" {
		if typeCode == table.TypeMain {
			db.Where("board_barcode =?", barcode)
		} else if typeCode == table.TypeWhole {
			db.Where("whole_barcode =?", barcode)
		} else {
			return nil, 0, errors.New("不支持的条码类型")
		}
	}
	db.Count(&total)

	// 指定测试结果
	if status != table.ResultNone {
		db.Joins("PowerPlan", "status =?", status).Joins("BoardPlan", "status =?", status).Joins("WholePlan", "status =?", status)
	} else {
		db.Joins("PowerPlan").Joins("BoardPlan").Joins("WholePlan")
	}
	db.Preload("PowerPlan.InfoTestItem").Preload("BoardPlan.InfoTestItem").Preload("WholePlan.InfoTestItem")

	// 查询
	err = db.Limit(ncount).Offset((npage - 1) * ncount).Find(&records).Error
	if err != nil {
		return nil, 0, err
	}
	return records, total, nil
}

// 重置测试项方案，当指定测试方案不存在时，创建新的测试方案，否则删除原测试记录
// plan 测试类型
// barcode 条码
// 返回 测试项方案记录ID，错误信息
func (d *DB) ResetTestPlan(plan string, barcode string) (uint, error) {
	// 整机复测条码，前面加 R 字符
	if plan == table.TypeRewholeStr {
		barcode = "R" + barcode
	}

	// 组织写入的测试方案
	infoPlan := table.InfoPlan{
		Barcode: barcode,
	}

	if plan == table.TypeMainStr {
		infoPlan.Type = table.TypeMain
	} else if plan == table.TypeWholeStr {
		infoPlan.Type = table.TypeWhole
	} else if plan == table.TypeRewholeStr {
		infoPlan.Type = table.TypeRewhole
	} else {
		return 0, errors.New("不支持的测试类型")
	}

	// 判断条码是否存在
	var rowPlan table.InfoPlan
	var total int64
	db := d.Debug().Model(&table.InfoPlan{}).Where("type =?", infoPlan.Type).Where("barcode =?", infoPlan.Barcode).Count(&total)
	if db.Error != nil {
		return 0, db.Error
	}
	if total > 0 {
		db = db.First(&rowPlan)
		if db.Error != nil {
			return 0, db.Error
		}
		// 删除原测试记录
		db = d.Debug().Where("record_id =?", rowPlan.ID).Delete(&table.InfoTestItem{})
		if db.Error != nil {
			return 0, db.Error
		}
		return rowPlan.ID, nil
	}

	// 条码不存在时，写入数据库
	db = d.Create(&infoPlan)
	if db.Error != nil {
		return 0, db.Error
	}

	return infoPlan.ID, nil
}

// 更新板级测试结果
// plan 测试类型
// barcode 电源板条码
// result 测试结果
func (d *DB) UpdateTestPlan(plan string, barcode string, result bool) error {
	status := table.ResultPass
	if !result {
		status = table.ResultFail
	}

	typePlan := table.TypeMain
	if plan == table.TypeMainStr {
		typePlan = table.TypeMain
	} else if plan == table.TypeWholeStr {
		typePlan = table.TypeWhole
	} else if plan == table.TypeRewholeStr {
		typePlan = table.TypeRewhole
	} else {
		return errors.New("不支持的测试类型")
	}

	// 整机复测条码，前面加 R 字符
	if plan == table.TypeRewholeStr {
		barcode = "R" + barcode
	}

	db := d.Model(&table.InfoPlan{}).Where("type =?", typePlan).Where("barcode =?", barcode).Update("status", status)
	if db.Error != nil {
		return db.Error
	}

	if db.RowsAffected == 0 {
		return fmt.Errorf("条码 %s 未测试，先添加测试记录", barcode)
	}

	return nil
}

// 添加测试结果
// planID 测试方案ID
// item 测试项
// value 测试值
// result 测试结果
// 返回 错误信息
func (d *DB) AddTestResult(planID uint, item string, value string, result bool) error {
	// 查询指定测试方案的测试项是否已存在
	var testItem table.InfoTestItem
	var total int64
	db := d.Debug().Model(&table.InfoTestItem{}).Where("record_id =?", planID).Where("name =?", item).Count(&total)
	if db.Error != nil {
		return db.Error
	}

	// 启用事务处理
	return d.Transaction(func(tx *gorm.DB) error {
		// 测试项已存在，则更新测试结果
		if total > 0 {
			db = db.First(&testItem)
			if db.Error != nil {
				return db.Error
			}

			db = tx.Debug().Model(&table.InfoTestItem{}).Where("id =?", testItem.ID).Updates(map[string]interface{}{
				"value":  value,
				"result": result,
			})
			if db.Error != nil {
				return db.Error
			}
			return nil
		}

		// 组织写入的测试结果
		testResult := table.InfoTestItem{
			RecordID: planID,
			Name:     item,
			Value:    value,
			Result:   result,
		}

		// 写入测试结果
		return tx.Create(&testResult).Error
	})
}

// 根据整机条码查询主板指定测试项的测试结果
// barcode 整机条码
// item 主板测试项
// 返回 测试结果，错误信息
func (d *DB) SelectMainTestResult(barcode string, item string) (string, error) {
	var testResult table.InfoAssemble
	db := d.Debug().Model(&table.InfoAssemble{}).Where("whole_barcode =?", barcode).Preload("BoardPlan.InfoTestItem", "name =? AND result =?", item, true).First(&testResult)
	if db.Error != nil {
		return "", db.Error
	}

	for _, v := range testResult.BoardPlan.InfoTestItem {
		if v.Name == item {
			return v.Value, nil
		}
	}

	return "", errors.New("测试项不存在")
}
