*.sqlite3
*.exe
pvFactoryBackend
config.cfg
*_test.go

# Binaries for programs and plugins*.exe
*.exe~*.dll
*.so*.dylib

# Test binary, built with `go test -c`*.test

# Output of the go coverage tool, specifically when used with LiteID*.out
# Dependency directories (remove the comment below to include it)# vendor/

# Go workspace filego.work
# IDE-specific files
.idea/
.vscode/
# Log files*.log
# OS-specific files
.DS_Store
Thumbs.db


