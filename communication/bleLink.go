package communication

import (
	"encoding/hex"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	"tinygo.org/x/bluetooth"
)

var (
	serviceUUID = bluetooth.New16BitUUID(0xFFF0)
	rxUUID      = bluetooth.New16BitUUID(0xFFF1)
	txUUID      = bluetooth.New16BitUUID(0xFFF2)
)

type BleLink struct {
	// 蓝牙字节间读取超时时间
	ReadTimeout time.Duration
	// 接收到信息后的回调，由回调来判断哪些数据已被处理
	// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
	// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
	// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
	onGetFrame func(data []byte, newoff int) int

	// 目标设备蓝牙地址
	address string
	// 建立连接的目标蓝牙设备
	device *bluetooth.Device
	// 接收通道
	rxChan *bluetooth.DeviceCharacteristic
	// 发送通道
	txChan *bluetooth.DeviceCharacteristic
	// 数据读取缓冲区
	bufRead []byte
	// 已读取到缓冲区的数据长度
	lenRead int
	// 数据缓冲区锁
	bufLock sync.Mutex
}

// NewBleLink 创建蓝牙设备对象
// address: 目标蓝牙设备地址
func NewBleLink(address string) *BleLink {
	return &BleLink{
		address:     address,
		ReadTimeout: 100 * time.Millisecond,
		onGetFrame:  nil,
		bufRead:     make([]byte, 40960),
	}
}

// Init 初始化蓝牙设备，目的是开启蓝牙功能，若已开启则可以不执行此操作
func (b *BleLink) Init() error {
	// 初始化蓝牙设备
	if _, err := bluetooth.DefaultAdapter.Address(); err != nil {
		log.Debug().Msg("初始化蓝牙设备")
		err = bluetooth.DefaultAdapter.Enable()
		if err != nil {
			return errors.Join(errors.New("蓝牙初始化失败"), err)
		}
	}

	return nil
}

// Open 开启蓝牙设备连接到目标地址，并检索服务和特征值，订阅接收通道
func (b *BleLink) Open() error {
	// 若已连接，则断开连接
	b.Close()

	// 记录扫描到的设备
	var devAddr *bluetooth.Address
	// 超时时间后停止扫描
	go func() {
		time.Sleep(20 * time.Second)
		bluetooth.DefaultAdapter.StopScan()
	}()

	// 扫描蓝牙设备
	err := bluetooth.DefaultAdapter.Scan(func(adapter *bluetooth.Adapter, device bluetooth.ScanResult) {
		log.Debug().Str("name", device.LocalName()).Str("address", device.Address.String()).Msg("发现蓝牙设备")
		if strings.ReplaceAll(device.Address.String(), ":", "")[2:] == strings.ToUpper(b.address[2:]) {
			log.Debug().Str("name", device.LocalName()).Msg("匹配到目标蓝牙设备")
			devAddr = &device.Address
			// 停止扫描
			adapter.StopScan()
		}
	})
	if err != nil {
		return errors.Join(errors.New("蓝牙扫描失败"), err)
	}
	if devAddr == nil {
		return errors.New("未找到目标蓝牙设备")
	}

	// 连接蓝牙设备
	log.Debug().Str("address", devAddr.String()).Msg("连接蓝牙设备")
	d, err := bluetooth.DefaultAdapter.Connect(*devAddr, bluetooth.ConnectionParams{})
	if err != nil {
		return errors.Join(errors.New("蓝牙连接失败"), err)
	}
	b.device = &d

	// 发现服务
	serv, err := b.device.DiscoverServices([]bluetooth.UUID{serviceUUID})
	if err != nil {
		b.Close()
		return errors.Join(errors.New("蓝牙服务发现失败"), err)
	}
	// 发现特征值
	chars, err := serv[0].DiscoverCharacteristics([]bluetooth.UUID{rxUUID, txUUID})
	if err != nil {
		b.Close()
		return errors.Join(errors.New("蓝牙特征值发现失败"), err)
	}
	b.rxChan = &chars[0]
	b.txChan = &chars[1]

	// 订阅特征值
	err = chars[0].EnableNotifications(b.addData)
	if err != nil {
		b.Close()
		return errors.Join(errors.New("蓝牙接收订阅失败"), err)
	}

	return nil
}

// Close 断开蓝牙连接
func (b *BleLink) Close() error {
	if b.device != nil {
		err := b.device.Disconnect()
		b.device = nil
		return err
	}
	return nil
}

// IsOpen 判断蓝牙连接是否已开启
func (b *BleLink) IsOpen() bool {
	return b.device != nil
}

// Write 发送数据
func (b *BleLink) Write(data []byte) (int, error) {
	if b.device == nil {
		return 0, errors.New("未开启蓝牙通讯")
	}
	if b.txChan == nil {
		return 0, errors.New("蓝牙发送通道未发现")
	}

	// 发送数据
	log.Debug().Str("MAC", b.address).Str("UUID", b.txChan.UUID().String()).Str("data", hex.EncodeToString(data)).Msg("蓝牙发送数据")
	return b.txChan.WriteWithoutResponse(data)
}

// ReadNoWait 不阻塞接收数据
func (b *BleLink) ReadNoWait(data []byte) (int, error) {
	if b.device == nil {
		return 0, errors.New("未开启蓝牙通讯")
	}
	// 操作数据前上锁
	b.bufLock.Lock()
	defer b.bufLock.Unlock()
	// 读取数据
	n := copy(data, b.bufRead[0:b.lenRead])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("MAC", b.address).Str("UUID", b.rxChan.UUID().String()).Str("data", hex.EncodeToString(data[0:n])).Msg("蓝牙接收到数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(b.bufRead, b.bufRead[n:b.lenRead])
	b.lenRead -= n
	return n, nil
}

// Read 阻塞接收数据
func (b *BleLink) Read(data []byte, totalTimeout time.Duration, byteTimeout time.Duration) (int, error) {
	if b.device == nil {
		return 0, errors.New("未开启蓝牙通讯")
	}

	// 记录起始时间
	startTime := time.Now()
	// 记录当前数据长度
	n := b.lenRead
	time.Sleep(byteTimeout)
	for n < 1 && time.Since(startTime) < totalTimeout || b.lenRead > n && n < len(data) {
		n = b.lenRead
		time.Sleep(byteTimeout)
	}
	// 操作数据前上锁
	b.bufLock.Lock()
	defer b.bufLock.Unlock()
	// 读取数据
	n = copy(data, b.bufRead[0:n])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("MAC", b.address).Str("UUID", b.rxChan.UUID().String()).Str("data", hex.EncodeToString(data[0:n])).Msg("蓝牙接收到数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(b.bufRead, b.bufRead[n:b.lenRead])
	b.lenRead -= n
	return n, nil
}

// SetOnReceive 设置接收到数据后的回调函数
func (b *BleLink) SetOnReceive(callback func(data []byte, newoff int) int) {
	b.onGetFrame = callback
}

// addData 读取并处理数据
func (b *BleLink) addData(buf []byte) {
	nlen := b.lenRead
	n := copy(b.bufRead[nlen:], buf)
	// 未得到数据
	if n < 1 {
		return
	}
	nlen += n
	log.Debug().Str("MAC", b.address).Str("UUID", b.rxChan.UUID().String()).Str("data", hex.EncodeToString(b.bufRead[b.lenRead:nlen])).Msg("蓝牙接收到数据")

	// 处理数据前上锁
	b.bufLock.Lock()
	// 由回调函数处理数据
	if b.onGetFrame != nil {
		// 处理数据
		newoff := b.onGetFrame(b.bufRead[0:nlen], b.lenRead)
		for newoff > 0 {
			// 清除已处理的数据
			copy(b.bufRead, b.bufRead[newoff:nlen])
			nlen -= newoff
			b.lenRead = nlen
			// 已无数据可处理，退出循环
			if nlen < 1 {
				break
			}
			newoff = b.onGetFrame(b.bufRead[0:nlen], b.lenRead)
		}
	} else {
		// 未设置回调函数，直接将数据存入缓冲区
		b.lenRead = nlen
	}
	// 解锁
	b.bufLock.Unlock()
}
