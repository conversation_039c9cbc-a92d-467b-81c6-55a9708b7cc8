package communication

import (
	"encoding/hex"
	"errors"
	"net"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

type TCP struct {
	// 连接信息
	Host string
	Port int
	// 接收缓冲区大小
	ReceiveBufferSize int
	// 接收到信息后的回调，由回调来判断哪些数据已被处理
	// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
	// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
	// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
	onGetFrame func(data []byte, newoff int) int

	// 连接对象
	conn net.Conn
	// 数据读取缓冲区
	bufRead []byte
	// 已读取到缓冲区的数据长度
	lenRead int
	// 数据缓冲区锁
	bufLock sync.Mutex
	// 接收协程ID，用于判断当前协程是否最新创建
	receiveID int
}

// NewTCP 创建一个TCP连接
func NewTCP(host string, port int) *TCP {
	return &TCP{
		Host:              host,
		Port:              port,
		ReceiveBufferSize: 4096,
		onGetFrame:        nil,
		bufRead:           make([]byte, 40960),
	}
}

// Open 连接
func (t *TCP) Open() error {
	if t.conn != nil {
		return t.conn.Close()
	}
	// 随机产生接收协程ID
	t.receiveID = int(time.Now().UnixNano() % 1000000000)

	// 建立连接
	conn, err := net.Dial("tcp", t.Host+":"+strconv.Itoa(t.Port))
	if err != nil {
		return err
	}
	t.conn = conn
	// 调用接收协程
	go t.readAlways(t.receiveID)

	return nil
}

// Close 断开连接
func (t *TCP) Close() error {
	// 断开连接
	if t.conn == nil {
		err := t.conn.Close()
		t.conn = nil
		return err
	}
	return nil
}

// IsOpen 是否已连接
func (t *TCP) IsOpen() bool {
	return t.conn != nil && t.conn.RemoteAddr() != nil
}

// Write 发送数据
func (t *TCP) Write(data []byte) (int, error) {
	if t.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}
	// 发送数据
	log.Debug().Str("host", t.Host).Int("port", t.Port).Str("data", hex.EncodeToString(data)).Msg("TCP发送数据")
	return t.conn.Write(data)
}

// Read 不阻塞接收数据
func (t *TCP) ReadNoWait(data []byte) (int, error) {
	if t.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}
	// 操作数据前上锁
	t.bufLock.Lock()
	defer t.bufLock.Unlock()
	// 读取数据
	n := copy(data, t.bufRead[0:t.lenRead])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("host", t.Host).Int("port", t.Port).Str("data", hex.EncodeToString(data[0:n])).Msg("TCP接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(t.bufRead, t.bufRead[n:t.lenRead])
	t.lenRead -= n
	return n, nil
}

// Read 阻塞接收数据
func (t *TCP) Read(data []byte, totalTimeout time.Duration, byteTimeout time.Duration) (int, error) {
	if t.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}

	// 记录起始时间
	startTime := time.Now()
	// 记录当前数据长度
	n := t.lenRead
	time.Sleep(byteTimeout)
	for n < 1 && time.Since(startTime) < totalTimeout || t.lenRead > n && n < len(data) {
		n = t.lenRead
		time.Sleep(byteTimeout)
	}
	// 操作数据前上锁
	t.bufLock.Lock()
	defer t.bufLock.Unlock()
	// 读取数据
	n = copy(data, t.bufRead[0:n])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("host", t.Host).Int("port", t.Port).Str("data", hex.EncodeToString(data[0:n])).Msg("TCP接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(t.bufRead, t.bufRead[n:t.lenRead])
	t.lenRead -= n
	return n, nil
}

// SetOnReceive 设置接收到数据后的回调函数
func (t *TCP) SetOnReceive(callback func(data []byte, newoff int) int) {
	t.onGetFrame = callback
}

// readAlways 读取并处理数据，该函数会阻塞，直到关闭串口
func (t *TCP) readAlways(receiveID int) {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("host", t.Host).Int("port", t.Port).Str("Stack", string(debug.Stack())).Msgf("TCP.readAlways 程序运行异常: %v", r)
			t.Close()
		}
	}()

	for t.conn != nil && t.receiveID == receiveID {
		nlen := t.lenRead
		n := 0
		// 读取数据前上锁
		t.bufLock.Lock()
		// 读取数据
		n, _ = t.conn.Read(t.bufRead[nlen:])
		// 读取数据后，解锁
		t.bufLock.Unlock()
		// 未得到数据
		if n < 1 {
			time.Sleep(50 * time.Millisecond)
			continue
		}
		nlen += n
		log.Debug().Str("host", t.Host).Int("port", t.Port).Str("data", hex.EncodeToString(t.bufRead[t.lenRead:nlen])).Msg("TCP接收数据")

		// 处理数据前上锁
		t.bufLock.Lock()
		// 由回调函数处理数据
		if t.onGetFrame != nil {
			// 处理数据
			newoff := t.onGetFrame(t.bufRead[0:nlen], t.lenRead)
			for newoff > 0 {
				// 清除已处理的数据
				copy(t.bufRead, t.bufRead[newoff:nlen])
				nlen -= newoff
				t.lenRead = nlen
				// 已无数据可处理，退出循环
				if nlen < 1 {
					break
				}
				newoff = t.onGetFrame(t.bufRead[0:nlen], t.lenRead)
			}
		} else {
			// 未设置回调函数，直接将数据存入缓冲区
			t.lenRead = nlen
		}
		// 解锁
		t.bufLock.Unlock()
	}
}
