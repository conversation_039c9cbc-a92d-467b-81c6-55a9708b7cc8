package communication

import "time"

// Communication 通信接口
type Communication interface {
	// Open 连接
	Open() error
	// Close 断开连接
	Close() error
	// IsOpen 判断是否已连接
	IsOpen() bool

	// Write 发送数据
	Write(data []byte) (int, error)
	// ReadNoWait 不阻塞接收数据
	ReadNoWait(data []byte) (int, error)
	// Read 阻塞接收数据
	Read(data []byte, totalTimeout time.Duration, byteTimeout time.Duration) (int, error)

	// SetOnReceive 设置接收到信息后的回调函数，由回调来判断哪些数据已被处理
	// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
	// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
	// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
	SetOnReceive(callback func(data []byte, newoff int) int)
}
