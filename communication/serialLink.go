package communication

/**
 * @Author: ywj
 * @Date: 2024/04/26
 * @Desc: 串口通讯类
 * 打开串口后执行串口数据的监听，监听到指令格式的数据后，将通过回调返回
 */

import (
	"encoding/hex"
	"errors"
	"runtime/debug"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	"go.bug.st/serial"
)

type SerialLink struct {
	// 串口设备名称
	Name string
	// 串口波特率
	Baud int
	// 串口校验码
	Parity serial.Parity
	// 串口字节间读取超时时间
	ReadTimeout time.Duration
	// 接收到信息后的回调，由回调来判断哪些数据已被处理
	// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
	// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
	// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
	onGetFrame func(data []byte, newoff int) int

	// 初始化之后的串口句柄
	port serial.Port
	// 数据读取缓冲区
	bufRead []byte
	// 已读取到缓冲区的数据长度
	lenRead int
	// 数据缓冲区锁
	bufLock sync.Mutex
	// 接收协程ID，用于判断当前协程是否最新创建
	receiveID int
}

// NewSerialLink 创建串口对象
func NewSerialLink(name string) *SerialLink {
	return &SerialLink{
		Name:        name,
		Baud:        115200,
		Parity:      serial.EvenParity,
		ReadTimeout: 100 * time.Millisecond,
		onGetFrame:  nil,
		port:        nil,
	}
}

// Open 初始化串口
// 执行初始化并连接
func (s *SerialLink) Open() error {
	//设置串口参数
	c := &serial.Mode{
		BaudRate: s.Baud,
		Parity:   s.Parity,
	}

	// 随机产生接收协程ID
	s.receiveID = int(time.Now().UnixNano() % 1000000000)

	// 若已打开串口，先关闭
	if s.port != nil {
		s.Close()
	}
	// 未设置缓冲区大小，默认1024字节
	if s.bufRead == nil {
		s.bufRead = make([]byte, 1024)
	}

	//打开串口
	port, err := serial.Open(s.Name, c)
	if err == nil {
		s.port = port
		port.SetReadTimeout(s.ReadTimeout)
		// 串口打开成功后，调用接收协程
		go s.readAlways(s.receiveID)
	}
	return err
}

// Close 关闭串口
func (s *SerialLink) Close() error {
	if s.port != nil {
		err := s.port.Close()
		s.port = nil
		return err
	}
	return nil
}

// IsOpen 是否已打开
func (s *SerialLink) IsOpen() bool {
	return s.port != nil
}

// Write 向串口写入数据
func (s *SerialLink) Write(data []byte) (int, error) {
	if s.port == nil {
		return 0, errors.New("未开启串口通讯")
	}
	log.Debug().Str("port", s.Name).Str("data", hex.EncodeToString(data)).Msg("串口发送数据")
	return s.port.Write(data)
}

// Read 不阻塞接收数据
func (s *SerialLink) ReadNoWait(data []byte) (int, error) {
	if s.port == nil {
		return 0, errors.New("未开启串口通讯")
	}
	// 操作数据前上锁
	s.bufLock.Lock()
	defer s.bufLock.Unlock()
	// 读取数据
	n := copy(data, s.bufRead[0:s.lenRead])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("port", s.Name).Str("data", hex.EncodeToString(data[0:n])).Msg("串口接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(s.bufRead, s.bufRead[n:s.lenRead])
	s.lenRead -= n
	return n, nil
}

// Read 阻塞接收数据
func (s *SerialLink) Read(data []byte, totalTimeout time.Duration, byteTimeout time.Duration) (int, error) {
	if s.port == nil {
		return 0, errors.New("未开启串口通讯")
	}

	// 记录起始时间
	startTime := time.Now()
	// 记录当前数据长度
	n := s.lenRead
	time.Sleep(byteTimeout)
	for n < 1 && time.Since(startTime) < totalTimeout || s.lenRead > n && n < len(data) {
		n = s.lenRead
		time.Sleep(byteTimeout)
	}
	// 操作数据前上锁
	s.bufLock.Lock()
	defer s.bufLock.Unlock()
	// 读取数据
	n = copy(data, s.bufRead[0:n])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("port", s.Name).Str("data", hex.EncodeToString(data[0:n])).Msg("串口接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(s.bufRead, s.bufRead[n:s.lenRead])
	s.lenRead -= n
	return n, nil
}

// SetOnReceive 设置接收到数据后的回调函数
func (s *SerialLink) SetOnReceive(callback func(data []byte, newoff int) int) {
	s.onGetFrame = callback
}

// readAlways 读取并处理数据，该函数会阻塞，直到关闭串口
func (s *SerialLink) readAlways(receiveID int) {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("port", s.Name).Str("Stack", string(debug.Stack())).Msgf("SerialLink.readAlways 程序运行异常: %v", r)
			s.Close()
		}
	}()

	for s.port != nil && s.receiveID == receiveID {
		nlen := s.lenRead
		n := 0
		for {
			// 读取数据前上锁
			s.bufLock.Lock()
			// 读取数据
			n, _ = s.port.Read(s.bufRead[nlen:])
			// 读取数据后，解锁
			s.bufLock.Unlock()
			if n < 1 {
				break
			}
			nlen += n
		}
		// 未得到数据
		if nlen == s.lenRead {
			time.Sleep(50 * time.Millisecond)
			continue
		}
		log.Debug().Str("port", s.Name).Str("data", hex.EncodeToString(s.bufRead[s.lenRead:nlen])).Msg("串口接收数据")

		// 处理数据前上锁
		s.bufLock.Lock()
		// 由回调函数处理数据
		if s.onGetFrame != nil {
			// 处理数据
			newoff := s.onGetFrame(s.bufRead[0:nlen], s.lenRead)
			for newoff > 0 {
				// 清除已处理的数据
				copy(s.bufRead, s.bufRead[newoff:nlen])
				nlen -= newoff
				s.lenRead = nlen
				// 已无数据可处理，退出循环
				if nlen < 1 {
					break
				}
				newoff = s.onGetFrame(s.bufRead[0:nlen], s.lenRead)
			}
		} else {
			// 未设置回调函数，直接将数据存入缓冲区
			s.lenRead = nlen
		}
		// 解锁
		s.bufLock.Unlock()
	}
}
