package communication

import (
	"encoding/hex"
	"errors"
	"net"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

type UDP struct {
	// 连接信息
	Host string
	Port int
	// 接收缓冲区大小
	ReceiveBufferSize int
	// 接收到信息后的回调，由回调来判断哪些数据已被处理
	// data: 接收到的总数据，包含之前未处理的数据及新增加的数据
	// newoff: 新增数据在总数据中的偏移量，即从第几个字节开始，从0开始计数
	// 返回值：返回值为已处理数据总数，即需要清除的数据量，或未处理数据的起始位置，从0开始计数
	onGetFrame func(data []byte, newoff int) int

	// 连接对象
	conn net.Conn
	// 数据读取缓冲区
	bufRead []byte
	// 已读取到缓冲区的数据长度
	lenRead int
	// 数据缓冲区锁
	bufLock sync.Mutex
	// 接收协程ID，用于判断当前协程是否最新创建
	receiveID int
}

// NewUDP 创建一个 UDP 连接
func NewUDP(host string, port int) *UDP {
	return &UDP{
		Host:              host,
		Port:              port,
		ReceiveBufferSize: 4096,
		onGetFrame:        nil,
		bufRead:           make([]byte, 40960),
	}
}

// Open 连接
func (u *UDP) Open() error {
	if u.conn != nil {
		return u.conn.Close()
	}
	// 随机产生接收协程ID
	u.receiveID = int(time.Now().UnixNano() % 1000000000)

	// 建立连接
	conn, err := net.Dial("udp", u.Host+":"+strconv.Itoa(u.Port))
	if err != nil {
		return err
	}
	u.conn = conn
	// 调用接收协程
	go u.readAlways(u.receiveID)

	return nil
}

// Close 断开连接
func (u *UDP) Close() error {
	// 断开连接
	if u.conn == nil {
		err := u.conn.Close()
		u.conn = nil
		return err
	}
	return nil
}

// IsOpen 是否已连接
func (u *UDP) IsOpen() bool {
	return u.conn != nil && u.conn.RemoteAddr() != nil
}

// Write 发送数据
func (u *UDP) Write(data []byte) (int, error) {
	if u.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}
	// 发送数据
	log.Debug().Str("host", u.Host).Int("port", u.Port).Str("data", hex.EncodeToString(data)).Msg("UDP发送数据")
	return u.conn.Write(data)
}

// Read 不阻塞接收数据
func (u *UDP) ReadNoWait(data []byte) (int, error) {
	if u.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}
	// 操作数据前上锁
	u.bufLock.Lock()
	defer u.bufLock.Unlock()
	// 读取数据
	n := copy(data, u.bufRead[0:u.lenRead])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("host", u.Host).Int("port", u.Port).Str("data", hex.EncodeToString(data[0:n])).Msg("UDP接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(u.bufRead, u.bufRead[n:u.lenRead])
	u.lenRead -= n
	return n, nil
}

// Read 阻塞接收数据
func (u *UDP) Read(data []byte, totalTimeout time.Duration, byteTimeout time.Duration) (int, error) {
	if u.conn == nil {
		return 0, errors.New("未开启TCP通讯")
	}

	// 记录起始时间
	startTime := time.Now()
	// 记录当前数据长度
	n := u.lenRead
	time.Sleep(byteTimeout)
	for n < 1 && time.Since(startTime) < totalTimeout || u.lenRead > n && n < len(data) {
		n = u.lenRead
		time.Sleep(byteTimeout)
	}
	// 操作数据前上锁
	u.bufLock.Lock()
	defer u.bufLock.Unlock()
	// 读取数据
	n = copy(data, u.bufRead[0:n])
	// 若 n 不大于0，则接收超时
	if n < 1 {
		return 0, errors.New("接收超时")
	}
	log.Debug().Str("host", u.Host).Int("port", u.Port).Str("data", hex.EncodeToString(data[0:n])).Msg("UDP接收数据")
	// 读取数据后，将剩余数据移到缓冲区开头
	copy(u.bufRead, u.bufRead[n:u.lenRead])
	u.lenRead -= n
	return n, nil
}

// SetOnReceive 设置接收到数据后的回调函数
func (u *UDP) SetOnReceive(callback func(data []byte, newoff int) int) {
	u.onGetFrame = callback
}

// readAlways 读取并处理数据，该函数会阻塞，直到关闭串口
func (u *UDP) readAlways(receiveID int) {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Str("host", u.Host).Int("port", u.Port).Str("Stack", string(debug.Stack())).Msgf("UDP.readAlways 程序运行异常，异常：%v", r)
			u.Close()
		}
	}()

	for u.conn != nil && u.receiveID == receiveID {
		nlen := u.lenRead
		n := 0
		// 读取数据前上锁
		u.bufLock.Lock()
		// 读取数据
		n, _ = u.conn.Read(u.bufRead[nlen:])
		// 读取数据后，解锁
		u.bufLock.Unlock()
		// 未得到数据
		if n < 1 {
			time.Sleep(50 * time.Millisecond)
			continue
		}
		nlen += n
		log.Debug().Str("host", u.Host).Int("port", u.Port).Str("data", hex.EncodeToString(u.bufRead[u.lenRead:nlen])).Msg("UDP接收数据")

		// 处理数据前上锁
		u.bufLock.Lock()
		// 由回调函数处理数据
		if u.onGetFrame != nil {
			// 处理数据
			newoff := u.onGetFrame(u.bufRead[0:nlen], u.lenRead)
			for newoff > 0 {
				// 清除已处理的数据
				copy(u.bufRead, u.bufRead[newoff:nlen])
				nlen -= newoff
				u.lenRead = nlen
				// 已无数据可处理，退出循环
				if nlen < 1 {
					break
				}
				newoff = u.onGetFrame(u.bufRead[0:nlen], u.lenRead)
			}
		} else {
			// 未设置回调函数，直接将数据存入缓冲区
			u.lenRead = nlen
		}
		// 解锁
		u.bufLock.Unlock()
	}
}
