package base

import (
	"flag"
	"fmt"
	"os"
)

const (
	VERSION = "1.0"
)

// Args 命令行参数处理
type Args struct {
	// 配置文件名
	Config string
	// 调试模式
	Debug bool
	// 版本信息
	Version string
}

// Init 初始化参数
func (sels *Args) Init() bool {
	flag.StringVar(&sels.Config, "c", "config.cfg", "指定从配置文件加载参数")
	flag.BoolVar(&sels.Debug, "debug", false, "指定为调试模式，显示更多信息")

	flag.Usage = sels.usage
	flag.Parse()

	return flag.NArg() <= 0
}

func (a *Args) usage() {
	fmt.Fprint(os.Stderr, os.Args[0], "\nversion: ", a.Version, "\n用法: ", os.Args[0], " [-c <配置文件>] [-debug]\n")
	flag.PrintDefaults()
}
