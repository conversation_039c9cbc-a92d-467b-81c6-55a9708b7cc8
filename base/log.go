package base

import (
	"io"
	"os"
	"path"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gopkg.in/natefinch/lumberjack.v2"
)

var MainLog zerolog.Logger

type FilteredWriter struct {
	io.Writer
	Level zerolog.Level
}

func (w FilteredWriter) WriteLevel(level zerolog.Level, p []byte) (n int, err error) {
	if level >= w.Level {
		return w.Write(p)
	}
	return len(p), nil
}

// 日志初始化
func InitLog(logArgs LogArgs) {
	// 存在日志文件配置时，建立日志文件
	var logFile *FilteredWriter = nil
	if len(logArgs.FileDir) > 0 && IsDir(logArgs.FileDir) {
		saveLog := &lumberjack.Logger{
			Filename:   path.Join(logArgs.FileDir, "main.log"),
			MaxSize:    logArgs.FileSize,
			MaxAge:     logArgs.FileDays,
			MaxBackups: logArgs.FileCount,
			LocalTime:  true,
			Compress:   logArgs.FileCompress,
		}
		logFile = &FilteredWriter{
			Writer: zerolog.ConsoleWriter{
				Out:        saveLog,
				TimeFormat: "2006-01-02 15:04:05.000000",
				NoColor:    true,
			},
			Level: zerolog.Level(logArgs.FileLevel),
		}
	}

	// 配置日志
	zerolog.TimeFieldFormat = "2006-01-02 15:04:05.000000"
	zerolog.DurationFieldUnit = time.Second

	// 控制台日志
	logOut := FilteredWriter{
		Writer: zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: "01-02 15:04:05.000000",
			NoColor:    !logArgs.StdColor,
		},
		Level: zerolog.Level(logArgs.StdLevel),
	}

	if logFile == nil {
		log.Logger = log.Output(logOut)
	} else {
		log.Logger = log.Output(zerolog.MultiLevelWriter(logOut, logFile))
	}

	// 重定向标准错误输出到文件
	if len(logArgs.FileDir) > 0 && IsDir(logArgs.FileDir) {
		redirectStderr(path.Join(logArgs.FileDir, "error.log"))
	}
}

// IsDir 判断路径是否为目录
func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}
