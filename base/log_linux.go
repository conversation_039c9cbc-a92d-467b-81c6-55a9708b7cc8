package base

import (
	"os"
	"syscall"

	"github.com/rs/zerolog/log"
)

// redirectStderr 重定向标准错误输出到文件
func redirectStderr(stderrname string) error {
	logFile, err := os.OpenFile(stderrname, os.O_WRONLY|os.O_CREATE|os.O_SYNC|os.O_APPEND, 0644)
	if err != nil {
		log.Error().Msg("创建标准错误输出文件失败")
		return err
	}
	err = syscall.Dup3(int(logFile.Fd()), int(os.Stderr.Fd()), 0)
	if err != nil {
		log.Error().Msg("重定向标准错误输出失败")
		return err
	}
	return nil
}
