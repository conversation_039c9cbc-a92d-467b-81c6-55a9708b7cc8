package base

import (
	"os"

	"github.com/BurntSushi/toml"
)

// Config 主配置信息
type Config struct {
	// 服务监听地址
	ListenAddress string
	// 配置文件名，导入时不修改
	filename string `toml:"-"`
	// 工装名称
	DeviceName string
	// 写号逆变器配置文件路径
	InverterResource string
	// 测试校验参数
	Validator ValidatorArgs
	// 工装通讯连接参数
	Comm []CommArgs
	// 数据库专用参数
	DB DBArgs
	// 日志专用参数
	Log LogArgs
}

// ValidatorArgs 测试校验参数
type ValidatorArgs struct {
	// 软件版本号
	SoftVersion string
}

// CommArgs 工装通讯连接参数
type CommArgs struct {
	// 工位编号
	WorkstationID int
	// 记录当前的测试方案类型
	TestType string `toml:"-"`
	// 测试对象条码
	Barcode string `toml:"-"`
	// 板级测试上电触发的GPIO号
	PowerOnGPIO uint
	// 条码扫描头对应的串口号
	BarcodeScannerPort string
	// 数据交互模组‘信号引脚’对应的串口号
	SignalPinPort string
	// 数据交互模组 RS485 对应的串口号
	RS485Port string
}

// DBArgs 数据库参数
type DBArgs struct {
	// 数据库类型，目前支持 postgres, sqlite3
	Type string
	// 数据库连接串
	DSN string
}

// LogArgs 日志参数
type LogArgs struct {
	// 终端日志级别，0: DebugLevel，1: InfoLevel，2: WarnLevel，3: ErrorLevel，4: FatalLevel，5: PanicLevel，6: NoLevel，7: Disabled
	StdLevel int
	// 终端日志是否用彩色标示不同内容
	StdColor bool
	// 文件日志级别，0: DebugLevel，1: InfoLevel，2: WarnLevel，3: ErrorLevel，4: FatalLevel，5: PanicLevel，6: NoLevel，7: Disabled
	FileLevel int
	// 日志文件目录，不想保存到文件，则使用空字符串
	FileDir string
	// 用于日志文件分割，每个文件的最大大小，单位MB
	FileSize int
	// 日志文件保留个数，0表示无限个数
	FileCount int
	// 日志文件保存天数，0表示永久保存
	FileDays int
	// 是否压缩日志文件，压缩后文件名会带上.gz后缀
	FileCompress bool
}

// NewConfig 创建一个新的配置实例
func NewConfig(filename string) *Config {
	return &Config{
		filename: filename,
	}
}

// Load 从配置文件加载
func (e *Config) Load() {
	_, err := toml.DecodeFile(e.filename, &e)
	if err != nil {
		// 配置文件不存在，则使用默认配置
		e.ListenAddress = ":8080"
		e.DeviceName = "无名设备"
		e.Log.StdLevel = 1
		e.Log.StdColor = true
		e.Log.FileLevel = 0
		e.Log.FileDir = ""
		e.Log.FileSize = 100
		e.Log.FileCount = 0
		e.Log.FileDays = 30
		e.Log.FileCompress = true
	}
}

// Save 保存配置文件
func (e *Config) Save() error {
	// 创建文件
	file, err := os.Create(e.filename)
	if err != nil {
		return err
	}
	defer file.Close()
	// 写入文件
	return toml.NewEncoder(file).Encode(e)
}
